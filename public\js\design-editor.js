   // --- Setup ---
   const canvas = document.getElementById("demo");
   const canvasArea = document.getElementById("canvas-area");
   const ctx = canvas.getContext("2d", { alpha: true });
   const w = canvas.width; const h = canvas.height;

   // --- Artboard ---
   let artboard = null;
   let isArtboardEditMode = false;
   const toggleArtboardBtn = document.getElementById('toggleArtboardBtn');

   toggleArtboardBtn.addEventListener('click', () => {
       if (!artboard) {
           // Create centered artboard with default size
           artboard = {
               x: w / 2 - 300,
               y: h / 2 - 300,
               width: 600,
               height: 600,
               isSelected: true
           };
           // Enter artboard edit mode
           isArtboardEditMode = true;
           toggleArtboardBtn.textContent = 'Confirm';
           toggleArtboardBtn.style.backgroundColor = '#dcffcb';
           canvas.classList.add('artboard-edit-mode');

           // Deselect any selected object when entering artboard mode
           if (selectedObjectIndex !== -1) {
               canvasObjects[selectedObjectIndex].isSelected = false;
               selectedObjectIndex = -1;
               updateUIFromSelectedObject();
           }
       } else if (isArtboardEditMode) {
           // Exit artboard edit mode but keep artboard visible
           isArtboardEditMode = false;
           toggleArtboardBtn.textContent = 'Artboard';
           toggleArtboardBtn.style.backgroundColor = '';
           canvas.classList.remove('artboard-edit-mode');
       } else {
           // Toggle artboard visibility and enter edit mode
           artboard.isSelected = true;
           isArtboardEditMode = true;
           toggleArtboardBtn.textContent = 'Confirm';
           toggleArtboardBtn.style.backgroundColor = '#dcffcb';
           canvas.classList.add('artboard-edit-mode');

           // Deselect any selected object when entering artboard mode
           if (selectedObjectIndex !== -1) {
               canvasObjects[selectedObjectIndex].isSelected = false;
               selectedObjectIndex = -1;
               updateUIFromSelectedObject();
           }
       }
       update();
   });

   // --- Zoom/Pan State ---
   let scale = 1.0; let offsetX = 0; let offsetY = 0; const MIN_SCALE = 0.1; const MAX_SCALE = 10.0; const ZOOM_SENSITIVITY = 0.001; let isPanning = false; let panStartX, panStartY;

   // --- Artboard Resize State ---
   let isResizingArtboard = false;
   let resizeCorner = null;

   canvas.addEventListener('mousedown', (e) => {
       const coords = getCanvasCoordinates(e);
       const world = canvasToWorld(coords.x, coords.y);

       // Check for artboard corners when artboard is selected AND in edit mode
       if (artboard && artboard.isSelected && isArtboardEditMode) {
           const size = 8 / scale;
           const half = size / 2;
           const corners = [
               ['tl', artboard.x, artboard.y],
               ['tr', artboard.x + artboard.width, artboard.y],
               ['bl', artboard.x, artboard.y + artboard.height],
               ['br', artboard.x + artboard.width, artboard.y + artboard.height]
           ];
           for (const [cornerName, cx, cy] of corners) {
               if (
                   world.x >= cx - half &&
                   world.x <= cx + half &&
                   world.y >= cy - half &&
                   world.y <= cy + half
               ) {
                   isResizingArtboard = true;
                   resizeCorner = cornerName;
                   e.preventDefault();
                   return;
               }
           }

           // If in artboard edit mode, prevent other interactions
           if (isArtboardEditMode) {
               e.preventDefault();
               return;
           }
       }
   });

   canvas.addEventListener('mousemove', (e) => {
       if (!isResizingArtboard) return;
       const coords = getCanvasCoordinates(e);
       const world = canvasToWorld(coords.x, coords.y);

       // Make sure we're in artboard edit mode when resizing
       if (!isArtboardEditMode) {
           isArtboardEditMode = true;
           toggleArtboardBtn.textContent = 'Confirm';
           toggleArtboardBtn.style.backgroundColor = '#dcffcb';
           canvas.classList.add('artboard-edit-mode');
       }

       switch (resizeCorner) {
           case 'tl':
               artboard.width += artboard.x - world.x;
               artboard.height += artboard.y - world.y;
               artboard.x = world.x;
               artboard.y = world.y;
               break;
           case 'tr':
               artboard.width = world.x - artboard.x;
               artboard.height += artboard.y - world.y;
               artboard.y = world.y;
               break;
           case 'bl':
               artboard.width += artboard.x - world.x;
               artboard.x = world.x;
               artboard.height = world.y - artboard.y;
               break;
           case 'br':
               artboard.width = world.x - artboard.x;
               artboard.height = world.y - artboard.y;
               break;
       }
       update();
   });

   window.addEventListener('mouseup', () => {
       isResizingArtboard = false;
       resizeCorner = null;
   });

   // --- Offscreen Canvases (Text Effects) ---
   const os = document.createElement("canvas"); os.width = 4096; os.height = 4096; const octx = os.getContext("2d"); const tempWarpCanvas = document.createElement("canvas"); tempWarpCanvas.width = 4096; tempWarpCanvas.height = 4096; const tempWarpCtx = tempWarpCanvas.getContext("2d"); const letterCanvas = document.createElement("canvas"); letterCanvas.width = 2048; letterCanvas.height = 2048; const letterCtx = letterCanvas.getContext("2d");

   // --- Controls References ---
   const textControlsWrapper = document.getElementById('text-controls'); const iText = document.getElementById("iText"); const addEditTextBtn = document.getElementById("addEditTextBtn"); const deleteTextBtn = document.getElementById("deleteTextBtn"); const iTextColor = document.getElementById("iTextColor"); const iFontFamily = document.getElementById("iFontFamily"); const iBold = document.getElementById("iBold"); const iItalic = document.getElementById("iItalic"); const iFontSize = document.getElementById("iFontSize"); const iTextRotation = document.getElementById("iTextRotation"); const vFontSize = document.getElementById("vFontSize"); const vTextRotation = document.getElementById("vTextRotation"); const effectModeSelect = document.getElementById("effectMode"); const skewSlider = document.getElementById("skewSlider"); const skewYSlider = document.getElementById("skewYSlider"); const vSkew = document.getElementById("vSkew"); const vSkewY = document.getElementById("vSkewY"); const iCurve = document.getElementById("iCurve"); const iOffset = document.getElementById("iOffset"); const iHeight = document.getElementById("iHeight"); const iBottom = document.getElementById("iBottom"); const iTriangle = document.getElementById("iTriangle"); const iShiftCenter = document.getElementById("iShiftCenter"); const vCurve = document.getElementById("vCurve"); const vOffset = document.getElementById("vOffset"); const vHeight = document.getElementById("vHeight"); const vBottom = document.getElementById("vBottom"); const vShiftCenter = document.getElementById("vShiftCenter"); const iDiameter = document.getElementById("iDiameter"); const iKerning = document.getElementById("iKerning"); const iFlip = document.getElementById("iFlip"); const vDiameter = document.getElementById("vDiameter"); const vKerning = document.getElementById("vKerning"); const iCurveAmount = document.getElementById("iCurveAmount"); const iCurveKerning = document.getElementById("iCurveKerning"); const iCurveFlip = document.getElementById("iCurveFlip"); const vCurveAmount = document.getElementById("vCurveAmount"); const vCurveKerning = document.getElementById("vCurveKerning"); const shadowSelect = document.getElementById("shadow"); const shadowColorPicker = document.getElementById("shadowColor"); const shadowOffsetXSlider = document.getElementById("shadowOffsetX"); const shadowOffsetYSlider = document.getElementById("shadowOffsetY"); const shadowBlurSlider = document.getElementById("shadowBlur"); const vShadowOffsetX = document.getElementById("vShadowOffsetX"); const vShadowOffsetY = document.getElementById("vShadowOffsetY"); const vShadowBlur = document.getElementById("vShadowBlur"); const blockShadowColorPicker = document.getElementById("blockShadowColor"); const blockShadowOpacitySlider = document.getElementById("blockShadowOpacity"); const blockShadowOffsetSlider = document.getElementById("blockShadowOffset"); const blockShadowAngleSlider = document.getElementById("blockShadowAngle"); const blockShadowBlurSlider = document.getElementById("blockShadowBlur"); const blockShadowPerspective = document.getElementById("blockShadowPerspective"); const blockShadowPerspectiveIntensity = document.getElementById("blockShadowPerspectiveIntensity"); const vBlockShadowOpacity = document.getElementById("vBlockShadowOpacity"); const vBlockShadowOffset = document.getElementById("vBlockShadowOffset"); const vBlockShadowAngle = document.getElementById("vBlockShadowAngle"); const vBlockShadowBlur = document.getElementById("vBlockShadowBlur"); const vBlockShadowPerspectiveIntensity = document.getElementById("vBlockShadowPerspectiveIntensity"); const lineShadowColorPicker = document.getElementById("lineShadowColor"); const lineShadowDistanceSlider = document.getElementById("lineShadowDistance"); const lineShadowAngleSlider = document.getElementById("lineShadowAngle"); const lineShadowThicknessSlider = document.getElementById("lineShadowThickness"); const vLineShadowDistance = document.getElementById("vLineShadowDistance"); const vLineShadowAngle = document.getElementById("vLineShadowAngle"); const vLineShadowThickness = document.getElementById("vLineShadowThickness"); const detailed3DPrimaryColorPicker = document.getElementById("detailed3DPrimaryColor"); const detailed3DPrimaryOpacitySlider = document.getElementById("detailed3DPrimaryOpacity"); const detailed3DOffsetSlider = document.getElementById("detailed3DOffset"); const detailed3DAngleSlider = document.getElementById("detailed3DAngle"); const detailed3DBlurSlider = document.getElementById("detailed3DBlur"); const detailed3DSecondaryColorPicker = document.getElementById("detailed3DSecondaryColor"); const detailed3DSecondaryOpacitySlider = document.getElementById("detailed3DSecondaryOpacity"); const detailed3DSecondaryWidthSlider = document.getElementById("detailed3DSecondaryWidth"); const detailed3DSecondaryOffsetXSlider = document.getElementById("detailed3DSecondaryOffsetX"); const detailed3DSecondaryOffsetYSlider = document.getElementById("detailed3DSecondaryOffsetY"); const vDetailed3DPrimaryOpacity = document.getElementById("vDetailed3DPrimaryOpacity"); const vDetailed3DOffset = document.getElementById("vDetailed3DOffset"); const vDetailed3DAngle = document.getElementById("vDetailed3DAngle"); const vDetailed3DBlur = document.getElementById("vDetailed3DBlur"); const vDetailed3DSecondaryOpacity = document.getElementById("vDetailed3DSecondaryOpacity"); const vDetailed3DSecondaryWidth = document.getElementById("vDetailed3DSecondaryWidth"); const vDetailed3DSecondaryOffsetX = document.getElementById("vDetailed3DSecondaryOffsetX"); const vDetailed3DSecondaryOffsetY = document.getElementById("vDetailed3DSecondaryOffsetY"); const strokeToggle = document.getElementById("strokeToggle"); const linesDecorationSelect = document.getElementById("linesDecoration"); const strokeWidthSlider = document.getElementById("strokeWidth"); const strokeColorPicker = document.getElementById("strokeColor"); const strokeOpacitySlider = document.getElementById("strokeOpacity"); const vStrokeWidth = document.getElementById("vStrokeWidth"); const vStrokeOpacity = document.getElementById("vStrokeOpacity"); const hWeight = document.getElementById("hWeight"); const hDistance = document.getElementById("hDistance"); const hColor = document.getElementById("hColor"); const vHWeight = document.getElementById("vHWeight"); const vHDistance = document.getElementById("vHDistance"); const ccDistance = document.getElementById("ccDistance"); const ccColor = document.getElementById("ccColor"); const ccFillTop = document.getElementById("ccFillTop"); const ccFillBottom = document.getElementById("ccFillBottom"); const vCcDistance = document.getElementById("vCcDistance"); const oWeight = document.getElementById("oWeight"); const oDistance = document.getElementById("oDistance"); const oColor = document.getElementById("oColor"); const vOWeight = document.getElementById("vOWeight"); const vODistance = document.getElementById("vODistance"); const flcDistance = document.getElementById("flcDistance"); const flcFillTop = document.getElementById("flcFillTop"); const flcFillBottom = document.getElementById("flcFillBottom"); const flcColor = document.getElementById("flcColor"); const flcMaxWeight = document.getElementById("flcMaxWeight"); const flcSpacing = document.getElementById("flcSpacing"); const vFlcDistance = document.getElementById("vFlcDistance"); const vFlcMaxWeight = document.getElementById("vFlcMaxWeight"); const vFlcSpacing = document.getElementById("vFlcSpacing");
   const addImageBtn = document.getElementById('addImageBtn'); const imageFileInput = document.getElementById('image-file-input'); const deleteImageBtn = document.getElementById('deleteImageBtn'); const imageControlsWrapper = document.getElementById('image-controls'); const noImageSelectedMsg = document.getElementById('no-image-selected-msg'); const iImageSize = document.getElementById('iImageSize'); const vImageSize = document.getElementById('vImageSize'); const iImageRotation = document.getElementById('iImageRotation'); const vImageRotation = document.getElementById('vImageRotation');
   // Image opacity controls
   const iImageOpacity = document.getElementById('iImageOpacity'); const vImageOpacity = document.getElementById('vImageOpacity');
   // Image stroke controls
   const iImageStroke = document.getElementById('iImageStroke'); const iImageStrokeWidth = document.getElementById('iImageStrokeWidth'); const vImageStrokeWidth = document.getElementById('vImageStrokeWidth'); const iImageStrokeColor = document.getElementById('iImageStrokeColor'); const iImageStrokeOpacity = document.getElementById('iImageStrokeOpacity'); const vImageStrokeOpacity = document.getElementById('vImageStrokeOpacity');
   // Image shadow controls
   const iImageShadow = document.getElementById('iImageShadow'); const iImageShadowColor = document.getElementById('iImageShadowColor'); const iImageShadowOpacity = document.getElementById('iImageShadowOpacity'); const vImageShadowOpacity = document.getElementById('vImageShadowOpacity'); const iImageShadowOffsetX = document.getElementById('iImageShadowOffsetX'); const vImageShadowOffsetX = document.getElementById('vImageShadowOffsetX'); const iImageShadowOffsetY = document.getElementById('iImageShadowOffsetY'); const vImageShadowOffsetY = document.getElementById('vImageShadowOffsetY'); const iImageShadowBlur = document.getElementById('iImageShadowBlur'); const vImageShadowBlur = document.getElementById('vImageShadowBlur');
   const zoomInBtn = document.getElementById('zoomInBtn'); const zoomOutBtn = document.getElementById('zoomOutBtn'); const zoomLevelSpan = document.getElementById('zoomLevel');
   const moveForwardBtn = document.getElementById('moveForwardBtn'); const moveBackwardBtn = document.getElementById('moveBackwardBtn'); // Ensure references are declared

   // --- State & Constants ---
   let canvasObjects = []; let selectedObjectIndex = -1; let nextId = 0; let isDraggingObject = false; let dragStartX, dragStartY; let dragInitialObjectX, dragInitialObjectY; let dragInitialControlPoints = null; // <-- ADDED for mesh drag fix
   const selectionBoxPadding = 5; const letterSourcePadding = 50; // Increased padding for text effects
   let canvasBackgroundColor = '#ffffff'; // Initial background color

   // --- Expose variables to global scope for external scripts ---
   window.canvasObjects = canvasObjects;
   window.selectedObjectIndex = selectedObjectIndex;
   window.w = w;
   window.h = h;
   window.canvas = canvas;
   window.ctx = ctx;
   window.update = update;
   window.updateUIFromSelectedObject = updateUIFromSelectedObject;
   window.createImageObject = createImageObject;

   // Function to sync global references when local variables change
   function syncGlobalReferences() {
       window.canvasObjects = canvasObjects;
       window.selectedObjectIndex = selectedObjectIndex;
   }

   // --- Object Factories ---
   function createTextObject(options = {}) {
       const defaults = {
           id: nextId++,
           type: 'text',
           text: "TEXT",
           x: w / 2,
           y: h / 2,
           color: "#3b82f6",
           gradient: null, // For gradient colors
           fontFamily: "Poppins",
           fontSize: 150,
           bold: true,
           italic: false,
           rotation: 0,
           letterSpacing: 0, // Default letter spacing (0 = normal)
           opacity: 100, // Default opacity (100% = fully opaque)
           isSelected: false,
           effectMode: 'normal',
           decorationMode: 'noDecoration',
           strokeMode: 'noStroke',
           strokeOpacity: 100, // Independent stroke opacity for text
           shadowMode: 'noShadow', // Always start with no shadow effect
           skewX: 0,
           skewY: 0,
           warpCurve: 100,
           warpOffset: 10,
           warpHeight: 100,
           warpBottom: 150,
           warpTriangle: false,
           warpShiftCenter: 100,
           circleDiameter: 600,
           circleKerning: 0,
           circleFlip: false,
           curveAmount: 40,
           curveKerning: 0,
           curveFlip: false,
           gridDistortCols: 2,
           gridDistortRows: 1,
           gridDistortPadding: 120,
           gridDistortIntensity: 100,

           // Default values for perspective shadow
           perspectiveShadowColor: '#333333',
           perspectiveShadowOpacity: 100,
           perspectiveShadowOffset: 6,
           perspectiveShadowAngle: 105,
           perspectiveShadowBlur: 2,
           perspectiveShadowIntensity: 16,
           gridDistortVerticalOnly: false,
           hLineWeight: 3,
           hLineDist: 7,
           hLineColor: "#0000FF",
           ccDist: 50,
           ccColor: "#00FF00",
           ccFillDir: "top",
           oLineWeight: 4,
           oLineDist: 3,
           oLineColor: "#0000FF",
           flcDist: 62,
           flcDir: 'top',
           flcColor: '#cccccc',
           flcWeight: 3,
           flcSpacing: 10,
           strokeWidth: 1,
           strokeColor: '#000000',
           shadowColor: '#000000',
           shadowOffsetX: 5,
           shadowOffsetY: 5,
           shadowBlur: 10,
           blockShadowColor: '#000000',
           blockShadowOpacity: 100,
           blockShadowOffset: 40,
           blockShadowAngle: -58,
           blockShadowBlur: 5,
           blockShadowPerspective: false,
           blockShadowPerspectiveIntensity: 50,

           perspectiveShadowOutlineColor: '#d1d5db',
           perspectiveShadowOutlineOpacity: 100,
           perspectiveShadowOutlineWidth: 3,
           perspectiveShadowOutlineOffsetX: 2,
           perspectiveShadowOutlineOffsetY: -3,
           lineShadowColor: '#AAAAAA',
           lineShadowDist: 15,
           lineShadowAngle: -45,
           lineShadowThickness: 5,
           d3dPrimaryColor: '#000000',
           d3dPrimaryOpacity: 100,
           d3dOffset: 36,
           d3dAngle: -63,
           d3dBlur: 5,
           d3dSecondaryColor: '#00FF00',
           d3dSecondaryOpacity: 100,
           d3dSecondaryWidth: 4, // Changed from 0 to 4 to enable front outline by default
           d3dSecondaryOffsetX: -5,
           d3dSecondaryOffsetY: -5,
           // Initialize gridDistort object
           gridDistort: {
               gridCols: 3,
               gridRows: 2,
               gridPadding: 120,
               intensity: 1.0,
               showGrid: false, // Always start with grid hidden
               lastText: "TEXT", // Initialize with the default text
               controlPoints: [], // Will be populated by initializeGridPoints only when needed
               relativeControlPoints: [] // Will be populated by storeRelativeControlPoints
           },
           // Initialize mesh warp object
           meshWarp: {
               controlPoints: [], // Current control point positions
               initialControlPoints: [], // Original control point positions
               relativeControlPoints: [], // Relative positions for preserving distortion
               hasCustomDistortion: false, // Track if user has customized the distortion
               showGrid: true, // Grid visibility
               gridRect: null, // Grid bounds
               initialized: false // Whether mesh has been initialized
           }
       };

       const textObj = { ...defaults, ...options };

       // If the text was changed in options, update the lastText
       if (options.text) {
           textObj.gridDistort.lastText = options.text;
       }

       return textObj;
   }
   function createImageObject(imgElement, options = {}) {
       const defaults = {
           id: nextId++,
           type: 'image',
           image: imgElement,
           x: w / 2,
           y: h / 2,
           scale: 1.0,
           rotation: 0,
           isSelected: false,
           originalWidth: imgElement.naturalWidth,
           originalHeight: imgElement.naturalHeight,
           imageUrl: options.imageUrl || imgElement.src,
           generationId: null,
           isFromGeneration: false,
           backgroundRemoved: false,
           // Shadow properties
           shadowMode: 'none',
           shadowColor: '#000000',
           shadowOffsetX: 5,
           shadowOffsetY: 5,
           shadowBlur: 10,
           shadowOpacity: 100,
           // Stroke properties
           strokeMode: 'none',
           strokeWidth: 3,
           strokeColor: '#000000',
           strokeOpacity: 100, // Independent stroke opacity
           // Image opacity
           opacity: 100
       };
       return { ...defaults, ...options };
   }

   // --- Helpers ---
   function hexToRgba(hex, alpha = 1) { let r = 0, g = 0, b = 0; if (hex.length === 4) { r = parseInt(hex[1] + hex[1], 16); g = parseInt(hex[2] + hex[2], 16); b = parseInt(hex[3] + hex[3], 16); } else if (hex.length === 7) { r = parseInt(hex[1] + hex[2], 16); g = parseInt(hex[3] + hex[4], 16); b = parseInt(hex[5] + hex[6], 16); } if (isNaN(r) || isNaN(g) || isNaN(b)) { console.warn(`Invalid hex: ${hex}`); return 'rgba(0,0,0,0)'; } return `rgba(${r},${g},${b},${alpha})`; }
   function calculateOffset(distance, angleDegrees) { const angleRadians = angleDegrees * (Math.PI / 180); return { x: distance * Math.cos(angleRadians), y: distance * Math.sin(angleRadians) }; }

   // --- Coordinate Conversion ---
   function getCanvasCoordinates(event) { const rect = canvas.getBoundingClientRect(); let clientX, clientY; if (event.touches && event.touches.length > 0) { clientX = event.touches[0].clientX; clientY = event.touches[0].clientY; } else { clientX = event.clientX; clientY = event.clientY; } return { x: clientX - rect.left, y: clientY - rect.top }; }
   function canvasToWorld(canvasX, canvasY) { return { x: (canvasX - offsetX) / scale, y: (canvasY - offsetY) / scale }; }

   // --- Simple Gradient Creation ---
   function createSimpleGradient(targetCtx, textObj, letterIndex = null, totalLetters = null) {
       if (!textObj.gradient || textObj.gradient.type === 'solid') {
           return null;
       }

       console.log('🎨 Creating simple gradient for text:', textObj.text, 'letterIndex:', letterIndex, 'totalLetters:', totalLetters);

       // Determine if we're rendering a single letter for distort effects
       const isRenderingSingleLetter = (letterIndex !== null && totalLetters !== null && totalLetters > 1);

       let textWidth, textHeight;

       if (isRenderingSingleLetter) {
           // For single letter rendering, we need to calculate the gradient based on the full text
           // but position it correctly for this specific letter
           const fullTextMetrics = targetCtx.measureText(textObj.originalText || textObj.text);
           const fullTextWidth = fullTextMetrics.width;
           textWidth = fullTextWidth;
           textHeight = textObj.fontSize;
           console.log('🎨 Single letter gradient - Full text dimensions:', { textWidth, textHeight });
       } else {
           // For normal rendering, use the actual text being rendered
           const textMetrics = targetCtx.measureText(textObj.text);
           textWidth = textMetrics.width;
           textHeight = textObj.fontSize;
           console.log('🎨 Normal gradient - Text dimensions:', { textWidth, textHeight });
       }

       let gradient;

       if (textObj.gradient.type === 'linear') {
           // Extract angle from gradient value (e.g., "linear-gradient(125deg, ...)")
           let angle = 0; // Default to horizontal
           if (textObj.gradient.value) {
               const angleMatch = textObj.gradient.value.match(/linear-gradient\((\d+)deg/);
               if (angleMatch) {
                   angle = parseInt(angleMatch[1]);
               }
           }

           // Convert angle to radians and calculate direction vector
           // CSS gradients: 0deg = top to bottom, 90deg = left to right
           // Canvas gradients: 0deg = left to right, 90deg = top to bottom
           // So we need to adjust by 90 degrees to match CSS convention
           const adjustedAngle = angle + 90;
           const angleRad = (adjustedAngle * Math.PI) / 180;
           const cos = Math.cos(angleRad);
           const sin = Math.sin(angleRad);

           if (isRenderingSingleLetter) {
               // For single letters, create a gradient that spans the letter width with correct angle
               const letterWidth = targetCtx.measureText(textObj.text).width;
               const letterHeight = textHeight;

               // Calculate gradient line length based on letter dimensions and angle
               const diagonal = Math.sqrt(letterWidth * letterWidth + letterHeight * letterHeight);
               const halfLength = diagonal / 2;

               // Calculate gradient endpoints
               const x1 = -halfLength * cos;
               const y1 = -halfLength * sin;
               const x2 = halfLength * cos;
               const y2 = halfLength * sin;

               gradient = targetCtx.createLinearGradient(x1, y1, x2, y2);
               console.log('🎨 Created single letter linear gradient:', {
                   x1, y1, x2, y2, letterWidth, letterIndex, totalLetters, angle, angleRad
               });
           } else {
               // Normal linear gradient for full text with correct angle
               const diagonal = Math.sqrt(textWidth * textWidth + textHeight * textHeight);
               const halfLength = diagonal / 2;

               // Calculate gradient endpoints
               const x1 = -halfLength * cos;
               const y1 = -halfLength * sin;
               const x2 = halfLength * cos;
               const y2 = halfLength * sin;

               gradient = targetCtx.createLinearGradient(x1, y1, x2, y2);
               console.log('🎨 Created normal linear gradient:', { x1, y1, x2, y2, angle, angleRad });
           }

       } else if (textObj.gradient.type === 'radial') {
           const centerX = 0;
           const centerY = 0;
           const radius = Math.max(textWidth, textHeight) / 2;

           gradient = targetCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
           console.log('🎨 Created radial gradient:', { centerX, centerY, radius });
       }

       // Add color stops
       if (gradient && textObj.gradient.gradient.colors) {
           if (isRenderingSingleLetter && letterIndex !== null && totalLetters !== null) {
               // For single letters, calculate which portion of the gradient this letter should show
               const letterSpacing = textObj._effectiveLetterSpacing || 0;

               // Calculate the position of this letter within the full text
               let letterStartX = -textWidth / 2;
               for (let i = 0; i < letterIndex; i++) {
                   const prevLetter = (textObj.originalText || textObj.text)[i];
                   letterStartX += targetCtx.measureText(prevLetter).width + letterSpacing;
               }

               const letterWidth = targetCtx.measureText(textObj.text).width;
               const letterEndX = letterStartX + letterWidth;

               // Convert letter position to percentage of full text width
               const letterStartPercent = ((letterStartX + textWidth / 2) / textWidth) * 100;
               const letterEndPercent = ((letterEndX + textWidth / 2) / textWidth) * 100;

               console.log('🎨 Letter gradient mapping:', {
                   letterIndex, letterStartPercent, letterEndPercent,
                   letterStartX, letterEndX, textWidth, letterWidth
               });

               // Create color stops that represent only this letter's portion of the gradient
               const relevantStops = [];

               // Add color stops that fall within this letter's range
               textObj.gradient.gradient.colors.forEach(colorStop => {
                   const stopPercent = colorStop.position;

                   if (stopPercent >= letterStartPercent && stopPercent <= letterEndPercent) {
                       // This color stop falls within the letter's range
                       const relativePosition = (stopPercent - letterStartPercent) / (letterEndPercent - letterStartPercent);
                       relevantStops.push({
                           position: relativePosition,
                           color: colorStop.color
                       });
                   }
               });

               // If no stops fall within the range, interpolate the color
               if (relevantStops.length === 0) {
                   // Find the color at the letter's center
                   const letterCenterPercent = (letterStartPercent + letterEndPercent) / 2;
                   const interpolatedColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterCenterPercent);
                   relevantStops.push({
                       position: 0,
                       color: interpolatedColor
                   });
                   relevantStops.push({
                       position: 1,
                       color: interpolatedColor
                   });
               } else {
                   // Add boundary colors if needed
                   if (relevantStops[0].position > 0) {
                       const startColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterStartPercent);
                       relevantStops.unshift({
                           position: 0,
                           color: startColor
                       });
                   }
                   if (relevantStops[relevantStops.length - 1].position < 1) {
                       const endColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterEndPercent);
                       relevantStops.push({
                           position: 1,
                           color: endColor
                       });
                   }
               }

               // Apply the calculated color stops
               relevantStops.forEach(stop => {
                   gradient.addColorStop(stop.position, stop.color);
                   console.log('🎨 Added letter color stop:', stop);
               });

           } else {
               // Normal gradient for full text
               textObj.gradient.gradient.colors.forEach(colorStop => {
                   gradient.addColorStop(colorStop.position / 100, colorStop.color);
                   console.log('🎨 Added color stop:', colorStop);
               });
           }
       }

       return gradient;
   }

   // Helper function to interpolate color at a specific position in a gradient
   function interpolateGradientColor(colorStops, targetPercent) {
       // Sort color stops by position
       const sortedStops = [...colorStops].sort((a, b) => a.position - b.position);

       // If target is before first stop, return first color
       if (targetPercent <= sortedStops[0].position) {
           return sortedStops[0].color;
       }

       // If target is after last stop, return last color
       if (targetPercent >= sortedStops[sortedStops.length - 1].position) {
           return sortedStops[sortedStops.length - 1].color;
       }

       // Find the two stops that bracket our target position
       for (let i = 0; i < sortedStops.length - 1; i++) {
           const stop1 = sortedStops[i];
           const stop2 = sortedStops[i + 1];

           if (targetPercent >= stop1.position && targetPercent <= stop2.position) {
               // Interpolate between these two colors
               const ratio = (targetPercent - stop1.position) / (stop2.position - stop1.position);
               return interpolateColors(stop1.color, stop2.color, ratio);
           }
       }

       // Fallback to first color
       return sortedStops[0].color;
   }

   // Helper function to interpolate between two hex colors
   function interpolateColors(color1, color2, ratio) {
       // Convert hex to RGB
       const rgb1 = hexToRgb(color1);
       const rgb2 = hexToRgb(color2);

       if (!rgb1 || !rgb2) return color1; // Fallback if conversion fails

       // Interpolate each channel
       const r = Math.round(rgb1.r + (rgb2.r - rgb1.r) * ratio);
       const g = Math.round(rgb1.g + (rgb2.g - rgb1.g) * ratio);
       const b = Math.round(rgb1.b + (rgb2.b - rgb1.b) * ratio);

       // Convert back to hex
       return rgbToHex(r, g, b);
   }

   // Helper function to convert hex to RGB
   function hexToRgb(hex) {
       const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
       return result ? {
           r: parseInt(result[1], 16),
           g: parseInt(result[2], 16),
           b: parseInt(result[3], 16)
       } : null;
   }

   // Helper function to convert RGB to hex
   function rgbToHex(r, g, b) {
       return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
   }

   // --- Font and Bounds ---
   function setTextContextOn(targetCtx, textObj, letterIndex = null, totalLetters = null) {
       // Make sure we have valid style properties
       const fontStyle = textObj.italic ? "italic" : "normal";
       const fontWeight = textObj.bold ? "bold" : "normal";

       // Set the font with proper style and weight
       targetCtx.font = `${fontStyle} ${fontWeight} ${textObj.fontSize}px "${textObj.fontFamily}"`;

       // Handle gradient or solid color
       console.log('🎨 setTextContextOn - textObj.gradient:', textObj.gradient);
       console.log('🎨 setTextContextOn - textObj.color:', textObj.color);

       // Only set solid color for actual distort effects that render letter-by-letter
       // Normal text and skew should use the normal gradient system
       const isLetterByLetterEffect = (letterIndex !== null && letterIndex !== undefined);
       if (isLetterByLetterEffect) {
           // For letter-by-letter rendering, use solid color (gradient is handled by normal system)
           targetCtx.fillStyle = textObj.color;
           console.log('🎨 Set fillStyle to solid color for letter-by-letter rendering:', textObj.color);
       } else {
           // For normal text, don't interfere with gradient system
           targetCtx.fillStyle = textObj.color; // Default, will be overridden by gradient system if needed
           console.log('🎨 Set default fillStyle for normal text:', textObj.color);
       }

       targetCtx.textAlign = "center";
       targetCtx.textBaseline = "middle";

       // Apply letter spacing if specified
       // Note: letterSpacing is not directly supported in Canvas, but we'll store it
       // for use in our custom text rendering functions
       textObj._effectiveLetterSpacing = (textObj.letterSpacing !== undefined) ? textObj.letterSpacing : 0;

       // Note: Opacity is handled individually for text and stroke in rendering functions
       // Do not apply global opacity here as it would affect stroke independence

       // Log the font style for debugging
       console.log('Setting text context with font:', targetCtx.font,
                  'letter spacing:', textObj._effectiveLetterSpacing);
   }
   function calculateObjectBounds(obj) {
       if (!obj) return { x: 0, y: 0, width: 0, height: 0, cx: 0, cy: 0 };

       if (obj.type === 'text') {
           if (!obj.text) return { x: 0, y: 0, width: 0, height: 0, cx: obj.x, cy: obj.y };

           const tempCtx = document.createElement('canvas').getContext('2d');
           setTextContextOn(tempCtx, obj);

           // Check if letter spacing is applied
           const letterSpacing = obj._effectiveLetterSpacing || 0;
           let width;

           if (letterSpacing === 0) {
               // Standard text measurement
               const metrics = tempCtx.measureText(obj.text.toUpperCase());
               width = metrics.width;
           } else {
               // Calculate width with letter spacing
               const letters = obj.text.toUpperCase().split('');
               let totalWidth = 0;

               // Sum the width of each letter
               letters.forEach(letter => {
                   totalWidth += tempCtx.measureText(letter).width;
               });

               // Add letter spacing between characters
               if (letters.length > 1) {
                   totalWidth += letterSpacing * (letters.length - 1);
               }

               width = totalWidth;
           }

           const metrics = tempCtx.measureText(obj.text.toUpperCase());
           const ascent = metrics.actualBoundingBoxAscent || obj.fontSize * 0.8;
           const descent = metrics.actualBoundingBoxDescent || obj.fontSize * 0.2;
           const height = ascent + descent;

           return {
               x: obj.x - width / 2,
               y: obj.y - height / 2,
               width: width,
               height: height,
               cx: obj.x,
               cy: obj.y
           };
       } else if (obj.type === 'image') {
           if (!obj.image || !obj.originalWidth || !obj.originalHeight)
               return { x: 0, y: 0, width: 0, height: 0, cx: obj.x, cy: obj.y };

           const scaledWidth = obj.originalWidth * obj.scale;
           const scaledHeight = obj.originalHeight * obj.scale;

           // Add stroke width to bounds if stroke is enabled
           let strokePadding = 0;
           if (obj.strokeMode === 'standard' && obj.strokeWidth) {
               // Use maximum padding to ensure proper selection area for thick strokes
               strokePadding = Math.max(obj.strokeWidth * 3, obj.strokeWidth + 20);
           }

           return {
               x: obj.x - scaledWidth / 2 - strokePadding,
               y: obj.y - scaledHeight / 2 - strokePadding,
               width: scaledWidth + strokePadding * 2,
               height: scaledHeight + strokePadding * 2,
               cx: obj.x,
               cy: obj.y
           };
       }

       return { x: 0, y: 0, width: 0, height: 0, cx: 0, cy: 0 };
   }
   function getRotatedBoundingBox(bounds, angleDeg) { const cx = bounds.cx; const cy = bounds.cy; const w = bounds.width; const h = bounds.height; const x = bounds.x; const y = bounds.y; if (w === 0 || h === 0) return { x: cx, y: cy, width: 0, height: 0 }; const angleRad = angleDeg * Math.PI / 180; const cos = Math.cos(angleRad); const sin = Math.sin(angleRad); const corners = [ { x: x, y: y }, { x: x + w, y: y }, { x: x + w, y: y + h }, { x: x, y: y + h } ]; let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity; corners.forEach(corner => { const translatedX = corner.x - cx; const translatedY = corner.y - cy; const rotatedX = translatedX * cos - translatedY * sin; const rotatedY = translatedX * sin + translatedY * cos; const finalX = rotatedX + cx; const finalY = rotatedY + cy; minX = Math.min(minX, finalX); minY = Math.min(minY, finalY); maxX = Math.max(maxX, finalX); maxY = Math.max(maxY, finalY); }); return { x: minX, y: minY, width: maxX - minX, height: maxY - minY }; }

   // --- UI Binding ---
   function setControlsDisabled(wrapperElement, isDisabled, keepTextEnabled = false) {
       wrapperElement.querySelectorAll('input, select, button').forEach(el => {
           // Special handling for #iText based on keepTextEnabled flag
           if (el.id === 'iText') {
               el.disabled = keepTextEnabled ? false : isDisabled;
           } else {
               el.disabled = isDisabled;
           }
       });
   }
   function updateUIFromSelectedObject() {
       console.log('🔍 updateUIFromSelectedObject called');
       console.log('🔍 selectedObjectIndex:', selectedObjectIndex);
       console.log('🔍 canvasObjects length:', canvasObjects.length);

       const selectedObject = selectedObjectIndex !== -1 ? canvasObjects[selectedObjectIndex] : null;
       console.log('🔍 selectedObject:', selectedObject);

       // Define checks for button state *once* at the beginning
       const canMoveForward = selectedObjectIndex !== -1 && selectedObjectIndex < canvasObjects.length - 1;
       const canMoveBackward = selectedObjectIndex !== -1 && selectedObjectIndex > 0;

       // Auto-switch tabs based on selected object type
       if (selectedObject) {
           const sidebarTabs = document.querySelectorAll('.sidebar-tab');
           const sidebarContents = document.querySelectorAll('.sidebar-content');

           let targetTab = null;
           if (selectedObject.type === 'text') {
               targetTab = document.querySelector('.sidebar-tab[data-tab="text-tab-content"]');
           } else if (selectedObject.type === 'image') {
               targetTab = document.querySelector('.sidebar-tab[data-tab="image-tab-content"]');
           }

           if (targetTab) {
               // Switch to the appropriate tab
               sidebarTabs.forEach(t => t.classList.remove('active'));
               sidebarContents.forEach(c => c.classList.remove('active'));
               targetTab.classList.add('active');
               document.getElementById(targetTab.getAttribute('data-tab'))?.classList.add('active');
           }
       }

       if (selectedObject) {
           if (selectedObject.type === 'text') {
               setControlsDisabled(textControlsWrapper, false, true); // Enable all text controls, keep #iText enabled
               imageControlsWrapper.classList.remove('visible');
               noImageSelectedMsg.style.display = 'block';
               deleteImageBtn.disabled = true;
               addEditTextBtn.textContent = 'Edit';
               deleteTextBtn.disabled = false;

               // Populate text controls (omitted for brevity - this part is unchanged)
               iText.value = selectedObject.text;
               iTextColor.value = selectedObject.color;

               // Update gradient picker if it exists
               const textGradientPicker = document.getElementById('textGradientPicker');
               if (textGradientPicker && textGradientPicker.gradientPickerInstance) {
                   if (selectedObject.gradient) {
                       textGradientPicker.gradientPickerInstance.setValue(selectedObject.gradient.value, selectedObject.gradient.type);
                   } else {
                       textGradientPicker.gradientPickerInstance.setValue(selectedObject.color, 'solid');
                   }
               }

               iFontFamily.value = selectedObject.fontFamily;
               iBold.checked = selectedObject.bold;
               iItalic.checked = selectedObject.italic;
               iFontSize.value = selectedObject.fontSize; vFontSize.textContent = selectedObject.fontSize + 'px';
               iTextRotation.value = selectedObject.rotation; vTextRotation.textContent = selectedObject.rotation + '°';

               // Update letter spacing control
               const letterSpacing = selectedObject.letterSpacing !== undefined ? selectedObject.letterSpacing : 0;
               document.getElementById('iLetterSpacing').value = letterSpacing;
               document.getElementById('vLetterSpacing').textContent = letterSpacing + 'px';

               // Update opacity control
               const opacity = selectedObject.opacity !== undefined ? selectedObject.opacity : 100;
               document.getElementById('iOpacity').value = opacity;
               document.getElementById('vOpacity').textContent = opacity + '%';

               // Update circular rotation slider if it exists
               const iCircleRotation = document.getElementById('iCircleRotation');
               const vCircleRotation = document.getElementById('vCircleRotation');
               if (iCircleRotation && vCircleRotation) {
                   iCircleRotation.value = selectedObject.rotation;
                   vCircleRotation.textContent = selectedObject.rotation + '°';
               }
               effectModeSelect.value = selectedObject.effectMode;
               skewSlider.value = selectedObject.skewX; vSkew.textContent = selectedObject.skewX;
               skewYSlider.value = selectedObject.skewY; vSkewY.textContent = selectedObject.skewY;
               iCurve.value = selectedObject.warpCurve; vCurve.textContent = selectedObject.warpCurve;
               iOffset.value = selectedObject.warpOffset; vOffset.textContent = selectedObject.warpOffset;
               iHeight.value = selectedObject.warpHeight; vHeight.textContent = selectedObject.warpHeight;
               iBottom.value = selectedObject.warpBottom; vBottom.textContent = selectedObject.warpBottom;
               iTriangle.checked = selectedObject.warpTriangle;
               iShiftCenter.value = selectedObject.warpShiftCenter; vShiftCenter.textContent = selectedObject.warpShiftCenter;
               iDiameter.value = selectedObject.circleDiameter; vDiameter.textContent = selectedObject.circleDiameter + 'px';
               iKerning.value = selectedObject.circleKerning; vKerning.textContent = selectedObject.circleKerning + 'px';
               iFlip.checked = selectedObject.circleFlip;
               iCurveAmount.value = selectedObject.curveAmount; vCurveAmount.textContent = selectedObject.curveAmount;
               iCurveKerning.value = selectedObject.curveKerning; vCurveKerning.textContent = selectedObject.curveKerning + 'px';
               iCurveFlip.checked = selectedObject.curveFlip;

               // Grid Distort controls
               if (document.getElementById('iGridDistortCols')) {
                   const iGridDistortCols = document.getElementById('iGridDistortCols');
                   const vGridDistortCols = document.getElementById('vGridDistortCols');
                   const iGridDistortRows = document.getElementById('iGridDistortRows');
                   const vGridDistortRows = document.getElementById('vGridDistortRows');
                   const iGridDistortPadding = document.getElementById('iGridDistortPadding');
                   const vGridDistortPadding = document.getElementById('vGridDistortPadding');
                   const iGridDistortIntensity = document.getElementById('iGridDistortIntensity');
                   const vGridDistortIntensity = document.getElementById('vGridDistortIntensity');
                   const resetGridDistortBtn = document.getElementById('resetGridDistortBtn');
                   const toggleGridDistortBtn = document.getElementById('toggleGridDistortBtn');

                   iGridDistortCols.value = selectedObject.gridDistortCols || 3;
                   vGridDistortCols.textContent = selectedObject.gridDistortCols || 3;

                   iGridDistortRows.value = selectedObject.gridDistortRows || 2;
                   vGridDistortRows.textContent = selectedObject.gridDistortRows || 2;

                   iGridDistortPadding.value = selectedObject.gridDistortPadding || 120;
                   vGridDistortPadding.textContent = (selectedObject.gridDistortPadding || 120) + 'px';

                   iGridDistortIntensity.value = selectedObject.gridDistortIntensity || 100;
                   vGridDistortIntensity.textContent = (selectedObject.gridDistortIntensity || 100) + '%';

                   // Set the direction radio buttons
                   const gridDistortDirectionBoth = document.getElementById('gridDistortDirectionBoth');
                   const gridDistortDirectionVertical = document.getElementById('gridDistortDirectionVertical');

                   if (gridDistortDirectionBoth && gridDistortDirectionVertical) {
                       if (selectedObject.gridDistortVerticalOnly) {
                           gridDistortDirectionVertical.checked = true;
                           gridDistortDirectionBoth.checked = false;
                       } else {
                           gridDistortDirectionBoth.checked = true;
                           gridDistortDirectionVertical.checked = false;
                       }
                   }

                   // Enable Grid Distort buttons when a text object is selected
                   // and when the grid-distort effect is selected
                   if (resetGridDistortBtn) {
                       resetGridDistortBtn.disabled = false;
                   }

                   if (toggleGridDistortBtn) {
                       toggleGridDistortBtn.disabled = false;

                       // Update button text based on current grid visibility state
                       if (selectedObject.gridDistort && selectedObject.gridDistort.showGrid) {
                           toggleGridDistortBtn.textContent = 'Hide Grid';
                       } else {
                           toggleGridDistortBtn.textContent = 'Show Grid';
                       }
                   }

                   // If the effect mode is grid-distort, make sure the buttons are enabled
                   if (selectedObject.effectMode === 'grid-distort') {
                       if (resetGridDistortBtn) resetGridDistortBtn.disabled = false;
                       if (toggleGridDistortBtn) toggleGridDistortBtn.disabled = false;

                       // Enable direction radio buttons
                       if (gridDistortDirectionBoth) gridDistortDirectionBoth.disabled = false;
                       if (gridDistortDirectionVertical) gridDistortDirectionVertical.disabled = false;
                   }
               }

               // Mesh Warp controls
               if (document.getElementById('iMeshCols')) {
                   const iMeshCols = document.getElementById('iMeshCols');
                   const iMeshRows = document.getElementById('iMeshRows');
                   const resetMeshBtn = document.getElementById('resetMeshBtn');
                   const toggleMeshBtn = document.getElementById('toggleMeshBtn');

                   // If we have an active mesh handler for this object, update UI
                   if (typeof activeMeshWarpHandler !== 'undefined' &&
                       activeMeshWarpHandler &&
                       activeMeshWarpHandler.selectedTextObject === selectedObject) {

                       // Update UI with current values from the handler
                       if (iMeshCols) iMeshCols.value = activeMeshWarpHandler.NUM_COLS;
                       if (iMeshRows) iMeshRows.value = activeMeshWarpHandler.NUM_ROWS;

                       // Enable controls
                       if (iMeshCols) iMeshCols.disabled = false;
                       if (iMeshRows) iMeshRows.disabled = false;
                       if (resetMeshBtn) resetMeshBtn.disabled = false;
                       if (toggleMeshBtn) toggleMeshBtn.disabled = false;

                       // Update toggle button text
                       if (toggleMeshBtn) toggleMeshBtn.textContent = activeMeshWarpHandler.showGrid ? 'Hide Grid' : 'Show Grid';
                   } else if (selectedObject.effectMode === 'mesh') {
                       // If effect mode is mesh but no handler exists, create one
                       console.log('Effect mode is mesh but no handler exists, creating one');

                       // Create a new mesh warp handler for this object
                       activeMeshWarpHandler = new MeshWarpHandler(
                           document.getElementById('demo'),
                           selectedObject
                       );

                       // Enable controls
                       if (iMeshCols) iMeshCols.disabled = false;
                       if (iMeshRows) iMeshRows.disabled = false;
                       if (resetMeshBtn) resetMeshBtn.disabled = false;
                       if (toggleMeshBtn) toggleMeshBtn.disabled = false;

                       // Update toggle button text
                       if (toggleMeshBtn) toggleMeshBtn.textContent = activeMeshWarpHandler.showGrid ? 'Hide Grid' : 'Show Grid';
                   } else {
                       // Disable controls if not in mesh mode
                       if (iMeshCols) iMeshCols.disabled = true;
                       if (iMeshRows) iMeshRows.disabled = true;
                       if (resetMeshBtn) resetMeshBtn.disabled = true;
                       if (toggleMeshBtn) toggleMeshBtn.disabled = true;
                   }
               }

               if (shadowSelect) shadowSelect.value = selectedObject.shadowMode;
               if (shadowColorPicker) shadowColorPicker.value = selectedObject.shadowColor;
               if (shadowOffsetXSlider) shadowOffsetXSlider.value = selectedObject.shadowOffsetX;
               if (vShadowOffsetX) vShadowOffsetX.textContent = selectedObject.shadowOffsetX + 'px';
               if (shadowOffsetYSlider) shadowOffsetYSlider.value = selectedObject.shadowOffsetY;
               if (vShadowOffsetY) vShadowOffsetY.textContent = selectedObject.shadowOffsetY + 'px';
               if (shadowBlurSlider) shadowBlurSlider.value = selectedObject.shadowBlur;
               if (vShadowBlur) vShadowBlur.textContent = selectedObject.shadowBlur + 'px';

               // Block Shadow controls
               if (blockShadowColorPicker) blockShadowColorPicker.value = selectedObject.blockShadowColor;
               if (blockShadowOpacitySlider) blockShadowOpacitySlider.value = selectedObject.blockShadowOpacity;
               if (vBlockShadowOpacity) vBlockShadowOpacity.textContent = selectedObject.blockShadowOpacity + '%';
               if (blockShadowOffsetSlider) blockShadowOffsetSlider.value = selectedObject.blockShadowOffset;
               if (vBlockShadowOffset) vBlockShadowOffset.textContent = selectedObject.blockShadowOffset + 'px';
               if (blockShadowAngleSlider) blockShadowAngleSlider.value = selectedObject.blockShadowAngle;
               if (vBlockShadowAngle) vBlockShadowAngle.textContent = selectedObject.blockShadowAngle + '°';
               if (blockShadowBlurSlider) blockShadowBlurSlider.value = selectedObject.blockShadowBlur;
               if (vBlockShadowBlur) vBlockShadowBlur.textContent = selectedObject.blockShadowBlur + 'px';
               if (blockShadowPerspective) blockShadowPerspective.checked = selectedObject.blockShadowPerspective || false;
               // Enable the perspective toggle only if block shadow is selected
               if (blockShadowPerspective) blockShadowPerspective.disabled = (selectedObject.shadowMode !== 'blockShadow');

               // Set perspective intensity slider value
               if (blockShadowPerspectiveIntensity) blockShadowPerspectiveIntensity.value = selectedObject.blockShadowPerspectiveIntensity || 50;
               if (vBlockShadowPerspectiveIntensity) vBlockShadowPerspectiveIntensity.textContent = (selectedObject.blockShadowPerspectiveIntensity || 50) + '%';
               if (blockShadowPerspectiveIntensity) blockShadowPerspectiveIntensity.disabled = (selectedObject.shadowMode !== 'blockShadow' || !selectedObject.blockShadowPerspective);

               // Show/hide the perspective intensity control based on perspective toggle
               const perspectiveControl = document.querySelector('.perspective-control');
               if (perspectiveControl) {
                   perspectiveControl.style.display = (selectedObject.blockShadowPerspective && selectedObject.shadowMode === 'blockShadow') ? 'block' : 'none';
               }

               // Perspective Shadow controls
               const perspectiveShadowColorPicker = document.getElementById('perspectiveShadowColor');
               const perspectiveShadowOpacitySlider = document.getElementById('perspectiveShadowOpacity');
               const perspectiveShadowOffsetSlider = document.getElementById('perspectiveShadowOffset');
               const perspectiveShadowAngleSlider = document.getElementById('perspectiveShadowAngle');
               const perspectiveShadowBlurSlider = document.getElementById('perspectiveShadowBlur');
               const perspectiveShadowIntensitySlider = document.getElementById('perspectiveShadowIntensity');
               const vPerspectiveShadowOpacity = document.getElementById('vPerspectiveShadowOpacity');
               const vPerspectiveShadowOffset = document.getElementById('vPerspectiveShadowOffset');
               const vPerspectiveShadowAngle = document.getElementById('vPerspectiveShadowAngle');
               const vPerspectiveShadowBlur = document.getElementById('vPerspectiveShadowBlur');
               const vPerspectiveShadowIntensity = document.getElementById('vPerspectiveShadowIntensity');

               if (perspectiveShadowColorPicker) perspectiveShadowColorPicker.value = selectedObject.perspectiveShadowColor || '#333333';
               if (perspectiveShadowOpacitySlider) {
                   perspectiveShadowOpacitySlider.value = selectedObject.perspectiveShadowOpacity || 100;
                   if (vPerspectiveShadowOpacity) vPerspectiveShadowOpacity.textContent = (selectedObject.perspectiveShadowOpacity || 100) + '%';
               }
               if (perspectiveShadowOffsetSlider) {
                   perspectiveShadowOffsetSlider.value = selectedObject.perspectiveShadowOffset || 6;
                   if (vPerspectiveShadowOffset) vPerspectiveShadowOffset.textContent = (selectedObject.perspectiveShadowOffset || 6) + 'px';
               }
               if (perspectiveShadowAngleSlider) {
                   perspectiveShadowAngleSlider.value = selectedObject.perspectiveShadowAngle || 105;
                   if (vPerspectiveShadowAngle) vPerspectiveShadowAngle.textContent = (selectedObject.perspectiveShadowAngle || 105) + '°';
               }
               if (perspectiveShadowBlurSlider) {
                   perspectiveShadowBlurSlider.value = selectedObject.perspectiveShadowBlur || 2;
                   if (vPerspectiveShadowBlur) vPerspectiveShadowBlur.textContent = (selectedObject.perspectiveShadowBlur || 2) + 'px';
               }
               if (perspectiveShadowIntensitySlider) {
                   perspectiveShadowIntensitySlider.value = selectedObject.perspectiveShadowIntensity || 16;
                   if (vPerspectiveShadowIntensity) vPerspectiveShadowIntensity.textContent = (selectedObject.perspectiveShadowIntensity || 16) + '%';
               }

               // Perspective Shadow Outline controls
               const perspectiveShadowOutlineColorPicker = document.getElementById('perspectiveShadowOutlineColor');
               const perspectiveShadowOutlineOpacitySlider = document.getElementById('perspectiveShadowOutlineOpacity');
               const perspectiveShadowOutlineWidthSlider = document.getElementById('perspectiveShadowOutlineWidth');
               const perspectiveShadowOutlineOffsetXSlider = document.getElementById('perspectiveShadowOutlineOffsetX');
               const perspectiveShadowOutlineOffsetYSlider = document.getElementById('perspectiveShadowOutlineOffsetY');
               const vPerspectiveShadowOutlineOpacity = document.getElementById('vPerspectiveShadowOutlineOpacity');
               const vPerspectiveShadowOutlineWidth = document.getElementById('vPerspectiveShadowOutlineWidth');
               const vPerspectiveShadowOutlineOffsetX = document.getElementById('vPerspectiveShadowOutlineOffsetX');
               const vPerspectiveShadowOutlineOffsetY = document.getElementById('vPerspectiveShadowOutlineOffsetY');

               if (perspectiveShadowOutlineColorPicker) perspectiveShadowOutlineColorPicker.value = selectedObject.perspectiveShadowOutlineColor || '#d1d5db';
               if (perspectiveShadowOutlineOpacitySlider) {
                   perspectiveShadowOutlineOpacitySlider.value = selectedObject.perspectiveShadowOutlineOpacity || 100;
                   if (vPerspectiveShadowOutlineOpacity) vPerspectiveShadowOutlineOpacity.textContent = (selectedObject.perspectiveShadowOutlineOpacity || 100) + '%';
               }
               if (perspectiveShadowOutlineWidthSlider) {
                   perspectiveShadowOutlineWidthSlider.value = selectedObject.perspectiveShadowOutlineWidth || 3;
                   if (vPerspectiveShadowOutlineWidth) vPerspectiveShadowOutlineWidth.textContent = (selectedObject.perspectiveShadowOutlineWidth || 3) + 'px';
               }
               if (perspectiveShadowOutlineOffsetXSlider) {
                   perspectiveShadowOutlineOffsetXSlider.value = selectedObject.perspectiveShadowOutlineOffsetX || 2;
                   if (vPerspectiveShadowOutlineOffsetX) vPerspectiveShadowOutlineOffsetX.textContent = (selectedObject.perspectiveShadowOutlineOffsetX || 2) + 'px';
               }
               if (perspectiveShadowOutlineOffsetYSlider) {
                   perspectiveShadowOutlineOffsetYSlider.value = selectedObject.perspectiveShadowOutlineOffsetY || -3;
                   if (vPerspectiveShadowOutlineOffsetY) vPerspectiveShadowOutlineOffsetY.textContent = (selectedObject.perspectiveShadowOutlineOffsetY || -3) + 'px';
               }
               if (lineShadowColorPicker) lineShadowColorPicker.value = selectedObject.lineShadowColor;
               if (lineShadowDistanceSlider) lineShadowDistanceSlider.value = selectedObject.lineShadowDist;
               if (vLineShadowDistance) vLineShadowDistance.textContent = selectedObject.lineShadowDist + 'px';
               if (lineShadowAngleSlider) lineShadowAngleSlider.value = selectedObject.lineShadowAngle;
               if (vLineShadowAngle) vLineShadowAngle.textContent = selectedObject.lineShadowAngle + '°';
               if (lineShadowThicknessSlider) lineShadowThicknessSlider.value = selectedObject.lineShadowThickness;
               if (vLineShadowThickness) vLineShadowThickness.textContent = selectedObject.lineShadowThickness + 'px';
               if (detailed3DPrimaryColorPicker) detailed3DPrimaryColorPicker.value = selectedObject.d3dPrimaryColor;
               if (detailed3DPrimaryOpacitySlider) detailed3DPrimaryOpacitySlider.value = selectedObject.d3dPrimaryOpacity;
               if (vDetailed3DPrimaryOpacity) vDetailed3DPrimaryOpacity.textContent = selectedObject.d3dPrimaryOpacity + '%';
               if (detailed3DOffsetSlider) detailed3DOffsetSlider.value = selectedObject.d3dOffset;
               if (vDetailed3DOffset) vDetailed3DOffset.textContent = selectedObject.d3dOffset + 'px';
               if (detailed3DAngleSlider) detailed3DAngleSlider.value = selectedObject.d3dAngle;
               if (vDetailed3DAngle) vDetailed3DAngle.textContent = selectedObject.d3dAngle + '°';
               if (detailed3DBlurSlider) detailed3DBlurSlider.value = selectedObject.d3dBlur;
               if (vDetailed3DBlur) vDetailed3DBlur.textContent = selectedObject.d3dBlur + 'px';
               if (detailed3DSecondaryColorPicker) detailed3DSecondaryColorPicker.value = selectedObject.d3dSecondaryColor;
               if (detailed3DSecondaryOpacitySlider) detailed3DSecondaryOpacitySlider.value = selectedObject.d3dSecondaryOpacity;
               if (vDetailed3DSecondaryOpacity) vDetailed3DSecondaryOpacity.textContent = selectedObject.d3dSecondaryOpacity + '%';
               if (detailed3DSecondaryWidthSlider) detailed3DSecondaryWidthSlider.value = selectedObject.d3dSecondaryWidth;
               if (vDetailed3DSecondaryWidth) vDetailed3DSecondaryWidth.textContent = selectedObject.d3dSecondaryWidth + 'px';
               if (detailed3DSecondaryOffsetXSlider) detailed3DSecondaryOffsetXSlider.value = selectedObject.d3dSecondaryOffsetX;
               if (vDetailed3DSecondaryOffsetX) vDetailed3DSecondaryOffsetX.textContent = selectedObject.d3dSecondaryOffsetX + 'px';
               if (detailed3DSecondaryOffsetYSlider) detailed3DSecondaryOffsetYSlider.value = selectedObject.d3dSecondaryOffsetY;
               if (vDetailed3DSecondaryOffsetY) vDetailed3DSecondaryOffsetY.textContent = selectedObject.d3dSecondaryOffsetY + 'px';
               if (strokeToggle) strokeToggle.value = selectedObject.strokeMode;
               if (strokeWidthSlider) strokeWidthSlider.value = selectedObject.strokeWidth;
               if (vStrokeWidth) vStrokeWidth.textContent = selectedObject.strokeWidth + 'px';
               if (strokeColorPicker) strokeColorPicker.value = selectedObject.strokeColor;
               if (strokeOpacitySlider) strokeOpacitySlider.value = selectedObject.strokeOpacity || 100;
               if (vStrokeOpacity) vStrokeOpacity.textContent = (selectedObject.strokeOpacity || 100) + '%';
               if (linesDecorationSelect) linesDecorationSelect.value = selectedObject.decorationMode;
               if (hWeight) hWeight.value = selectedObject.hLineWeight;
               if (vHWeight) vHWeight.textContent = selectedObject.hLineWeight + 'px';
               if (hDistance) hDistance.value = selectedObject.hLineDist;
               if (vHDistance) vHDistance.textContent = selectedObject.hLineDist + 'px';
               if (hColor) hColor.value = selectedObject.hLineColor;
               if (ccDistance) ccDistance.value = selectedObject.ccDist;
               if (vCcDistance) vCcDistance.textContent = selectedObject.ccDist + '%';
               if (ccColor) ccColor.value = selectedObject.ccColor;
               if (ccFillTop && ccFillBottom) {
                   if (selectedObject.ccFillDir === 'top') {
                       ccFillTop.checked = true;
                       ccFillBottom.checked = false;
                   } else {
                       ccFillTop.checked = false;
                       ccFillBottom.checked = true;
                   }
               }
               if (oWeight) oWeight.value = selectedObject.oLineWeight;
               if (vOWeight) vOWeight.textContent = selectedObject.oLineWeight + 'px';
               if (oDistance) oDistance.value = selectedObject.oLineDist;
               if (vODistance) vODistance.textContent = selectedObject.oLineDist + 'px';
               if (oColor) oColor.value = selectedObject.oLineColor;
               if (flcDistance) flcDistance.value = selectedObject.flcDist;
               if (vFlcDistance) vFlcDistance.textContent = selectedObject.flcDist + '%';
               if (flcColor) flcColor.value = selectedObject.flcColor;
               if (flcMaxWeight) flcMaxWeight.value = selectedObject.flcWeight;
               if (vFlcMaxWeight) vFlcMaxWeight.textContent = selectedObject.flcWeight + 'px';
               if (flcSpacing) flcSpacing.value = selectedObject.flcSpacing;
               if (vFlcSpacing) vFlcSpacing.textContent = selectedObject.flcSpacing + 'px';
               if (flcFillTop && flcFillBottom) {
                   if (selectedObject.flcDir === 'top') {
                       flcFillTop.checked = true;
                       flcFillBottom.checked = false;
                   } else {
                       flcFillTop.checked = false;
                       flcFillBottom.checked = true;
                   }
               }

               updateBodyClass(selectedObject);

           } else if (selectedObject.type === 'image') {
               setControlsDisabled(textControlsWrapper, true, true); // Disable text controls BUT keep #iText enabled
               imageControlsWrapper.classList.add('visible');
               noImageSelectedMsg.style.display = 'none';
               deleteImageBtn.disabled = false;
               addEditTextBtn.textContent = 'Add'; // Keep 'Add' for text when image selected
               deleteTextBtn.disabled = true;

               // Populate image controls
               iImageSize.value = selectedObject.scale; vImageSize.textContent = Math.round(selectedObject.scale * 100) + '%';
               iImageRotation.value = selectedObject.rotation; vImageRotation.textContent = selectedObject.rotation + '°';
               iImageOpacity.value = selectedObject.opacity || 100; vImageOpacity.textContent = (selectedObject.opacity || 100) + '%';

               // Populate image stroke controls
               iImageStroke.value = selectedObject.strokeMode || 'none';
               iImageStrokeWidth.value = selectedObject.strokeWidth || 3;
               vImageStrokeWidth.textContent = (selectedObject.strokeWidth || 3) + 'px';
               iImageStrokeColor.value = selectedObject.strokeColor || '#000000';
               iImageStrokeOpacity.value = selectedObject.strokeOpacity || 100;
               vImageStrokeOpacity.textContent = (selectedObject.strokeOpacity || 100) + '%';

               // Show/hide stroke parameter controls
               const imageStrokeParam = document.querySelector('.image-stroke-param');
               if (imageStrokeParam) {
                   imageStrokeParam.style.display = selectedObject.strokeMode === 'standard' ? 'block' : 'none';
               }

               // Populate image shadow controls
               iImageShadow.value = selectedObject.shadowMode || 'none';
               iImageShadowColor.value = selectedObject.shadowColor || '#000000';
               iImageShadowOpacity.value = selectedObject.shadowOpacity || 100;
               vImageShadowOpacity.textContent = (selectedObject.shadowOpacity || 100) + '%';
               iImageShadowOffsetX.value = selectedObject.shadowOffsetX || 5;
               vImageShadowOffsetX.textContent = (selectedObject.shadowOffsetX || 5) + 'px';
               iImageShadowOffsetY.value = selectedObject.shadowOffsetY || 5;
               vImageShadowOffsetY.textContent = (selectedObject.shadowOffsetY || 5) + 'px';
               iImageShadowBlur.value = selectedObject.shadowBlur || 10;
               vImageShadowBlur.textContent = (selectedObject.shadowBlur || 10) + 'px';

               // Show/hide shadow parameter controls
               const imageShadowParam = document.querySelector('.image-shadow-param');
               if (imageShadowParam) {
                   imageShadowParam.style.display = selectedObject.shadowMode === 'standard' ? 'block' : 'none';
               }

               // Show/hide color picker for SVG files
               const imageColorGroup = document.getElementById('imageColorGroup');
               const iImageColor = document.getElementById('iImageColor');
               if (selectedObject.imageUrl && selectedObject.imageUrl.toLowerCase().endsWith('.svg')) {
                   imageColorGroup.style.display = 'flex';
                   if (iImageColor) {
                       iImageColor.value = selectedObject.svgColor || '#000000';
                   }
               } else {
                   imageColorGroup.style.display = 'none';
               }

               // Show/hide Remove Background button
               const removeBgBtn = document.getElementById('removeBgBtn');
               if (selectedObject.isFromGeneration && selectedObject.generationId && !selectedObject.backgroundRemoved) {
                   removeBgBtn.style.display = 'block';
                   removeBgBtn.disabled = false;
                   removeBgBtn.textContent = 'Remove Background';
               } else {
                   removeBgBtn.style.display = 'none';
               }

               document.body.className = 'image-selected';
           }
       } else {
           // No object selected
           setControlsDisabled(textControlsWrapper, true, true); // Disable text controls BUT keep #iText enabled
           iText.value = ''; // Clear text input
           imageControlsWrapper.classList.remove('visible');
           noImageSelectedMsg.style.display = 'block';
           deleteImageBtn.disabled = true;
           addEditTextBtn.textContent = 'Add';
           deleteTextBtn.disabled = true;
           // Don't set move button state here, do it once at the end
           document.body.className = 'normal';
       }
       // Update move button states *once* at the end, based on checks
       moveForwardBtn.disabled = !canMoveForward;
       moveBackwardBtn.disabled = !canMoveBackward;

       // Update copy/paste button states
       updateCopyPasteButtons();
   }
   function updateSelectedObjectFromUI(property, value) {
       console.log(`=== UPDATE OBJECT PROPERTY DEBUG (${property}) ===`);

       if (selectedObjectIndex === -1) {
           console.error(`Cannot update property '${property}': No object selected`);
           console.log('=== END UPDATE OBJECT PROPERTY DEBUG ===');
           return;
       }

       const selectedObject = canvasObjects[selectedObjectIndex];
       console.log(`Updating property '${property}' on object:`, {
           id: selectedObject.id,
           type: selectedObject.type,
           text: selectedObject.text,
           oldValue: selectedObject[property],
           newValue: value
       });

       // Special handling for blockShadowPerspectiveIntensity
       if (property === 'blockShadowPerspectiveIntensity' && selectedObject.type === 'text') {
           console.log('Special handling for blockShadowPerspectiveIntensity');
           console.log('Current shadow state:', {
               shadowMode: selectedObject.shadowMode,
               blockShadowPerspective: selectedObject.blockShadowPerspective,
               blockShadowPerspectiveIntensity: selectedObject.blockShadowPerspectiveIntensity
           });

           // Make sure perspective mode is enabled when changing intensity
           if (selectedObject.shadowMode === 'blockShadow' && !selectedObject.blockShadowPerspective) {
               console.log('Forcing perspective mode to be enabled');
               selectedObject.blockShadowPerspective = true;

               // Update UI to reflect this change
               if (blockShadowPerspective) {
                   blockShadowPerspective.checked = true;
                   console.log('Updated checkbox state to checked');
               } else {
                   console.error('Could not find blockShadowPerspective checkbox!');
               }

               // Show the perspective control
               const perspectiveControl = document.querySelector('.perspective-control');
               if (perspectiveControl) {
                   perspectiveControl.style.display = 'block';
                   console.log('Perspective control display set to: block');
               } else {
                   console.error('Could not find perspective-control element!');
               }
           }

           // If shadow mode is not blockShadow, force it
           if (selectedObject.shadowMode !== 'blockShadow') {
               console.log('Forcing shadow mode to blockShadow (was:', selectedObject.shadowMode, ')');
               selectedObject.shadowMode = 'blockShadow';

               // Update UI to reflect this change
               if (shadowSelect) {
                   shadowSelect.value = 'blockShadow';
                   console.log('Updated shadow select to blockShadow');
               } else {
                   console.error('Could not find shadowSelect element!');
               }
           }
       }

       if (selectedObject.type === 'text') {
           if (selectedObject.hasOwnProperty(property) || property.startsWith('gridDistort') || property === 'gradient') {
               // Store old value for logging
               const oldValue = selectedObject[property];

               // Update the property
               selectedObject[property] = value;

               console.log(`Property '${property}' updated:`, {
                   oldValue: oldValue,
                   newValue: value,
                   effectiveValue: selectedObject[property] // Check what was actually stored
               });
               switch (property) {
                   case 'fontSize': vFontSize.textContent = value + 'px'; break;
                   case 'rotation': vTextRotation.textContent = value + '°'; break;
                   case 'skewX': vSkew.textContent = value; break;
                   case 'skewY': vSkewY.textContent = value; break;
                   case 'warpCurve': vCurve.textContent = value; break;
                   case 'warpOffset': vOffset.textContent = value; break;
                   case 'warpHeight': vHeight.textContent = value; break;
                   case 'warpBottom': vBottom.textContent = value; break;
                   case 'warpShiftCenter': vShiftCenter.textContent = value; break;
                   case 'circleDiameter': vDiameter.textContent = value + 'px'; break;
                   case 'circleKerning': vKerning.textContent = value + 'px'; break;
                   case 'curveAmount': vCurveAmount.textContent = value; break;
                   case 'curveKerning': vCurveKerning.textContent = value + 'px'; break;
                   case 'gridDistortCols':
                       if (document.getElementById('vGridDistortCols')) {
                           document.getElementById('vGridDistortCols').textContent = value;
                       }
                       break;
                   case 'gridDistortRows':
                       if (document.getElementById('vGridDistortRows')) {
                           document.getElementById('vGridDistortRows').textContent = value;
                       }
                       break;
                   case 'gridDistortPadding':
                       if (document.getElementById('vGridDistortPadding')) {
                           document.getElementById('vGridDistortPadding').textContent = value + 'px';
                       }
                       break;
                   case 'gridDistortIntensity':
                       if (document.getElementById('vGridDistortIntensity')) {
                           document.getElementById('vGridDistortIntensity').textContent = value + '%';
                       }
                       break;
                   case 'hLineWeight': vHWeight.textContent = value + 'px'; break;
                   case 'hLineDist': vHDistance.textContent = value + 'px'; break;
                   case 'ccDist': vCcDistance.textContent = value + '%'; break;
                   case 'oLineWeight': vOWeight.textContent = value + 'px'; break;
                   case 'oLineDist': vODistance.textContent = value + 'px'; break;
                   case 'flcDist': vFlcDistance.textContent = value + '%'; break;
                   case 'flcWeight': vFlcMaxWeight.textContent = value + 'px'; break;
                   case 'flcSpacing': vFlcSpacing.textContent = value + 'px'; break;
                   case 'strokeWidth': vStrokeWidth.textContent = value + 'px'; break;
                   case 'shadowOffsetX': vShadowOffsetX.textContent = value + 'px'; break;
                   case 'shadowOffsetY': vShadowOffsetY.textContent = value + 'px'; break;
                   case 'shadowBlur': vShadowBlur.textContent = value + 'px'; break;
                   case 'blockShadowOpacity': vBlockShadowOpacity.textContent = value + '%'; break;
                   case 'blockShadowOffset': vBlockShadowOffset.textContent = value + 'px'; break;
                   case 'blockShadowAngle': vBlockShadowAngle.textContent = value + '°'; break;
                   case 'blockShadowBlur': vBlockShadowBlur.textContent = value + 'px'; break;
                   case 'blockShadowPerspectiveIntensity':
                       if (vBlockShadowPerspectiveIntensity) {
                           vBlockShadowPerspectiveIntensity.textContent = value + '%';
                           console.log('Updated perspective intensity display to:', value + '%');
                       } else {
                           console.error('Could not find vBlockShadowPerspectiveIntensity element!');
                       }
                       break;
                   case 'perspectiveShadowColor':
                       if (document.getElementById('perspectiveShadowColor')) {
                           document.getElementById('perspectiveShadowColor').value = value;
                       }
                       break;
                   case 'perspectiveShadowOpacity':
                       if (document.getElementById('vPerspectiveShadowOpacity')) {
                           document.getElementById('vPerspectiveShadowOpacity').textContent = value + '%';
                       }
                       break;
                   case 'perspectiveShadowOffset':
                       if (document.getElementById('vPerspectiveShadowOffset')) {
                           document.getElementById('vPerspectiveShadowOffset').textContent = value + 'px';
                       }
                       break;
                   case 'perspectiveShadowAngle':
                       if (document.getElementById('vPerspectiveShadowAngle')) {
                           document.getElementById('vPerspectiveShadowAngle').textContent = value + '°';
                       }
                       break;
                   case 'perspectiveShadowBlur':
                       if (document.getElementById('vPerspectiveShadowBlur')) {
                           document.getElementById('vPerspectiveShadowBlur').textContent = value + 'px';
                       }
                       break;
                   case 'perspectiveShadowIntensity':
                       if (document.getElementById('vPerspectiveShadowIntensity')) {
                           document.getElementById('vPerspectiveShadowIntensity').textContent = value + '%';
                       }
                       break;
                   case 'perspectiveShadowOutlineColor':
                       if (document.getElementById('perspectiveShadowOutlineColor')) {
                           document.getElementById('perspectiveShadowOutlineColor').value = value;
                       }
                       break;
                   case 'perspectiveShadowOutlineOpacity':
                       if (document.getElementById('vPerspectiveShadowOutlineOpacity')) {
                           document.getElementById('vPerspectiveShadowOutlineOpacity').textContent = value + '%';
                       }
                       break;
                   case 'perspectiveShadowOutlineWidth':
                       if (document.getElementById('vPerspectiveShadowOutlineWidth')) {
                           document.getElementById('vPerspectiveShadowOutlineWidth').textContent = value + 'px';
                       }
                       break;
                   case 'perspectiveShadowOutlineOffsetX':
                       if (document.getElementById('vPerspectiveShadowOutlineOffsetX')) {
                           document.getElementById('vPerspectiveShadowOutlineOffsetX').textContent = value + 'px';
                       }
                       break;
                   case 'perspectiveShadowOutlineOffsetY':
                       if (document.getElementById('vPerspectiveShadowOutlineOffsetY')) {
                           document.getElementById('vPerspectiveShadowOutlineOffsetY').textContent = value + 'px';
                       }
                       break;
                   case 'letterSpacing':
                       if (document.getElementById('vLetterSpacing')) {
                           document.getElementById('vLetterSpacing').textContent = value + 'px';
                       }
                       break;
                   case 'opacity':
                       if (document.getElementById('vOpacity')) {
                           document.getElementById('vOpacity').textContent = value + '%';
                       }
                       break;
                   case 'lineShadowDist': vLineShadowDistance.textContent = value + 'px'; break;
                   case 'lineShadowAngle': vLineShadowAngle.textContent = value + '°'; break;
                   case 'lineShadowThickness': vLineShadowThickness.textContent = value + 'px'; break;
                   case 'd3dPrimaryOpacity': vDetailed3DPrimaryOpacity.textContent = value + '%'; break;
                   case 'd3dOffset': vDetailed3DOffset.textContent = value + 'px'; break;
                   case 'd3dAngle': vDetailed3DAngle.textContent = value + '°'; break;
                   case 'd3dBlur': vDetailed3DBlur.textContent = value + 'px'; break;
                   case 'd3dSecondaryOpacity': vDetailed3DSecondaryOpacity.textContent = value + '%'; break;
                   case 'd3dSecondaryWidth': vDetailed3DSecondaryWidth.textContent = value + 'px'; break;
                   case 'd3dSecondaryOffsetX': vDetailed3DSecondaryOffsetX.textContent = value + 'px'; break;
                   case 'd3dSecondaryOffsetY': vDetailed3DSecondaryOffsetY.textContent = value + 'px'; break;
               }

            // Special handling for text content changes
            if (property === 'text' && selectedObject.gridDistort) {
                console.log('Text changed from', selectedObject.gridDistort.lastText, 'to', value);
                console.log('Has relative control points?',
                    selectedObject.gridDistort.relativeControlPoints ?
                    selectedObject.gridDistort.relativeControlPoints.length : 'No');

                // Don't update lastText here - let initializeGridPoints handle it
                // This ensures the text change is detected properly
                initializeGridPoints(selectedObject);
                console.log('Reinitialized grid due to text content change');

                // Log the state after initialization
                console.log('After init - Has relative control points?',
                    selectedObject.gridDistort.relativeControlPoints ?
                    selectedObject.gridDistort.relativeControlPoints.length : 'No');
            }

            // Special handling for font size changes with grid distortion
            if (property === 'fontSize' && selectedObject.gridDistort) {
                console.log('Font size changed from', selectedObject.gridDistort.lastFontSize, 'to', value);
                console.log('Has relative control points?',
                    selectedObject.gridDistort.relativeControlPoints ?
                    selectedObject.gridDistort.relativeControlPoints.length : 'No');

                // Reinitialize grid points to scale with the new font size
                // while preserving the relative distortion pattern
                initializeGridPoints(selectedObject);
                console.log('Reinitialized grid due to font size change');

                // Log the state after initialization
                console.log('After font size change - Has relative control points?',
                    selectedObject.gridDistort.relativeControlPoints ?
                    selectedObject.gridDistort.relativeControlPoints.length : 'No');
            }

            // Special handling for mesh warp when text content or font size changes
            if ((property === 'text' || property === 'fontSize') &&
                selectedObject.effectMode === 'mesh' &&
                typeof activeMeshWarpHandler !== 'undefined' &&
                activeMeshWarpHandler &&
                activeMeshWarpHandler.selectedTextObject === selectedObject) {

                // The mesh warp handler will handle the grid update in its drawMeshGrid and drawWarpedText methods
                console.log('Text or font size changed for mesh warp object, grid will be updated on next render');
            }

            // Add special case for blockShadowPerspective
            if (property === 'blockShadowPerspective') {
                console.log('Block shadow perspective set to:', value);
            }

            // Add special case for shadowMode
            if (property === 'shadowMode') {
                console.log('Shadow mode set to:', value);
            }

            if (['effectMode', 'decorationMode', 'strokeMode', 'shadowMode', 'warpTriangle'].includes(property)) {
                updateBodyClass(selectedObject);
                console.log(`Updated body class for ${property} change`);
            }

            console.log('Forcing redraw...');
            update();
            console.log(`Property '${property}' update complete`);
        } else {
            console.warn(`Property "${property}" not found on selected text object.`);
        }
    } else if (selectedObject.type === 'image') {
        if (property === 'scale') {
            selectedObject.scale = parseFloat(value);
            vImageSize.textContent = Math.round(selectedObject.scale * 100) + '%';
            // Invalidate stroke cache when scale changes
            selectedObject.strokeCache = null;
            selectedObject.strokeCacheKey = null;
        } else if (property === 'rotation') {
            selectedObject.rotation = parseInt(value, 10);
            vImageRotation.textContent = selectedObject.rotation + '°';
        } else if (property === 'svgColor') {
            selectedObject.svgColor = value;
            // Regenerate the colored SVG
            if (selectedObject.imageUrl && selectedObject.imageUrl.toLowerCase().endsWith('.svg')) {
                recolorSVG(selectedObject, value);
            }
        } else if (property === 'svgGradient') {
            selectedObject.svgGradient = value;
            // Store gradient data directly on the object (like text gradients)
            if (value) {
                selectedObject.gradient = value;
                selectedObject.fillType = 'gradient';
                console.log('🎨 SVG gradient stored:', value);
            } else {
                selectedObject.gradient = null;
                selectedObject.fillType = 'solid';
                console.log('🎨 SVG gradient cleared');
            }
        } else if (property === 'shadowMode') {
            selectedObject.shadowMode = value;
        } else if (property === 'shadowColor') {
            selectedObject.shadowColor = value;
        } else if (property === 'shadowOffsetX') {
            selectedObject.shadowOffsetX = parseInt(value, 10);
            vImageShadowOffsetX.textContent = selectedObject.shadowOffsetX + 'px';
        } else if (property === 'shadowOffsetY') {
            selectedObject.shadowOffsetY = parseInt(value, 10);
            vImageShadowOffsetY.textContent = selectedObject.shadowOffsetY + 'px';
        } else if (property === 'shadowBlur') {
            selectedObject.shadowBlur = parseInt(value, 10);
            vImageShadowBlur.textContent = selectedObject.shadowBlur + 'px';
        } else if (property === 'shadowOpacity') {
            selectedObject.shadowOpacity = parseInt(value, 10);
            vImageShadowOpacity.textContent = selectedObject.shadowOpacity + '%';
        } else if (property === 'opacity') {
            selectedObject.opacity = parseInt(value, 10);
            vImageOpacity.textContent = selectedObject.opacity + '%';
        } else if (property === 'strokeMode') {
            selectedObject.strokeMode = value;
            // Invalidate stroke cache
            selectedObject.strokeCache = null;
            selectedObject.strokeCacheKey = null;
        } else if (property === 'strokeWidth') {
            selectedObject.strokeWidth = parseInt(value, 10);
            vImageStrokeWidth.textContent = selectedObject.strokeWidth + 'px';
            // Invalidate stroke cache
            selectedObject.strokeCache = null;
            selectedObject.strokeCacheKey = null;
        } else if (property === 'strokeColor') {
            selectedObject.strokeColor = value;
            // Invalidate stroke cache
            selectedObject.strokeCache = null;
            selectedObject.strokeCacheKey = null;
        } else {
            console.warn(`Property "${property}" not found or not applicable to image object.`);
            console.log('=== END UPDATE OBJECT PROPERTY DEBUG ===');
            return;
        }
        update();
    }

    console.log('=== END UPDATE OBJECT PROPERTY DEBUG ===');

    // Save state for undo/redo (with debouncing to avoid too many saves)
    if (!updateSelectedObjectFromUI.saveTimeout) {
        updateSelectedObjectFromUI.saveTimeout = setTimeout(() => {
            saveState(`Change ${property}`);
            updateSelectedObjectFromUI.saveTimeout = null;
        }, 500); // 500ms delay to group rapid changes
    }
}

   // --- SVG Color Recoloring Function ---
   async function recolorSVG(imageObject, newColor) {
       try {
           console.log('Recoloring SVG:', imageObject.imageUrl, 'to color:', newColor);

           // Fetch the original SVG content
           const response = await fetch(imageObject.imageUrl);
           if (!response.ok) {
               throw new Error(`Failed to fetch SVG: ${response.status}`);
           }

           const svgText = await response.text();
           console.log('Original SVG content preview:', svgText.substring(0, 200) + '...');

           // Create a new SVG with the desired color
           const coloredSvgText = applySVGColor(svgText, newColor);
           console.log('Colored SVG content preview:', coloredSvgText.substring(0, 200) + '...');

           // Create a blob URL for the colored SVG
           const blob = new Blob([coloredSvgText], { type: 'image/svg+xml' });
           const coloredUrl = URL.createObjectURL(blob);

           // Create a new image with the colored SVG
           const img = new Image();
           img.onload = () => {
               // Clean up the old blob URL if it exists
               if (imageObject.coloredBlobUrl) {
                   URL.revokeObjectURL(imageObject.coloredBlobUrl);
               }

               // Update the image object
               imageObject.image = img;
               imageObject.coloredBlobUrl = coloredUrl;
               imageObject.svgColor = newColor;

               // Force a redraw
               update();

               console.log('SVG recolored successfully');
           };

           img.onerror = (error) => {
               console.error('Error loading recolored SVG:', imageObject.imageUrl, error);
               console.error('Colored SVG content preview:', coloredSvgText.substring(0, 500) + '...');
               URL.revokeObjectURL(coloredUrl);

               // Fallback: keep the original image
               console.log('Keeping original SVG without color change');
           };

           img.src = coloredUrl;

       } catch (error) {
           console.error('Error recoloring SVG:', error);
       }
   }

   // --- SVG Gradient Recoloring Function ---
   async function recolorSVGWithGradient(imageObject, gradientData) {
       try {
           console.log('🎨 Applying gradient to SVG:', imageObject.imageUrl, gradientData);

           // Fetch the original SVG content
           const response = await fetch(imageObject.imageUrl);
           if (!response.ok) {
               throw new Error(`Failed to fetch SVG: ${response.status}`);
           }

           const svgText = await response.text();
           console.log('🎨 Original SVG content preview:', svgText.substring(0, 200) + '...');

           // Create a new SVG with the desired gradient
           const gradientSvgText = applySVGGradient(svgText, gradientData);
           console.log('🎨 Gradient SVG content preview:', gradientSvgText.substring(0, 200) + '...');

           // Create a blob URL for the gradient SVG
           const blob = new Blob([gradientSvgText], { type: 'image/svg+xml' });
           const gradientUrl = URL.createObjectURL(blob);

           // Create a new image with the gradient SVG
           const img = new Image();
           img.onload = () => {
               // Clean up the old blob URL if it exists
               if (imageObject.coloredBlobUrl) {
                   URL.revokeObjectURL(imageObject.coloredBlobUrl);
               }

               // Update the image object
               imageObject.image = img;
               imageObject.coloredBlobUrl = gradientUrl;
               imageObject.svgGradient = gradientData;

               // Force a redraw
               update();

               console.log('🎨 SVG gradient applied successfully');
           };

           img.onerror = (error) => {
               console.error('🎨 Error loading gradient SVG:', imageObject.imageUrl, error);
               console.error('🎨 Gradient SVG content preview:', gradientSvgText.substring(0, 500) + '...');
               URL.revokeObjectURL(gradientUrl);

               // Fallback: keep the original image
               console.log('🎨 Keeping original SVG without gradient change');
           };

           img.src = gradientUrl;

       } catch (error) {
           console.error('🎨 Error applying gradient to SVG:', error);
       }
   }

   function applySVGColor(svgText, color) {
       try {
           // Parse the SVG to ensure it's valid XML
           const parser = new DOMParser();
           const svgDoc = parser.parseFromString(svgText, 'image/svg+xml');

           // Check for parsing errors
           const parserError = svgDoc.querySelector('parsererror');
           if (parserError) {
               console.error('SVG parsing error:', parserError.textContent);
               return svgText; // Return original if parsing fails
           }

           const svgElement = svgDoc.documentElement;

           // Remove existing fill and stroke attributes from all elements
           const allElements = svgElement.querySelectorAll('*');
           allElements.forEach(element => {
               element.removeAttribute('fill');
               element.removeAttribute('stroke');
               // Also remove style-based fills and strokes
               const style = element.getAttribute('style');
               if (style) {
                   const newStyle = style
                       .replace(/fill:[^;]*;?/g, '')
                       .replace(/stroke:[^;]*;?/g, '')
                       .trim();
                   if (newStyle) {
                       element.setAttribute('style', newStyle);
                   } else {
                       element.removeAttribute('style');
                   }
               }
           });

           // Apply the new color to the root SVG element
           svgElement.setAttribute('fill', color);

           // Ensure the SVG has proper namespace
           if (!svgElement.hasAttribute('xmlns')) {
               svgElement.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
           }

           // Serialize back to string
           const serializer = new XMLSerializer();
           return serializer.serializeToString(svgDoc);

       } catch (error) {
           console.error('Error processing SVG:', error);
           // Fallback to simple string replacement if DOM parsing fails
           return applySVGColorFallback(svgText, color);
       }
   }

   function applySVGColorFallback(svgText, color) {
       // Fallback method using string replacement
       let coloredSvg = svgText;

       // Ensure SVG has proper namespace
       if (!coloredSvg.includes('xmlns=')) {
           coloredSvg = coloredSvg.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
       }

       // Remove existing fill attributes (more conservative approach)
       coloredSvg = coloredSvg.replace(/fill="[^"]*"/g, '');
       coloredSvg = coloredSvg.replace(/fill:[^;]*;?/g, '');

       // Add the new fill color to the root SVG element
       coloredSvg = coloredSvg.replace(/<svg([^>]*?)>/, `<svg$1 fill="${color}">`);

       return coloredSvg;
   }

   function applySVGGradient(svgText, gradientData) {
       try {
           console.log('🎨 Applying SVG gradient:', gradientData);

           // Parse the SVG to ensure it's valid XML
           const parser = new DOMParser();
           const svgDoc = parser.parseFromString(svgText, 'image/svg+xml');

           // Check for parsing errors
           const parserError = svgDoc.querySelector('parsererror');
           if (parserError) {
               console.error('SVG parsing error:', parserError.textContent);
               return svgText; // Return original if parsing fails
           }

           const svgElement = svgDoc.documentElement;

           // Create a unique gradient ID
           const gradientId = 'gradient_' + Math.random().toString(36).substring(2, 9);

           // Create defs element if it doesn't exist
           let defsElement = svgElement.querySelector('defs');
           if (!defsElement) {
               defsElement = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'defs');
               svgElement.insertBefore(defsElement, svgElement.firstChild);
           }

           // Create gradient element
           let gradientElement;
           if (gradientData.type === 'radial') {
               gradientElement = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'radialGradient');
               gradientElement.setAttribute('cx', '50%');
               gradientElement.setAttribute('cy', '50%');
               gradientElement.setAttribute('r', '50%');
           } else {
               gradientElement = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
               const angle = gradientData.gradient.angle || 0;

               // Convert angle to x1, y1, x2, y2 coordinates
               const angleRad = (angle * Math.PI) / 180;
               const cos = Math.cos(angleRad);
               const sin = Math.sin(angleRad);

               // Calculate gradient endpoints (0-100% coordinates)
               const x1 = 50 - 50 * cos;
               const y1 = 50 - 50 * sin;
               const x2 = 50 + 50 * cos;
               const y2 = 50 + 50 * sin;

               gradientElement.setAttribute('x1', `${x1}%`);
               gradientElement.setAttribute('y1', `${y1}%`);
               gradientElement.setAttribute('x2', `${x2}%`);
               gradientElement.setAttribute('y2', `${y2}%`);
           }

           gradientElement.setAttribute('id', gradientId);

           // Add color stops
           if (gradientData.gradient.colors) {
               gradientData.gradient.colors.forEach(colorStop => {
                   const stopElement = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'stop');
                   stopElement.setAttribute('offset', `${colorStop.position}%`);
                   stopElement.setAttribute('stop-color', colorStop.color);
                   gradientElement.appendChild(stopElement);
               });
           }

           // Add gradient to defs
           defsElement.appendChild(gradientElement);

           // Remove existing fill and stroke attributes from all elements
           const allElements = svgElement.querySelectorAll('*');
           allElements.forEach(element => {
               element.removeAttribute('fill');
               element.removeAttribute('stroke');
               // Also remove style-based fills and strokes
               const style = element.getAttribute('style');
               if (style) {
                   const newStyle = style
                       .replace(/fill:[^;]*;?/g, '')
                       .replace(/stroke:[^;]*;?/g, '')
                       .trim();
                   if (newStyle) {
                       element.setAttribute('style', newStyle);
                   } else {
                       element.removeAttribute('style');
                   }
               }
           });

           // Apply the gradient to the root SVG element
           svgElement.setAttribute('fill', `url(#${gradientId})`);

           // Ensure the SVG has proper namespace
           if (!svgElement.hasAttribute('xmlns')) {
               svgElement.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
           }

           // Serialize back to string
           const serializer = new XMLSerializer();
           const result = serializer.serializeToString(svgDoc);

           console.log('🎨 SVG gradient applied successfully');
           return result;

       } catch (error) {
           console.error('Error applying SVG gradient:', error);
           // Fallback to solid color using first color
           const fallbackColor = gradientData.gradient.colors[0].color;
           return applySVGColor(svgText, fallbackColor);
       }
   }

   // SVG Gradient Fill Function (same as text gradients)
   function applySVGGradientFill(ctx, obj, bounds) {
       console.log('🎨 APPLYING SVG GRADIENT FILL');
       console.log('%c🎨 SVG GRADIENT FILL: Starting gradient application', 'background-color: #335; color: #aff;');
       console.log('%c Object:', 'font-weight: bold;', obj);
       console.log('%c Bounds:', 'font-weight: bold;', bounds);

       // Check if we have the necessary data
       if (!obj || !obj.gradient || !obj.gradient.gradient || !obj.gradient.gradient.colors || obj.gradient.gradient.colors.length < 2) {
           console.error('%c🎨 SVG GRADIENT FILL: Missing gradient data!', 'background-color: #500; color: #fee;', {
               obj: obj ? 'Object exists' : 'No object',
               gradient: obj && obj.gradient ? JSON.stringify(obj.gradient) : 'No gradient'
           });

           // Fallback to solid fill
           ctx.fillStyle = obj.svgColor || '#ff0000';
           return false;
       }

       // Sort stops by position to ensure proper gradient rendering
       const sortedStops = [...obj.gradient.gradient.colors].sort((a, b) => a.position - b.position);

       console.log('%c🎨 SVG GRADIENT FILL: Sorted stops', 'background-color: #335; color: #aff;',
           JSON.stringify(sortedStops));

       // Create the appropriate gradient
       let gradient;

       try {
           if (obj.gradient.type === 'radial') {
               // Calculate center and radius for radial gradient
               const centerX = bounds.x + bounds.width / 2;
               const centerY = bounds.y + bounds.height / 2;
               const radius = Math.max(bounds.width, bounds.height) / 2;

               console.log('%c🎨 SVG GRADIENT FILL: Creating radial gradient', 'background-color: #335; color: #aff;', {
                   centerX,
                   centerY,
                   radius
               });

               gradient = ctx.createRadialGradient(
                   centerX, centerY, 0,
                   centerX, centerY, radius
               );
           } else {
               // Default to linear gradient
               const angle = obj.gradient.gradient.angle || 0;

               // Convert angle to gradient coordinates
               const angleRad = (angle * Math.PI) / 180;
               const cos = Math.cos(angleRad);
               const sin = Math.sin(angleRad);

               // Calculate gradient endpoints
               const startX = bounds.x + bounds.width / 2 - (bounds.width / 2) * cos;
               const startY = bounds.y + bounds.height / 2 - (bounds.height / 2) * sin;
               const endX = bounds.x + bounds.width / 2 + (bounds.width / 2) * cos;
               const endY = bounds.y + bounds.height / 2 + (bounds.height / 2) * sin;

               console.log('%c🎨 SVG GRADIENT FILL: Creating linear gradient', 'background-color: #335; color: #aff;', {
                   angle,
                   startX,
                   startY,
                   endX,
                   endY
               });

               gradient = ctx.createLinearGradient(startX, startY, endX, endY);
           }

           // Add color stops
           sortedStops.forEach(stop => {
               const offset = stop.position / 100; // Convert percentage to 0-1
               console.log('%c🎨 SVG GRADIENT FILL: Adding color stop', 'background-color: #335; color: #aff;', {
                   offset,
                   color: stop.color
               });
               gradient.addColorStop(offset, stop.color);
           });

           // Set the gradient as fill style
           ctx.fillStyle = gradient;

           console.log('%c🎨 SVG GRADIENT FILL: Gradient applied successfully!', 'background-color: #050; color: #afa;');
           return true;

       } catch (error) {
           console.error('%c🎨 SVG GRADIENT FILL: Error creating gradient', 'background-color: #500; color: #fee;', error);

           // Fallback to solid color using first color
           const fallbackColor = sortedStops[0].color;
           console.log('%c🎨 SVG GRADIENT FILL: Using fallback color', 'background-color: #550; color: #ffa;', fallbackColor);
           ctx.fillStyle = fallbackColor;
           return false;
       }
   }

   // Initialize gradient color pickers
   function initializeGradientColorPickers() {
       // Wait for GradientColorPicker to be loaded
       if (typeof GradientColorPicker === 'undefined') {
           console.log('GradientColorPicker not loaded yet, retrying...');
           setTimeout(initializeGradientColorPickers, 200);
           return;
       }

       console.log('🎨 Initializing gradient color pickers...');

       // Replace text color picker
       const textColorContainer = document.querySelector('#iTextColor').parentElement;
       if (textColorContainer) {
           const textGradientPicker = document.createElement('div');
           textGradientPicker.id = 'textGradientPicker';
           textColorContainer.appendChild(textGradientPicker);

           // Hide original color input
           document.querySelector('#iTextColor').style.display = 'none';

           const textPickerInstance = new GradientColorPicker(textGradientPicker, {
               defaultColor: '#FF0000',
               onChange: (colorData) => {
                   console.log('🎨 Text color changed:', colorData);
                   if (colorData.type === 'solid') {
                       console.log('🎨 Applying solid color:', colorData.value);
                       console.log('🎨 Selected object index:', selectedObjectIndex);
                       console.log('🎨 Selected object:', selectedObjectIndex !== -1 ? canvasObjects[selectedObjectIndex] : 'none');
                       updateSelectedObjectFromUI('color', colorData.value);
                       updateSelectedObjectFromUI('gradient', null); // Clear gradient when using solid color
                   } else {
                       console.log('🎨 Applying gradient:', colorData);
                       console.log('🎨 Selected object index:', selectedObjectIndex);
                       console.log('🎨 Selected object:', selectedObjectIndex !== -1 ? canvasObjects[selectedObjectIndex] : 'none');
                       // Handle gradient for text
                       updateSelectedObjectFromUI('gradient', colorData);
                       updateSelectedObjectFromUI('color', colorData.gradient.colors[0].color); // Set fallback color
                   }
               }
           });

           // Store instance reference for later access
           textGradientPicker.gradientPickerInstance = textPickerInstance;
       }

       // Replace SVG color picker
       const svgColorContainer = document.querySelector('#iImageColor').parentElement;
       if (svgColorContainer) {
           const svgGradientPicker = document.createElement('div');
           svgGradientPicker.id = 'svgGradientPicker';
           svgColorContainer.appendChild(svgGradientPicker);

           // Hide original color input
           document.querySelector('#iImageColor').style.display = 'none';

           new GradientColorPicker(svgGradientPicker, {
               defaultColor: '#000000',
               onChange: (colorData) => {
                   console.log('SVG color changed:', colorData);
                   if (colorData.type === 'solid') {
                       updateSelectedObjectFromUI('svgColor', colorData.value);
                       updateSelectedObjectFromUI('svgGradient', null); // Clear gradient when using solid color
                   } else {
                       console.log('🎨 Applying SVG gradient:', colorData);
                       updateSelectedObjectFromUI('svgGradient', colorData);
                       updateSelectedObjectFromUI('svgColor', colorData.gradient.colors[0].color); // Set fallback color
                   }
               }
           });
       }
   }

   function updateBodyClass(textObj) {
       if (!textObj || textObj.type !== 'text') {
           document.body.className = document.body.className.replace(/ (normal|warp|skew|circle|curve|grid-distort|horizontalLines|colorCut|obliqueLines|fadingLinesCut|stroke-enabled|shadow|block-shadow|perspective-shadow|line-shadow|detailed-3d|horizontal-skew|vertical-skew|triangle-warp-enabled)/g, '');
           return;
       }

       const effectMode = textObj.effectMode;
       const decorationMode = textObj.decorationMode;
       const shadowMode = textObj.shadowMode;
       const strokeMode = textObj.strokeMode;
       const isTriangleWarp = textObj.warpTriangle;

       let bodyClass = effectMode;

       if (decorationMode === 'horizontalLines') bodyClass += ' horizontalLines';
       else if (decorationMode === 'colorCut') bodyClass += ' colorCut';
       else if (decorationMode === 'obliqueLines') bodyClass += ' obliqueLines';
       else if (decorationMode === 'fadingLinesCut') bodyClass += ' fadingLinesCut';

       if (strokeMode === 'stroke') bodyClass += ' stroke-enabled';

       if (shadowMode === 'shadow') bodyClass += ' shadow';
       else if (shadowMode === 'blockShadow') bodyClass += ' block-shadow';
       else if (shadowMode === 'perspectiveShadow') bodyClass += ' perspective-shadow';
       else if (shadowMode === 'lineShadow') bodyClass += ' line-shadow';
       else if (shadowMode === 'detailed3D') bodyClass += ' detailed-3d';

       if (effectMode === 'warp' || effectMode === 'skew') {
           bodyClass += ' horizontal-skew';
       }

       if (effectMode === 'skew') {
           bodyClass += ' vertical-skew';
       }

       if (effectMode === 'warp' && isTriangleWarp) {
           bodyClass += ' triangle-warp-enabled';
       }

       if (effectMode === 'grid-distort') {
           bodyClass += ' grid-distort';
       }

       document.body.className = bodyClass.trim();
   }

   // --- Layering Functions ---
   function moveObjectForward() {
       if (selectedObjectIndex !== -1 && selectedObjectIndex < canvasObjects.length - 1) {
           const objToMove = canvasObjects.splice(selectedObjectIndex, 1)[0];
           canvasObjects.splice(selectedObjectIndex + 1, 0, objToMove);
           selectedObjectIndex++;
           updateUIFromSelectedObject(); // Update button states
           update();
       }
   }
   function moveObjectBackward() {
       if (selectedObjectIndex !== -1 && selectedObjectIndex > 0) {
           const objToMove = canvasObjects.splice(selectedObjectIndex, 1)[0];
           canvasObjects.splice(selectedObjectIndex - 1, 0, objToMove);
           selectedObjectIndex--;
           updateUIFromSelectedObject(); // Update button states
           update();
       }
   }

   // --- Font Preview ---
   function applyFontStylesToOptions() { const selectElement = document.getElementById('iFontFamily'); if (!selectElement) return; const options = selectElement.getElementsByTagName('option'); for (let option of options) { option.style.fontFamily = option.value; option.style.fontFamily += ', sans-serif'; } }

   // --- Shadow/Decoration Helpers --- (Full implementations exist but omitted for brevity)
   function applyBlockShadow(targetCtx, textObj, x, y) {
       // Generate a unique ID for this rendering pass to track in logs
       const renderPassId = Math.random().toString(36).substring(2, 8);

       console.log(`=== BLOCK SHADOW DEBUG START (${renderPassId}) ===`);
       console.log(`[${renderPassId}] Text object:`, textObj.text);
       console.log(`[${renderPassId}] Function called with:`, {
           x,
           y,
           hasPath: arguments.length > 3 && arguments[3] !== undefined,
           targetCtxType: targetCtx ? targetCtx.constructor.name : 'undefined'
       });

       try {
           // Always check if we should be using block shadow mode
           if (textObj.shadowMode !== 'blockShadow') {
               console.warn(`[${renderPassId}] WARNING: Called applyBlockShadow but shadowMode is not 'blockShadow', it's '${textObj.shadowMode}'`);
               // Don't return, continue with rendering anyway for debugging
           }

           // Check if textObj has all required properties
           if (!textObj.blockShadowColor || textObj.blockShadowOpacity === undefined ||
               textObj.blockShadowOffset === undefined || textObj.blockShadowAngle === undefined) {
               console.error(`[${renderPassId}] ERROR: Missing required block shadow properties:`, {
                   hasColor: !!textObj.blockShadowColor,
                   hasOpacity: textObj.blockShadowOpacity !== undefined,
                   hasOffset: textObj.blockShadowOffset !== undefined,
                   hasAngle: textObj.blockShadowAngle !== undefined
               });
           }

           const color = textObj.blockShadowColor || '#000000';
           const opacity = (textObj.blockShadowOpacity !== undefined) ? textObj.blockShadowOpacity / 100 : 1;
           const offset = textObj.blockShadowOffset || 40;
           const angleDeg = textObj.blockShadowAngle || -45;
           const blur = textObj.blockShadowBlur || 0;

           // IMPORTANT: Always check the perspective flag directly from the object
           let isPerspective = textObj.blockShadowPerspective || false;

           // Force perspective mode if it should be enabled
           if (textObj.shadowMode === 'blockShadow' && textObj.blockShadowPerspective) {
               console.log(`[${renderPassId}] FORCING PERSPECTIVE MODE - was:`, isPerspective);
               isPerspective = true;
           }

           const offsetCoords = calculateOffset(offset, angleDeg);

           console.log(`[${renderPassId}] Shadow properties:`, {
               color,
               opacity,
               offset,
               angleDeg,
               blur,
               isPerspective,
               perspectiveIntensity: textObj.blockShadowPerspectiveIntensity,
               shadowMode: textObj.shadowMode,
               offsetCoords
           });

           // Log the final state of the shadow properties
           console.log(`[${renderPassId}] Final shadow properties:`, {
               shadowMode: textObj.shadowMode,
               isPerspective: isPerspective,
               perspectiveIntensity: textObj.blockShadowPerspectiveIntensity
           });
       } catch (error) {
           console.error(`[${renderPassId}] ERROR in applyBlockShadow initial setup:`, error);
       }

       try {
           // Get shadow properties from the try block above
           const color = textObj.blockShadowColor || '#000000';
           const opacity = (textObj.blockShadowOpacity !== undefined) ? textObj.blockShadowOpacity / 100 : 1;
           const offset = textObj.blockShadowOffset || 40;
           const angleDeg = textObj.blockShadowAngle || -45;
           const blur = textObj.blockShadowBlur || 0;
           const offsetCoords = calculateOffset(offset, angleDeg);
           const isPerspective = textObj.blockShadowPerspective || false;

           targetCtx.save();
           setTextContextOn(targetCtx, textObj);
           targetCtx.fillStyle = hexToRgba(color, opacity);

           console.log(`[${renderPassId}] Canvas context state:`, {
               fillStyle: targetCtx.fillStyle,
               font: targetCtx.font,
               globalAlpha: targetCtx.globalAlpha
           });

           if (blur > 0) {
               targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
               targetCtx.shadowBlur = blur;
               targetCtx.shadowOffsetX = 0;
               targetCtx.shadowOffsetY = 0;
               console.log(`[${renderPassId}] Shadow blur applied:`, {
                   shadowColor: targetCtx.shadowColor,
                   shadowBlur: targetCtx.shadowBlur
               });
           }

           // Calculate number of steps based on offset
           // More steps for larger offsets to maintain smoothness
           const steps = Math.max(10, Math.floor(offset / 1.5));
           console.log(`[${renderPassId}] Number of shadow steps:`, steps);
       } catch (error) {
           console.error(`[${renderPassId}] ERROR in applyBlockShadow context setup:`, error);
       }

       try {
           // Get shadow properties again to ensure they're in scope
           const color = textObj.blockShadowColor || '#000000';
           const opacity = (textObj.blockShadowOpacity !== undefined) ? textObj.blockShadowOpacity / 100 : 1;
           const offset = textObj.blockShadowOffset || 40;
           const angleDeg = textObj.blockShadowAngle || -45;
           const blur = textObj.blockShadowBlur || 0;
           const offsetCoords = calculateOffset(offset, angleDeg);
           const isPerspective = textObj.blockShadowPerspective || false;
           const steps = Math.max(10, Math.floor(offset / 1.5));

           if (isPerspective) {
               console.log(`[${renderPassId}] Using PERSPECTIVE shadow mode`);
               // Completely redesigned perspective shadow effect
               // Get the perspective intensity (0-100) and convert to a scaling factor
               // Higher values create more dramatic perspective effects
               const perspectiveIntensity = (textObj.blockShadowPerspectiveIntensity || 50) / 100;

               // Calculate the minimum scale factor for the farthest shadow
               // This ensures that at maximum intensity, the farthest shadow can be very small
               // More dramatic scaling for higher intensity values
               const minScaleFactor = Math.max(0.02, 0.5 - (perspectiveIntensity * 0.48));

               console.log(`[${renderPassId}] Perspective intensity:`, textObj.blockShadowPerspectiveIntensity,
                          'Calculated factor:', perspectiveIntensity,
                          'Min scale factor:', minScaleFactor);

               // Draw shadows from back to front (important for proper layering)
               for (let i = steps; i >= 1; i--) {
                   const progress = i / steps;

                   // Calculate a more dramatic scaling factor that decreases as the shadow extends
                   // This creates a true perspective effect where distant shadows are much smaller
                   const distanceRatio = i / steps; // 1.0 at the start, approaching 0 at the end

                   // Apply a non-linear scaling based on the perspective intensity
                   // Higher intensity = more dramatic scaling at distance
                   // Use a much more aggressive scaling formula for a true perspective effect
                   // The exponent controls how quickly the shadow shrinks with distance
                   // Higher exponent values create more dramatic scaling (smaller distant shadows)

                   // Calculate the scale factor with a non-linear curve
                   // This creates a more dramatic perspective effect where distant shadows get much smaller
                   // Higher exponent values create more dramatic scaling (smaller distant shadows)
                   const exponent = 1 + perspectiveIntensity * 10;
                   let scaleProgress = Math.pow(distanceRatio, exponent);

                   // Apply the minimum scale factor to ensure the farthest shadow isn't too small
                   // This creates a smooth transition from 1.0 (closest) to minScaleFactor (farthest)
                   if (i === 1) { // For the farthest shadow
                       scaleProgress = minScaleFactor;
                   } else if (i < steps / 3) { // For shadows in the far third
                       // Blend between calculated scale and minimum scale for smoother transition
                       const blendFactor = (i - 1) / (steps / 3 - 1);
                       scaleProgress = minScaleFactor + (scaleProgress - minScaleFactor) * blendFactor;
                   }

                   if (i % 5 === 0 || i === 1 || i === steps) {
                       console.log(`[${renderPassId}] Shadow step ${i}/${steps}: distanceRatio=${distanceRatio.toFixed(2)}, scaleProgress=${scaleProgress.toFixed(3)}`);
                   }

                   // Calculate position for this shadow layer
                   // Multiply by offset to get the full distance
                   const posX = x + offsetCoords.x * progress * offset;
                   const posY = y + offsetCoords.y * progress * offset;

                   if (i % 5 === 0 || i === 1 || i === steps) {
                       console.log(`[${renderPassId}] Shadow position: (${posX.toFixed(1)}, ${posY.toFixed(1)}), scale: ${scaleProgress.toFixed(3)}`);
                   }

                   try {
                       // Save state before transformations
                       targetCtx.save();

                       // Move to the position where we'll draw this shadow layer
                       targetCtx.translate(posX, posY);

                       // Apply scaling for perspective effect
                       targetCtx.scale(scaleProgress, scaleProgress);

                       // Handle shadow blur
                       if (blur > 5 && i < steps) {
                           targetCtx.shadowColor = 'transparent';
                       }

                       // Draw the text at origin (0,0) since we've already translated
                       targetCtx.fillText((textObj.text || '').toUpperCase(), 0, 0);

                       // Restore shadow if needed
                       if (blur > 5 && i < steps) {
                           targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
                       }

                       // Restore the context state
                       targetCtx.restore();
                   } catch (error) {
                       console.error(`[${renderPassId}] ERROR in perspective shadow step ${i}:`, error);
                   }
               }
               console.log(`[${renderPassId}] === PERSPECTIVE SHADOW DEBUG END ===`);
           } else {
               // Standard block shadow mode
               console.log(`[${renderPassId}] Using STANDARD shadow mode (no perspective)`);

               try {
                   // Standard block shadow - uniform size
                   for (let i = steps; i >= 1; i--) {
                       const progress = i / steps;
                       // Multiply by offset to get the full distance
                       const currentX = x + offsetCoords.x * progress * offset;
                       const currentY = y + offsetCoords.y * progress * offset;

                       if (i % 5 === 0 || i === 1 || i === steps) {
                           console.log(`[${renderPassId}] Standard shadow step ${i}/${steps}: position=(${currentX.toFixed(1)}, ${currentY.toFixed(1)})`);
                       }

                       try {
                           if (blur > 5 && i < steps) {
                               targetCtx.shadowColor = 'transparent';
                           }

                           targetCtx.fillText((textObj.text || '').toUpperCase(), currentX, currentY);

                           if (blur > 5 && i < steps) {
                               targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
                           }
                       } catch (error) {
                           console.error(`[${renderPassId}] ERROR in standard shadow step ${i}:`, error);
                       }
                   }
                   console.log(`[${renderPassId}] === STANDARD SHADOW DEBUG END ===`);
               } catch (error) {
                   console.error(`[${renderPassId}] ERROR in standard shadow mode:`, error);
               }
           }
       } catch (error) {
           console.error(`[${renderPassId}] ERROR in shadow rendering:`, error);
       }

       // Always restore the context state
       try {
           targetCtx.restore();
           console.log(`[${renderPassId}] Context restored successfully`);
       } catch (error) {
           console.error(`[${renderPassId}] ERROR restoring context:`, error);
       }

       console.log(`[${renderPassId}] === BLOCK SHADOW FUNCTION END ===`);
   }
   function applyLineShadow(targetCtx, textObj, x, y) { const color = textObj.lineShadowColor; const distance = textObj.lineShadowDist; const angleDeg = textObj.lineShadowAngle; const thickness = Math.max(1, textObj.lineShadowThickness); const fullOffset = calculateOffset(distance, angleDeg); const cutterDistance = Math.max(0, distance - thickness); const cutterOffset = calculateOffset(cutterDistance, angleDeg); targetCtx.save(); setTextContextOn(targetCtx, textObj); targetCtx.fillStyle = color; targetCtx.fillText((textObj.text || '').toUpperCase(), x + fullOffset.x, y + fullOffset.y); targetCtx.globalCompositeOperation = 'destination-out'; targetCtx.fillStyle = 'black'; targetCtx.fillText((textObj.text || '').toUpperCase(), x + cutterOffset.x, y + cutterOffset.y); targetCtx.restore(); }

   // Apply perspective shadow to a path
   function applyPerspectiveShadow(targetCtx, textObj, x, y, path) {
       // Generate a unique ID for this rendering pass to track in logs
       const renderPassId = Math.random().toString(36).substring(2, 8);

       console.log(`=== PERSPECTIVE SHADOW START (${renderPassId}) ===`);
       console.log(`[${renderPassId}] Text object:`, textObj.text);
       console.log(`[${renderPassId}] Function called with:`, {
           x,
           y,
           hasPath: arguments.length > 3 && arguments[3] !== undefined,
           targetCtxType: targetCtx ? targetCtx.constructor.name : 'undefined'
       });

       try {
           // If no path is provided, use the standard text-based function
           if (!path) {
               // Use the original function for text objects
               const color = textObj.perspectiveShadowColor || '#000000';
               const opacity = (textObj.perspectiveShadowOpacity !== undefined) ? textObj.perspectiveShadowOpacity / 100 : 1;
               const offset = textObj.perspectiveShadowOffset || 40;
               const angleDeg = textObj.perspectiveShadowAngle || -58;
               const blur = textObj.perspectiveShadowBlur || 5;
               const perspectiveIntensity = (textObj.perspectiveShadowIntensity || 60) / 100;
               const offsetCoords = calculateOffset(offset, angleDeg);

               console.log(`[${renderPassId}] Perspective Shadow properties:`, {
                   color,
                   opacity,
                   offset,
                   angleDeg,
                   blur,
                   perspectiveIntensity,
                   shadowMode: textObj.shadowMode,
                   offsetCoords
               });

               targetCtx.save();
               setTextContextOn(targetCtx, textObj);
               targetCtx.fillStyle = hexToRgba(color, opacity);

               if (blur > 0) {
                   targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
                   targetCtx.shadowBlur = blur;
                   targetCtx.shadowOffsetX = 0;
                   targetCtx.shadowOffsetY = 0;
               }

               // Calculate number of steps based on offset
               // More steps for larger offsets to maintain smoothness
               const steps = Math.max(10, Math.floor(offset / 1.5));
               console.log(`[${renderPassId}] Number of shadow steps:`, steps);

               // Draw shadows from back to front (important for proper layering)
               for (let i = steps; i >= 1; i--) {
                   const progress = i / steps;

                   // Calculate a more dramatic scaling factor that decreases as the shadow extends
                   // This creates a true perspective effect where distant shadows are much smaller
                   const distanceRatio = i / steps; // 1.0 at the start, approaching 0 at the end

                   // Apply a non-linear scaling based on the perspective intensity
                   // Higher intensity = more dramatic scaling at distance
                   // The exponent controls how quickly the shadow shrinks with distance
                   const scaleProgress = Math.pow(distanceRatio, 1 + perspectiveIntensity * 6);

                   if (i % 5 === 0 || i === 1 || i === steps) {
                       console.log(`[${renderPassId}] Shadow step ${i}/${steps}: distanceRatio=${distanceRatio.toFixed(2)}, scaleProgress=${scaleProgress.toFixed(3)}`);
                   }

                   // Calculate position for this shadow layer
                   const posX = x + offsetCoords.x * progress * offset;
                   const posY = y + offsetCoords.y * progress * offset;

                   if (i % 5 === 0 || i === 1 || i === steps) {
                       console.log(`[${renderPassId}] Shadow position: (${posX.toFixed(1)}, ${posY.toFixed(1)}), scale: ${scaleProgress.toFixed(3)}`);
                   }

                   // Save state before transformations
                   targetCtx.save();

                   // Move to the position where we'll draw this shadow layer
                   targetCtx.translate(posX, posY);

                   // Apply scaling for perspective effect
                   targetCtx.scale(scaleProgress, scaleProgress);

                   // Handle shadow blur
                   if (blur > 5 && i < steps) {
                       targetCtx.shadowColor = 'transparent';
                   }

                   // Draw the text at origin (0,0) since we've already translated
                   targetCtx.fillText((textObj.text || '').toUpperCase(), 0, 0);

                   // Restore shadow if needed
                   if (blur > 5 && i < steps) {
                       targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
                   }

                   // Restore the context state
                   targetCtx.restore();
               }

               targetCtx.restore();
               console.log(`[${renderPassId}] === PERSPECTIVE SHADOW TEXT END ===`);
               return;
           }

           // Path-based perspective shadow implementation
           const color = textObj.perspectiveShadowColor || '#000000';
           const opacity = (textObj.perspectiveShadowOpacity !== undefined) ? textObj.perspectiveShadowOpacity / 100 : 1;
           const offset = textObj.perspectiveShadowOffset || 40;
           const angleDeg = textObj.perspectiveShadowAngle || -58;
           const blur = textObj.perspectiveShadowBlur || 5;
           const perspectiveIntensity = (textObj.perspectiveShadowIntensity || 60) / 100;
           const offsetCoords = calculateOffset(offset, angleDeg);

           console.log(`[${renderPassId}] Path-based perspective shadow properties:`, {
               color,
               opacity,
               offset,
               angleDeg,
               blur,
               perspectiveIntensity,
               shadowMode: textObj.shadowMode,
               offsetCoords
           });

           targetCtx.save();
           targetCtx.fillStyle = hexToRgba(color, opacity);

           if (blur > 0) {
               targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
               targetCtx.shadowBlur = blur;
               targetCtx.shadowOffsetX = 0;
               targetCtx.shadowOffsetY = 0;
           }

           // Calculate number of steps based on offset
           const steps = Math.max(10, Math.floor(offset / 1.5));

           // Create a Path2D object from the OpenType.js path if needed
           const shadowPath = path instanceof Path2D ? path : new Path2D();

           for (let i = steps; i >= 1; i--) {
               const progress = i / steps;
               const distanceRatio = i / steps;
               const scaleProgress = Math.pow(distanceRatio, 1 + perspectiveIntensity * 6);

               if (i % 5 === 0 || i === 1 || i === steps) {
                   console.log(`[${renderPassId}] Path shadow step ${i}/${steps}: scale=${scaleProgress.toFixed(3)}`);
               }

               targetCtx.save();

               // Invert the progress to get the correct offset
               // Farthest shadow (i=1) should have the largest offset
               // Closest shadow (i=steps) should have the smallest offset
               const offsetProgress = 1 - progress;

               // Apply both translation and scaling for perspective effect
               targetCtx.translate(offsetCoords.x * offsetProgress * offset, offsetCoords.y * offsetProgress * offset);
               targetCtx.scale(scaleProgress, scaleProgress);

               if (blur > 5 && i < steps) {
                   targetCtx.shadowColor = 'transparent';
               }

               targetCtx.fill(shadowPath);

               if (blur > 5 && i < steps) {
                   targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
               }

               targetCtx.restore();
           }

           targetCtx.restore();
           console.log(`[${renderPassId}] === PERSPECTIVE SHADOW PATH END ===`);
       } catch (error) {
           console.error(`[${renderPassId}] ERROR in perspective shadow:`, error);
       }
   }
   function applyDetailed3D_ExtrusionOnly(targetCtx, textObj, x, y) { const primaryColorRgba = hexToRgba(textObj.d3dPrimaryColor, textObj.d3dPrimaryOpacity / 100); const offset = textObj.d3dOffset; const angle = textObj.d3dAngle; const blur = textObj.d3dBlur; targetCtx.save(); setTextContextOn(targetCtx, textObj); const totalOffset = calculateOffset(offset, angle); const steps = Math.max(30, Math.floor(offset)); for (let i = steps; i >= 1; i--) { const progress = i / steps; const currentOffset = { x: totalOffset.x * progress, y: totalOffset.y * progress }; targetCtx.fillStyle = primaryColorRgba; targetCtx.fillText((textObj.text || '').toUpperCase(), x + currentOffset.x, y + currentOffset.y); } if (blur > 0) { targetCtx.save(); targetCtx.shadowColor = primaryColorRgba; targetCtx.shadowBlur = blur; targetCtx.shadowOffsetX = 0; targetCtx.shadowOffsetY = 0; targetCtx.fillStyle = primaryColorRgba; targetCtx.fillText((textObj.text || '').toUpperCase(), x + totalOffset.x, y + totalOffset.y); targetCtx.restore(); } targetCtx.restore(); }
   function applyDetailed3D_FrontOutline(targetCtx, textObj, x, y) { if (textObj.d3dSecondaryWidth <= 0) return; const secondaryColorRgba = hexToRgba(textObj.d3dSecondaryColor, textObj.d3dSecondaryOpacity / 100); targetCtx.save(); setTextContextOn(targetCtx, textObj); targetCtx.lineWidth = textObj.d3dSecondaryWidth; targetCtx.strokeStyle = secondaryColorRgba; targetCtx.lineJoin = 'round'; targetCtx.strokeText((textObj.text || '').toUpperCase(), x + textObj.d3dSecondaryOffsetX, y + textObj.d3dSecondaryOffsetY); targetCtx.restore(); }

   // Apply perspective shadow front outline
   function applyPerspectiveShadow_FrontOutline(targetCtx, textObj, x, y, path) {
       console.log('🔍 FRONT OUTLINE SOURCE #3: applyPerspectiveShadow_FrontOutline called');
       console.log('🔍 FRONT OUTLINE #3 DETAILS:', {
           outlineWidth: textObj.perspectiveShadowOutlineWidth,
           effectMode: textObj.effectMode,
           shadowMode: textObj.shadowMode,
           hasGradient: textObj.gradient && textObj.gradient.type !== 'solid',
           hasPath: !!path
       });

       // Skip if outline width is 0 or not set
       if (!textObj.perspectiveShadowOutlineWidth || textObj.perspectiveShadowOutlineWidth <= 0) {
           console.log('🔍 FRONT OUTLINE #3: Skipping - no outline width');
           return;
       }

       // Skip if we're using gradient masking (front outline handled separately)
       const isCircularWithGradient = (textObj.distortType === 'circular' || textObj.effectMode === 'circle') && textObj.gradient && textObj.gradient.type !== 'solid';
       const isGridDistortWithGradient = (textObj.effectMode === 'gridDistort') && textObj.gradient && textObj.gradient.type !== 'solid';
       const isCurvedWithGradient = (textObj.effectMode === 'curve') && textObj.gradient && textObj.gradient.type !== 'solid';
       const isMeshWarpWithGradient = (textObj.effectMode === 'meshWarp') && textObj.gradient && textObj.gradient.type !== 'solid';

       const usesGradientMasking = isCircularWithGradient || isGridDistortWithGradient || isCurvedWithGradient || isMeshWarpWithGradient;

       if (usesGradientMasking) {
           console.log('🔍 FRONT OUTLINE #3: Skipping applyPerspectiveShadow_FrontOutline (using gradient masking)');
           return;
       }

       console.log('🔍 FRONT OUTLINE #3: Proceeding with applyPerspectiveShadow_FrontOutline');

       // For grid distorted text, we need to draw the outline even if _outlineDrawn is true
       // because the grid distortion is rendered differently
       if (textObj._outlineDrawn && !path) {
           console.log("Skipping duplicate outline drawing for regular text");
           return;
       }

       // If this is a grid distorted text (path is provided), reset the flag to ensure it's drawn
       if (path) {
           textObj._outlineDrawn = false;
       }

       // Get outline properties
       const outlineColor = textObj.perspectiveShadowOutlineColor || '#00FF00';
       const outlineOpacity = (textObj.perspectiveShadowOutlineOpacity !== undefined) ?
           textObj.perspectiveShadowOutlineOpacity / 100 : 1.0;
       const outlineWidth = textObj.perspectiveShadowOutlineWidth || 4;
       const outlineOffsetX = textObj.perspectiveShadowOutlineOffsetX || -5;
       const outlineOffsetY = textObj.perspectiveShadowOutlineOffsetY || -5;

       // Add a debug ID to track this function call
       const debugId = Math.random().toString(36).substring(2, 8);
       console.log(`[${debugId}] Drawing perspective shadow outline: width=${outlineWidth}, color=${outlineColor}`);

       // Save context state
       targetCtx.save();

       // If a path is provided (for grid distorted text), use it
       if (path) {
           console.log(`[${debugId}] Using path-based approach for perspective shadow outline`);

           // Check if the path is an OpenType.js path or a Path2D object
           if (path.commands) {
               // It's an OpenType.js path
               console.log(`[${debugId}] Path is an OpenType.js path with ${path.commands.length} commands`);

               // First create a copy of the path with offset
               const offsetPath = new opentype.Path();

               // Apply the offset to each command
               for (let i = 0; i < path.commands.length; i++) {
                   const cmd = path.commands[i];
                   switch (cmd.type) {
                       case 'M':
                           offsetPath.moveTo(cmd.x + outlineOffsetX, cmd.y + outlineOffsetY);
                           break;
                       case 'L':
                           offsetPath.lineTo(cmd.x + outlineOffsetX, cmd.y + outlineOffsetY);
                           break;
                       case 'C':
                           // OpenType.js uses 'curveTo' for bezier curves
                           offsetPath.curveTo(
                               cmd.x1 + outlineOffsetX, cmd.y1 + outlineOffsetY,
                               cmd.x2 + outlineOffsetX, cmd.y2 + outlineOffsetY,
                               cmd.x + outlineOffsetX, cmd.y + outlineOffsetY
                           );
                           break;
                       case 'Q':
                           // OpenType.js uses 'quadTo' for quadratic curves
                           offsetPath.quadTo(
                               cmd.x1 + outlineOffsetX, cmd.y1 + outlineOffsetY,
                               cmd.x + outlineOffsetX, cmd.y + outlineOffsetY
                           );
                           break;
                       case 'Z':
                           offsetPath.closePath();
                           break;
                   }
               }

               // Now convert the OpenType.js path to a Canvas Path2D
               const outlinePath = new Path2D();
               for (let i = 0; i < offsetPath.commands.length; i++) {
                   const cmd = offsetPath.commands[i];
                   switch (cmd.type) {
                       case 'M':
                           outlinePath.moveTo(cmd.x, cmd.y);
                           break;
                       case 'L':
                           outlinePath.lineTo(cmd.x, cmd.y);
                           break;
                       case 'C':
                           outlinePath.bezierCurveTo(cmd.x1, cmd.y1, cmd.x2, cmd.y2, cmd.x, cmd.y);
                           break;
                       case 'Q':
                           outlinePath.quadraticCurveTo(cmd.x1, cmd.y1, cmd.x, cmd.y);
                           break;
                       case 'Z':
                           outlinePath.closePath();
                           break;
                   }
               }

               // Set up the stroke style for the outline
               targetCtx.strokeStyle = hexToRgba(outlineColor, outlineOpacity);
               targetCtx.lineWidth = outlineWidth;
               targetCtx.lineJoin = 'round';
               targetCtx.lineCap = 'round';

               // Draw the outline
               targetCtx.stroke(outlinePath);
           } else {
               // It's a Path2D object or something else
               console.log(`[${debugId}] Path is not an OpenType.js path, trying direct approach`);

               // Create a Path2D object for the outline
               const outlinePath = new Path2D();
               for (let i = 0; i < path.commands.length; i++) {
                   const cmd = path.commands[i];
                   switch (cmd.type) {
                       case 'M':
                           outlinePath.moveTo(cmd.x + outlineOffsetX, cmd.y + outlineOffsetY);
                           break;
                       case 'L':
                           outlinePath.lineTo(cmd.x + outlineOffsetX, cmd.y + outlineOffsetY);
                           break;
                       case 'C':
                           outlinePath.bezierCurveTo(
                               cmd.x1 + outlineOffsetX, cmd.y1 + outlineOffsetY,
                               cmd.x2 + outlineOffsetX, cmd.y2 + outlineOffsetY,
                               cmd.x + outlineOffsetX, cmd.y + outlineOffsetY
                           );
                           break;
                       case 'Q':
                           outlinePath.quadraticCurveTo(
                               cmd.x1 + outlineOffsetX, cmd.y1 + outlineOffsetY,
                               cmd.x + outlineOffsetX, cmd.y + outlineOffsetY
                           );
                           break;
                       case 'Z':
                           outlinePath.closePath();
                           break;
                   }
               }

               // Set up the stroke style for the outline
               targetCtx.strokeStyle = hexToRgba(outlineColor, outlineOpacity);
               targetCtx.lineWidth = outlineWidth;
               targetCtx.lineJoin = 'round';
               targetCtx.lineCap = 'round';

               // Draw the outline
               targetCtx.stroke(outlinePath);
           }
       } else {
           // For regular text, use the standard text-based approach
           // Set up text context
           setTextContextOn(targetCtx, textObj);

           // Set up the stroke style for the outline
           targetCtx.strokeStyle = hexToRgba(outlineColor, outlineOpacity);
           targetCtx.lineWidth = outlineWidth;
           targetCtx.lineJoin = 'round';
           targetCtx.lineCap = 'round';

           // Draw the text outline with offsets
           targetCtx.strokeText(
               (textObj.text || '').toUpperCase(),
               x + outlineOffsetX,
               y + outlineOffsetY
           );
       }

       // Mark this outline as drawn
       textObj._outlineDrawn = true;

       // Restore context state
       targetCtx.restore();
       console.log('🔍 FRONT OUTLINE SOURCE #3: applyPerspectiveShadow_FrontOutline COMPLETED');
   }

   // --- Master Styling Functions ---
   // **** REMOVED DEBUG LOG ****
   function renderStyledObjectToOffscreen(obj, targetCtx, targetCanvasWidth, targetCanvasHeight) {
       // Clear the entire canvas
       targetCtx.clearRect(0, 0, targetCanvasWidth, targetCanvasHeight);

       // Reset the outline drawn flag at the start of each render cycle
       obj._outlineDrawn = false;

       targetCtx.save();

       // Calculate center position
       const centerX = targetCanvasWidth / 2;
       const centerY = targetCanvasHeight / 2;

       // Set font and measure text
       setTextContextOn(targetCtx, obj);
       const text = (obj.text || '').toUpperCase();
       const metrics = targetCtx.measureText(text);

       // Calculate text dimensions with extra padding for effects
       // Use more generous estimates for ascent/descent to prevent clipping
       const ascent = metrics.actualBoundingBoxAscent || obj.fontSize * 1.0;
       const descent = metrics.actualBoundingBoxDescent || obj.fontSize * 0.4;
       const textHeight = ascent + descent;

       // Calculate top edge with extra padding
       const topEdgeY = centerY - textHeight / 2;

       // Apply opacity if specified (default is 100%)
       const opacity = (obj.opacity !== undefined) ? obj.opacity / 100 : 1;

       // Get the current fill style (which may be a gradient set by setTextContextOn)
       let mainFillStyle = targetCtx.fillStyle;

       // If opacity is less than 100%, apply it via globalAlpha instead of converting gradients to colors
       if (opacity < 1) {
           // Check if fillStyle is a gradient object (CanvasGradient)
           if (mainFillStyle && typeof mainFillStyle === 'object' && mainFillStyle.constructor.name === 'CanvasGradient') {
               // For gradients, use globalAlpha to preserve the gradient
               console.log('🎨 Preserving gradient with opacity:', opacity);
               // Don't modify mainFillStyle, just apply opacity via globalAlpha later
           } else {
               // For solid colors, convert to rgba
               if (mainFillStyle.startsWith('#')) {
                   mainFillStyle = hexToRgba(mainFillStyle, opacity);
               }
               // If it's already rgba, modify the alpha
               else if (mainFillStyle.startsWith('rgba')) {
                   const rgbaMatch = mainFillStyle.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([.\d]+)\)/);
                   if (rgbaMatch) {
                       const [_, r, g, b] = rgbaMatch;
                       mainFillStyle = `rgba(${r}, ${g}, ${b}, ${opacity})`;
                   }
               }
               // If it's rgb, convert to rgba
               else if (mainFillStyle.startsWith('rgb')) {
                   const rgbMatch = mainFillStyle.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
                   if (rgbMatch) {
                       const [_, r, g, b] = rgbMatch;
                       mainFillStyle = `rgba(${r}, ${g}, ${b}, ${opacity})`;
                   }
               }
           }
       }
        if (obj.decorationMode === "fadingLinesCut") { const distancePercent = obj.flcDist / 100; const direction = obj.flcDir; const lineColor = obj.flcColor; const lineWeight = obj.flcWeight; const lineSpacing = obj.flcSpacing; const baseTextColor = obj.color; const patternCanvas = document.createElement("canvas"); const patternWidth = 10; const patternHeight = Math.max(20, Math.ceil(textHeight) * 2); patternCanvas.width = patternWidth; patternCanvas.height = patternHeight; const pCtx = patternCanvas.getContext("2d"); const cutLineYRelative = patternHeight * distancePercent; pCtx.fillStyle = baseTextColor; pCtx.fillRect(0, 0, patternWidth, patternHeight); pCtx.fillStyle = lineColor; for (let y = 0; y < patternHeight; y += lineSpacing) { const lineCenterY = y + lineSpacing / 2; let drawLine = false; if (direction === 'top') { if (lineCenterY > cutLineYRelative) drawLine = true; } else { if (lineCenterY < cutLineYRelative) drawLine = true; } if (drawLine) { const lineDrawY = lineCenterY - lineWeight / 2; pCtx.fillRect(0, lineDrawY, patternWidth, lineWeight); } } pCtx.fillStyle = baseTextColor; if (direction === 'top') { pCtx.fillRect(0, 0, patternWidth, cutLineYRelative); } else { pCtx.fillRect(0, cutLineYRelative, patternWidth, patternHeight - cutLineYRelative); } try { mainFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); mainFillStyle.setTransform(patternTransform); } catch (e) { console.error("Error creating FLC pattern:", e); mainFillStyle = obj.color; } }
        else if (obj.decorationMode === "horizontalLines") { const weight = obj.hLineWeight; const distance = obj.hLineDist; const color = obj.hLineColor; const baseColor = obj.color; const totalHeight = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = 10; patternCanvas.height = totalHeight; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, 10, totalHeight); pCtx.fillStyle = color; pCtx.fillRect(0, 0, 10, weight); try { mainFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); mainFillStyle.setTransform(patternTransform); } catch (e) { console.error("Error creating HLines pattern:", e); mainFillStyle = obj.color; } }
        else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const color = obj.ccColor; const baseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), baseColor); gradient.addColorStop(1, baseColor); } else { gradient.addColorStop(0, baseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), baseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
        else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const color = obj.oLineColor; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i + size + distance, size + distance); } pCtx.stroke(); try { mainFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); mainFillStyle.setTransform(patternTransform); } catch (e) { console.error("Error creating OLines pattern:", e); mainFillStyle = obj.color; } }
       if (obj.shadowMode === "blockShadow") {
           applyBlockShadow(targetCtx, obj, centerX, centerY);
       } else if (obj.shadowMode === "perspectiveShadow") {
           console.log("Applying perspective shadow in renderStyledObjectToOffscreen");
           applyPerspectiveShadow(targetCtx, obj, centerX, centerY);
       } else if (obj.shadowMode === "lineShadow") {
           applyLineShadow(targetCtx, obj, centerX, centerY);
       } else if (obj.shadowMode === "detailed3D") {
           applyDetailed3D_ExtrusionOnly(targetCtx, obj, centerX, centerY);
       }
       targetCtx.save(); if (obj.shadowMode === "shadow") { targetCtx.shadowColor = obj.shadowColor; targetCtx.shadowOffsetX = obj.shadowOffsetX; targetCtx.shadowOffsetY = obj.shadowOffsetY; targetCtx.shadowBlur = obj.shadowBlur; }

       // Apply opacity for gradients via globalAlpha
       if (opacity < 1 && mainFillStyle && typeof mainFillStyle === 'object' && mainFillStyle.constructor.name === 'CanvasGradient') {
           targetCtx.globalAlpha = opacity;
           console.log('🎨 Applied globalAlpha for gradient:', opacity);
       }

       // GRADIENT TEST: Create a simple test gradient to verify coordinates
       if (obj.gradient && obj.gradient.type !== 'solid') {
           console.log('🎨 GRADIENT TEST: Text position and size details');
           console.log('🎨 GRADIENT TEST: centerX:', centerX, 'centerY:', centerY);
           console.log('🎨 GRADIENT TEST: text:', text);
           console.log('🎨 GRADIENT TEST: obj.x:', obj.x, 'obj.y:', obj.y);
           console.log('🎨 GRADIENT TEST: obj.fontSize:', obj.fontSize);

           // Measure text to understand its bounds
           const metrics = targetCtx.measureText(text);
           console.log('🎨 GRADIENT TEST: text metrics:', {
               width: metrics.width,
               actualBoundingBoxLeft: metrics.actualBoundingBoxLeft,
               actualBoundingBoxRight: metrics.actualBoundingBoxRight,
               actualBoundingBoxAscent: metrics.actualBoundingBoxAscent,
               actualBoundingBoxDescent: metrics.actualBoundingBoxDescent
           });

           // Calculate text bounds relative to drawing position
           const textLeft = centerX - metrics.width / 2;
           const textRight = centerX + metrics.width / 2;
           const textTop = centerY - obj.fontSize / 2;
           const textBottom = centerY + obj.fontSize / 2;

           console.log('🎨 GRADIENT TEST: calculated text bounds:', {
               left: textLeft,
               right: textRight,
               top: textTop,
               bottom: textBottom,
               width: textRight - textLeft,
               height: textBottom - textTop
           });

           // Create the actual user gradient based on type
           const gradientType = obj.gradient.type;
           console.log('🎨 GRADIENT: Type:', gradientType);

           // Calculate text bounds for gradient positioning
           const textWidth = textRight - textLeft;
           const textHeight = textBottom - textTop;
           const centerTextX = (textLeft + textRight) / 2;
           const centerTextY = (textTop + textBottom) / 2;

           let userGradient;

           if (gradientType === 'radial') {
               console.log('🎨 GRADIENT: Creating radial gradient');

               // For radial gradients, create a circle that covers the text
               const maxDimension = Math.max(textWidth, textHeight);
               const radius = maxDimension * 0.6; // Smaller radius for better effect

               console.log('🎨 GRADIENT: Radial parameters:', {
                   centerX: centerTextX,
                   centerY: centerTextY,
                   radius: radius,
                   textWidth: textWidth,
                   textHeight: textHeight
               });

               // Create radial gradient from center outward
               userGradient = targetCtx.createRadialGradient(
                   centerTextX, centerTextY, 0,        // Inner circle (center, radius 0)
                   centerTextX, centerTextY, radius    // Outer circle (center, calculated radius)
               );

           } else {
               console.log('🎨 GRADIENT: Creating linear gradient');

               // Linear gradient with angle support
               const uiAngle = obj.gradient.gradient.angle || 0;
               console.log('🎨 GRADIENT: UI angle:', uiAngle);

               // Convert UI angle to radians (0° = horizontal left-to-right, 90° = vertical top-to-bottom)
               const angleRad = (uiAngle * Math.PI) / 180;
               const cos = Math.cos(angleRad);
               const sin = Math.sin(angleRad);

               console.log('🎨 GRADIENT: angle calculations:', { angleRad, cos, sin });

               // Calculate gradient line length to ensure it covers the entire text
               const maxDimension = Math.max(textWidth, textHeight);
               const gradientLength = maxDimension * 1.5; // Extra length to ensure full coverage

               // Calculate gradient endpoints
               const x1 = centerTextX - (gradientLength / 2) * cos;
               const y1 = centerTextY - (gradientLength / 2) * sin;
               const x2 = centerTextX + (gradientLength / 2) * cos;
               const y2 = centerTextY + (gradientLength / 2) * sin;

               console.log('🎨 GRADIENT: final coordinates:', { x1, y1, x2, y2 });

               userGradient = targetCtx.createLinearGradient(x1, y1, x2, y2);
           }

           // Add user color stops
           if (obj.gradient.gradient.colors) {
               obj.gradient.gradient.colors.forEach(colorStop => {
                   userGradient.addColorStop(colorStop.position / 100, colorStop.color);
                   console.log('🎨 GRADIENT: Added user color stop:', colorStop);
               });
           }

           targetCtx.fillStyle = userGradient;
           console.log('🎨 GRADIENT: Applied', gradientType, 'gradient');
       } else {
           targetCtx.fillStyle = mainFillStyle; // Set the determined fill style
       }

       // DEBUG: Log what we're actually drawing with
       // Draw stroke first if enabled (before text fill) - outward only
       if (obj.strokeMode === 'stroke' && obj.strokeWidth > 0) {
           // Apply stroke with independent opacity - outward only like shapes
           const strokeOpacity = (obj.strokeOpacity !== undefined) ? obj.strokeOpacity / 100 : 1;
           const strokeColor = hexToRgba(obj.strokeColor, strokeOpacity);

           // Create a temporary canvas for outward-only stroke
           const strokeCanvas = document.createElement('canvas');
           strokeCanvas.width = targetCtx.canvas.width;
           strokeCanvas.height = targetCtx.canvas.height;
           const strokeCtx = strokeCanvas.getContext('2d');

           // Disable image smoothing for sharp strokes
           strokeCtx.imageSmoothingEnabled = false;

           // Set up stroke context to match the main text exactly
           strokeCtx.font = targetCtx.font; // Use the exact same font as the main text
           strokeCtx.textAlign = targetCtx.textAlign;
           strokeCtx.textBaseline = targetCtx.textBaseline;
           strokeCtx.globalAlpha = 1;
           strokeCtx.strokeStyle = strokeColor;
           // Use a much thicker stroke that will be partially cut out
           const effectiveStrokeWidth = Math.max(obj.strokeWidth * 8, 20); // 8x multiplier with 20px minimum
           console.log('🎨 STROKE DEBUG - Original width:', obj.strokeWidth, 'Effective width:', effectiveStrokeWidth);
           console.log('🎨 STROKE DEBUG - Font used:', strokeCtx.font);
           strokeCtx.lineWidth = effectiveStrokeWidth;
           strokeCtx.lineJoin = 'round';
           strokeCtx.lineCap = 'round';

           // Draw the thick stroke
           strokeCtx.strokeText(text, centerX, centerY);

           // Use destination-out to cut out a much smaller interior area
           strokeCtx.globalCompositeOperation = 'destination-out';
           strokeCtx.fillStyle = 'black'; // Color doesn't matter for destination-out

           // Use the same font size for the cutout to create proper outline
           strokeCtx.fillText(text, centerX, centerY);

           // Draw the outward-only stroke to the target canvas with independent opacity
           targetCtx.save();
           // Save current globalAlpha and reset it for stroke rendering
           const currentGlobalAlpha = targetCtx.globalAlpha;
           targetCtx.globalAlpha = 1; // Use full opacity for stroke (stroke already has its own opacity applied)
           targetCtx.drawImage(strokeCanvas, 0, 0);
           // Restore the original globalAlpha for text rendering
           targetCtx.globalAlpha = currentGlobalAlpha;
           targetCtx.restore();
       }

       console.log('🎨 About to draw text with fillStyle:', typeof targetCtx.fillStyle, targetCtx.fillStyle);

       targetCtx.fillText(text, centerX, centerY);
       targetCtx.restore();

       // REMOVED: All old front outline logic from renderStyledObjectToOffscreen
       // Front outlines are now ONLY handled by the gradient masking system for ALL effects
       console.log('🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only');
       targetCtx.restore();
       // Return calculated metrics
       return {
           width: metrics.width,
           height: textHeight,
           ascent: ascent,
           descent: descent,
           centerX: centerX, // Center X on the offscreen canvas
           centerY: centerY  // Center Y on the offscreen canvas
       };
   }
   function renderSingleStyledLetter(obj, letter, targetCtx, targetCanvasWidth, targetCanvasHeight, letterIndex = null, totalLetters = null) {
       // Clear the entire canvas
       targetCtx.clearRect(0, 0, targetCanvasWidth, targetCanvasHeight);

       // Reset the outline drawn flag at the start of each render cycle
       obj._outlineDrawn = false;

       targetCtx.save();

       // Calculate center position
       const centerX = targetCanvasWidth / 2;
       const centerY = targetCanvasHeight / 2;

       // Set font and measure text, passing letter index for gradient calculation
       setTextContextOn(targetCtx, obj, letterIndex, totalLetters);
       const metrics = targetCtx.measureText(letter);

       // Use more generous estimates for ascent/descent to prevent clipping
       const ascent = metrics.actualBoundingBoxAscent || obj.fontSize * 1.0;
       const descent = metrics.actualBoundingBoxDescent || obj.fontSize * 0.4;
       const textHeight = ascent + descent;

       // Calculate top edge with extra padding
       const topEdgeY = centerY - textHeight / 2;

       // Apply opacity if specified (default is 100%)
       const opacity = (obj.opacity !== undefined) ? obj.opacity / 100 : 1;

       // Get the current fill style (which may be a gradient set by setTextContextOn)
       let letterFillStyle = targetCtx.fillStyle;

       // If opacity is less than 100%, apply it via globalAlpha instead of converting gradients to colors
       if (opacity < 1) {
           // Check if fillStyle is a gradient object (CanvasGradient)
           if (letterFillStyle && typeof letterFillStyle === 'object' && letterFillStyle.constructor.name === 'CanvasGradient') {
               // For gradients, use globalAlpha to preserve the gradient
               console.log('🎨 Preserving letter gradient with opacity:', opacity);
               // Don't modify letterFillStyle, just apply opacity via globalAlpha later
           } else {
               // For solid colors, convert to rgba
               if (letterFillStyle.startsWith('#')) {
                   letterFillStyle = hexToRgba(letterFillStyle, opacity);
               }
               // If it's already rgba, modify the alpha
               else if (letterFillStyle.startsWith('rgba')) {
                   const rgbaMatch = letterFillStyle.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([.\d]+)\)/);
                   if (rgbaMatch) {
                       const [_, r, g, b] = rgbaMatch;
                       letterFillStyle = `rgba(${r}, ${g}, ${b}, ${opacity})`;
                   }
               }
               // If it's rgb, convert to rgba
               else if (letterFillStyle.startsWith('rgb')) {
                   const rgbMatch = letterFillStyle.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
                   if (rgbMatch) {
                       const [_, r, g, b] = rgbMatch;
                       letterFillStyle = `rgba(${r}, ${g}, ${b}, ${opacity})`;
                   }
               }
           }
       } if (obj.decorationMode === "fadingLinesCut") { const distancePercent = obj.flcDist / 100; const direction = obj.flcDir; const lineColor = obj.flcColor; const lineWeight = obj.flcWeight; const lineSpacing = obj.flcSpacing; const baseTextColor = obj.color; const patternCanvas = document.createElement("canvas"); const patternWidth = 10; const patternHeight = Math.max(20, Math.ceil(textHeight) * 2); patternCanvas.width = patternWidth; patternCanvas.height = patternHeight; const pCtx = patternCanvas.getContext("2d"); const cutLineYRelative = patternHeight * distancePercent; pCtx.fillStyle = baseTextColor; pCtx.fillRect(0, 0, patternWidth, patternHeight); pCtx.fillStyle = lineColor; for (let y = 0; y < patternHeight; y += lineSpacing) { const lineCenterY = y + lineSpacing / 2; let drawLine = false; if (direction === 'top') { if (lineCenterY > cutLineYRelative) drawLine = true; } else { if (lineCenterY < cutLineYRelative) drawLine = true; } if (drawLine) { const lineDrawY = lineCenterY - lineWeight / 2; pCtx.fillRect(0, lineDrawY, patternWidth, lineWeight); } } pCtx.fillStyle = baseTextColor; if (direction === 'top') { pCtx.fillRect(0, 0, patternWidth, cutLineYRelative); } else { pCtx.fillRect(0, cutLineYRelative, patternWidth, patternHeight - cutLineYRelative); } try { letterFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); letterFillStyle.setTransform(patternTransform); } catch (e) { console.error("Error creating FLC pattern for letter:", e); letterFillStyle = obj.color; } } else if (obj.decorationMode === "horizontalLines") { const weight = obj.hLineWeight; const distance = obj.hLineDist; const color = obj.hLineColor; const baseColor = obj.color; const totalHeight = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = 10; patternCanvas.height = totalHeight; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, 10, totalHeight); pCtx.fillStyle = color; pCtx.fillRect(0, 0, 10, weight); try { letterFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); letterFillStyle.setTransform(patternTransform); } catch (e) { letterFillStyle = obj.color; } } else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const color = obj.ccColor; const baseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), baseColor); gradient.addColorStop(1, baseColor); } else { gradient.addColorStop(0, baseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), baseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const color = obj.oLineColor; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i + size + distance, size + distance); } pCtx.stroke(); try { letterFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); letterFillStyle.setTransform(patternTransform); } catch (e) { letterFillStyle = obj.color; } } const letterObj = { ...obj, text: letter };
       if (obj.shadowMode === "blockShadow") applyBlockShadow(targetCtx, letterObj, centerX, centerY);
       if (obj.shadowMode === "perspectiveShadow") applyPerspectiveShadow(targetCtx, letterObj, centerX, centerY);
       if (obj.shadowMode === "lineShadow") applyLineShadow(targetCtx, letterObj, centerX, centerY);
       if (obj.shadowMode === "detailed3D") applyDetailed3D_ExtrusionOnly(targetCtx, letterObj, centerX, centerY);

       targetCtx.save();
       if (obj.shadowMode === "shadow") {
           targetCtx.shadowColor = obj.shadowColor;
           targetCtx.shadowOffsetX = obj.shadowOffsetX;
           targetCtx.shadowOffsetY = obj.shadowOffsetY;
           targetCtx.shadowBlur = obj.shadowBlur;
       }

       // Apply opacity for gradients via globalAlpha
       if (opacity < 1 && letterFillStyle && typeof letterFillStyle === 'object' && letterFillStyle.constructor.name === 'CanvasGradient') {
           targetCtx.globalAlpha = opacity;
           console.log('🎨 Applied globalAlpha for letter gradient:', opacity);
       }

       // Draw stroke first if enabled (before letter fill) - outward only
       if (obj.strokeMode === 'stroke' && obj.strokeWidth > 0) {
           // Apply stroke with independent opacity - outward only like shapes
           const strokeOpacity = (obj.strokeOpacity !== undefined) ? obj.strokeOpacity / 100 : 1;
           const strokeColor = hexToRgba(obj.strokeColor, strokeOpacity);

           // Create a temporary canvas for outward-only stroke
           const strokeCanvas = document.createElement('canvas');
           strokeCanvas.width = targetCtx.canvas.width;
           strokeCanvas.height = targetCtx.canvas.height;
           const strokeCtx = strokeCanvas.getContext('2d');

           // Disable image smoothing for sharp strokes
           strokeCtx.imageSmoothingEnabled = false;

           // Set up stroke context to match the main text exactly
           strokeCtx.font = targetCtx.font; // Use the exact same font as the main text
           strokeCtx.textAlign = targetCtx.textAlign;
           strokeCtx.textBaseline = targetCtx.textBaseline;
           strokeCtx.globalAlpha = 1;
           strokeCtx.strokeStyle = strokeColor;
           // Use a much thicker stroke that will be partially cut out
           const effectiveStrokeWidth = Math.max(obj.strokeWidth * 8, 20); // 8x multiplier with 20px minimum
           strokeCtx.lineWidth = effectiveStrokeWidth;
           strokeCtx.lineJoin = 'round';
           strokeCtx.lineCap = 'round';

           // Draw the thick stroke
           strokeCtx.strokeText(letter, centerX, centerY);

           // Use destination-out to cut out a much smaller interior area
           strokeCtx.globalCompositeOperation = 'destination-out';
           strokeCtx.fillStyle = 'black'; // Color doesn't matter for destination-out

           // Use the same font size for the cutout to create proper outline
           strokeCtx.fillText(letter, centerX, centerY);

           // Draw the outward-only stroke to the target canvas with independent opacity
           targetCtx.save();
           // Save current globalAlpha and reset it for stroke rendering
           const currentGlobalAlpha = targetCtx.globalAlpha;
           targetCtx.globalAlpha = 1; // Use full opacity for stroke (stroke already has its own opacity applied)
           targetCtx.drawImage(strokeCanvas, 0, 0);
           // Restore the original globalAlpha for text rendering
           targetCtx.globalAlpha = currentGlobalAlpha;
           targetCtx.restore();
       }

       targetCtx.fillStyle = letterFillStyle;
       targetCtx.fillText(letter, centerX, centerY);
       targetCtx.restore();

       // Apply front outlines for different shadow modes
       // Skip front outlines for effects with gradients that use gradient masking
       const isCircularWithGradient = (obj.distortType === 'circular' || obj.effectMode === 'circle') && obj.gradient && obj.gradient.type !== 'solid';
       const isGridDistortWithGradient = (obj.effectMode === 'gridDistort') && obj.gradient && obj.gradient.type !== 'solid';
       const isCurvedWithGradient = (obj.effectMode === 'curve') && obj.gradient && obj.gradient.type !== 'solid';
       const isMeshWarpWithGradient = (obj.effectMode === 'meshWarp') && obj.gradient && obj.gradient.type !== 'solid';

       // REMOVED: All front outline logic from renderSingleStyledLetter
       // Front outlines are now ONLY handled by the gradient masking system
       console.log('🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only');

       targetCtx.restore();
       return {
           width: metrics.width,
           height: textHeight,
           ascent: ascent,
           descent: descent,
           centerX: centerX,
           centerY: centerY
       }; }

   // --- Text Effect Rendering Logic ---
   // Draw text with normal or skew effect
   function drawNormalOrSkewObject(obj, targetCtx) {
       // Check if we need to use letter spacing
       const letterSpacing = obj._effectiveLetterSpacing || 0;

       if (letterSpacing === 0) {
           // If no letter spacing, use the standard rendering approach
           // Render the text to the offscreen canvas
           const metrics = renderStyledObjectToOffscreen(obj, octx, os.width, os.height);

           try {
               // Calculate source and destination rectangles based on metrics
               // Use much larger padding to accommodate shadows, strokes, and italic text
               // that might extend beyond the measured text bounds
               const effectsPadding = Math.max(
                   letterSourcePadding * 4,                // Base padding
                   obj.fontSize * 0.5,                     // Scale with font size
                   obj.shadowOffsetX + obj.shadowBlur * 2, // Account for shadow
                   obj.d3dOffset + obj.d3dBlur * 2,        // Account for 3D effects
                   obj.blockShadowOffset * 1.5,            // Account for block shadow
                   obj.strokeWidth * 3                     // Account for stroke
               );

               // Calculate source rectangle with generous padding
               const sw = metrics.width + effectsPadding * 2;
               const sh = metrics.height + effectsPadding * 2;
               const sx = metrics.centerX - sw / 2;
               const sy = metrics.centerY - sh / 2;

               // Destination rectangle matches source size
               const dw = sw;
               const dh = sh;
               const dx = -dw / 2; // Draw centered in the target context
               const dy = -dh / 2;

               // Ensure source rect is within bounds of the offscreen canvas
               if (sx >= 0 && sy >= 0 && sx + sw <= os.width && sy + sh <= os.height && sw > 0 && sh > 0) {
                   // Draw the text from offscreen canvas to the target canvas
                   targetCtx.drawImage(os, sx, sy, sw, sh, dx, dy, dw, dh);
               } else {
                   console.warn("Source rectangle out of bounds, using fallback drawing method.",
                       {sx, sy, sw, sh, osWidth: os.width, osHeight: os.height});

                   // Fallback: Use a centered portion of the offscreen canvas
                   // This ensures we at least get the center of the text
                   const safeWidth = Math.min(os.width * 0.8, sw);
                   const safeHeight = Math.min(os.height * 0.8, sh);
                   const safeSx = (os.width - safeWidth) / 2;
                   const safeSy = (os.height - safeHeight) / 2;

                   targetCtx.drawImage(os, safeSx, safeSy, safeWidth, safeHeight,
                       -safeWidth/2, -safeHeight/2, safeWidth, safeHeight);
               }
           } catch (e) {
               console.error("Error drawing text from offscreen canvas:", e);
           }
       } else {
           // If letter spacing is specified, render each letter individually
           const text = (obj.text || '').toUpperCase();
           if (!text) return;

           // Measure the total width with letter spacing
           const tempCtx = document.createElement('canvas').getContext('2d');
           setTextContextOn(tempCtx, obj);

           // Calculate total width with letter spacing
           let totalWidth = 0;
           const letters = text.split('');
           const letterWidths = letters.map(letter => {
               const width = tempCtx.measureText(letter).width;
               totalWidth += width;
               return width;
           });

           // Add letter spacing between characters
           totalWidth += letterSpacing * (letters.length - 1);

           // Start position for the first letter (centered)
           let currentX = -totalWidth / 2;

           // Draw each letter with the specified spacing
           for (let i = 0; i < letters.length; i++) {
               const letter = letters[i];
               const letterWidth = letterWidths[i];

               // Create a temporary object for this letter with original text for gradient calculation
               const letterObj = { ...obj, text: letter, originalText: obj.text };

               // Render the letter to the letter canvas
               const letterInfo = renderSingleStyledLetter(letterObj, letter, letterCtx, letterCanvas.width, letterCanvas.height, i, letters.length);

               // Calculate the position for this letter
               const letterX = currentX + letterWidth / 2;

               // Save the context state
               targetCtx.save();

               // Move to the letter position
               targetCtx.translate(letterX, 0);

               try {
                   // Calculate source rectangle with generous padding for effects
                   const effectsPadding = Math.max(
                       letterSourcePadding * 2,                // Base padding
                       obj.fontSize * 0.5,                     // Scale with font size
                       obj.shadowOffsetX + obj.shadowBlur * 2, // Account for shadow
                       obj.d3dOffset + obj.d3dBlur * 2,        // Account for 3D effects
                       obj.blockShadowOffset * 1.5,            // Account for block shadow
                       obj.strokeWidth * 3                     // Account for stroke
                   );

                   const sourceW = letterInfo.width + effectsPadding * 2;
                   const sourceH = letterInfo.height + effectsPadding * 2;
                   const sourceX = letterInfo.centerX - sourceW / 2;
                   const sourceY = letterInfo.centerY - sourceH / 2;
                   const destX = -sourceW / 2;
                   const destY = -sourceH / 2;

                   if (sourceX >= 0 && sourceY >= 0 && sourceW > 0 && sourceH > 0 &&
                       sourceX + sourceW <= letterCanvas.width && sourceY + sourceH <= letterCanvas.height) {
                       // Draw the letter from offscreen canvas to the target canvas
                       targetCtx.drawImage(letterCanvas, sourceX, sourceY, sourceW, sourceH, destX, destY, sourceW, sourceH);
                   } else {
                       // Fallback: Use a centered portion of the letter canvas
                       const safeWidth = Math.min(letterCanvas.width * 0.8, sourceW);
                       const safeHeight = Math.min(letterCanvas.height * 0.8, sourceH);
                       const safeSx = (letterCanvas.width - safeWidth) / 2;
                       const safeSy = (letterCanvas.height - safeHeight) / 2;

                       targetCtx.drawImage(letterCanvas, safeSx, safeSy, safeWidth, safeHeight,
                           -safeWidth/2, -safeHeight/2, safeWidth, safeHeight);
                   }
               } catch (e) {
                   console.error("Error drawing letter in spaced text:", e);
               }

               // Restore the context state
               targetCtx.restore();

               // Move to the next letter position
               currentX += letterWidth + letterSpacing;
           }
       }
   }
   function drawWarpedObject(obj, targetCtx) { // Added targetCtx
       renderStyledObjectToOffscreen(obj, octx, os.width, os.height);
       tempWarpCtx.clearRect(0, 0, tempWarpCanvas.width, tempWarpCanvas.height);
       tempWarpCtx.save();
       setTextContextOn(tempWarpCtx, obj);
       const metrics = tempWarpCtx.measureText((obj.text || '').toUpperCase());
       const ascent = metrics.actualBoundingBoxAscent || obj.fontSize * 0.8;
       const textHeight = ascent + (metrics.actualBoundingBoxDescent || obj.fontSize * 0.2);
       const sourceCenterY = os.height / 2;
       const sourceTopY = sourceCenterY - textHeight / 2;
       const curve = obj.warpCurve;
       const sourceOffsetY = obj.warpOffset;
       const sourceSampleHeight = obj.warpHeight;
       const bottom = obj.warpBottom;
       const isTri = obj.warpTriangle;
       const shiftCenterValue = obj.warpShiftCenter;
       const angleSteps = Math.PI / tempWarpCanvas.width;
       const peak = tempWarpCanvas.width * (shiftCenterValue / 200.0);
       for (let i = 0; i < tempWarpCanvas.width; i++) {
           let destHeight;
           if (isTri) {
               const distFromPeak = Math.abs(i - peak);
               const maxDist = Math.max(peak, tempWarpCanvas.width - peak);
               const factor = (maxDist > 0) ? (distFromPeak / maxDist) : 0;
               destHeight = bottom - (curve * factor);
           } else {
               destHeight = bottom - curve * Math.sin(i * angleSteps);
           }
           destHeight = Math.max(1, destHeight);
           const destY = -destHeight / 2;
           try {
               const sy = sourceTopY + sourceOffsetY;
               const sh = sourceSampleHeight;
               if (sy >= 0 && sh > 0 && sy + sh <= os.height) {
                   tempWarpCtx.drawImage(os, i * (os.width / tempWarpCanvas.width), sy, os.width / tempWarpCanvas.width, sh, i, destY + tempWarpCanvas.height / 2, 1, destHeight);
               }
           } catch (e) { /* ignore */ }
       }
       tempWarpCtx.restore();
       targetCtx.drawImage(tempWarpCanvas, -tempWarpCanvas.width / 2, -tempWarpCanvas.height / 2); // Use targetCtx
   }

   // New gradient masking function for circular text
   function drawCircularObjectWithGradientMask(obj, targetCtx) {
       console.log('🎨 GRADIENT MASK: Drawing circular text with gradient mask');

       // Save original gradient
       const originalGradient = obj.gradient;

       // Create a temporary canvas for the complete rendering
       const tempCanvas = document.createElement('canvas');
       tempCanvas.width = 2000;
       tempCanvas.height = 2000;
       const tempCtx = tempCanvas.getContext('2d');

       // Step 1: Draw effects and text with solid color to temp canvas (WITHOUT front outlines)
       tempCtx.save();
       tempCtx.translate(tempCanvas.width / 2, tempCanvas.height / 2);
       obj.gradient = { type: 'solid' };
       // Temporarily disable front outline to prevent it from being drawn in shadow effects
       const originalOutlineWidth = obj.perspectiveShadowOutlineWidth;
       const originalD3dWidth = obj.d3dSecondaryWidth;
       obj.perspectiveShadowOutlineWidth = 0;
       obj.d3dSecondaryWidth = 0;
       drawCircularObjectInternal(obj, tempCtx);
       // Restore outline settings
       obj.perspectiveShadowOutlineWidth = originalOutlineWidth;
       obj.d3dSecondaryWidth = originalD3dWidth;
       tempCtx.restore();

       // Step 2: Create text mask (only main text, no effects)
       const textCanvas = document.createElement('canvas');
       textCanvas.width = 2000;
       textCanvas.height = 2000;
       const textCtx = textCanvas.getContext('2d');

       // Draw circular text in white on transparent background (for masking)
       textCtx.save();
       textCtx.translate(textCanvas.width / 2, textCanvas.height / 2);
       textCtx.fillStyle = 'white';
       textCtx.font = `${obj.fontWeight || 'bold'} ${obj.fontSize}px ${obj.fontFamily || 'Poppins'}`;
       textCtx.textAlign = "center";
       textCtx.textBaseline = "middle";

       const diameter = obj.circleDiameter;
       const kerning = obj.circleKerning;
       const flipped = obj.circleFlip;
       const text = (obj.text || '').toUpperCase();
       const radius = diameter / 2;

       const contentArr = text.split('');
       const letterAngles = [];
       let totalAngle = 0;
       contentArr.forEach((letter) => {
           const letterWidth = textCtx.measureText(letter).width + kerning;
           const letterAngle = radius > 0 ? (letterWidth / radius) * (180 / Math.PI) : 0;
           letterAngles.push(letterAngle);
           totalAngle += letterAngle;
       });

       let currentAngleRad = (-totalAngle / 2) * Math.PI / 180;
       for (let i = 0; i < contentArr.length; i++) {
           const letter = contentArr[i];
           const letterAngleDeg = letterAngles[i];
           const letterAngleRad = letterAngleDeg * Math.PI / 180;
           const halfAngleRad = letterAngleRad / 2;
           currentAngleRad += halfAngleRad;

           const angleToDraw = flipped ? currentAngleRad + Math.PI : currentAngleRad;
           const x = radius * Math.cos(angleToDraw);
           const y = radius * Math.sin(angleToDraw);

           textCtx.save();
           textCtx.translate(x, y);
           let rot = angleToDraw + Math.PI / 2;
           if (flipped) { rot += Math.PI; }
           textCtx.rotate(rot);

           textCtx.fillText(letter, 0, 0);
           textCtx.restore();

           currentAngleRad += halfAngleRad;
       }
       textCtx.restore();

       // Step 3: Cut out text areas from the effects canvas using destination-out
       tempCtx.globalCompositeOperation = 'destination-out';
       tempCtx.drawImage(textCanvas, 0, 0);

       // Step 4: Create gradient canvas
       const gradientCanvas = document.createElement('canvas');
       gradientCanvas.width = 2000;
       gradientCanvas.height = 2000;
       const gradientCtx = gradientCanvas.getContext('2d');

       // Create the gradient
       const gradientType = originalGradient.type;
       let gradient;

       if (gradientType === 'radial') {
           const centerX = gradientCanvas.width / 2;
           const centerY = gradientCanvas.height / 2;
           const radius = Math.max(gradientCanvas.width, gradientCanvas.height) / 3;
           gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
       } else {
           // Linear gradient
           const angle = (originalGradient.gradient.angle || 0) * Math.PI / 180;
           const cos = Math.cos(angle);
           const sin = Math.sin(angle);
           const centerX = gradientCanvas.width / 2;
           const centerY = gradientCanvas.height / 2;
           const length = Math.max(gradientCanvas.width, gradientCanvas.height);

           const x1 = centerX - (length / 2) * cos;
           const y1 = centerY - (length / 2) * sin;
           const x2 = centerX + (length / 2) * cos;
           const y2 = centerY + (length / 2) * sin;

           gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
       }

       // Add color stops
       if (originalGradient.gradient.colors) {
           originalGradient.gradient.colors.forEach(colorStop => {
               gradient.addColorStop(colorStop.position / 100, colorStop.color);
           });
       }

       // Fill with gradient
       gradientCtx.fillStyle = gradient;
       gradientCtx.fillRect(0, 0, gradientCanvas.width, gradientCanvas.height);

       // Apply text as mask to gradient
       gradientCtx.globalCompositeOperation = 'destination-in';
       gradientCtx.drawImage(textCanvas, 0, 0);

       // Step 5: Draw effects (with text cut out) to target canvas
       console.log('🔍 RENDER ORDER: Step 5 - Drawing shadow effects');
       targetCtx.drawImage(tempCanvas, -tempCanvas.width / 2, -tempCanvas.height / 2);

       // Step 6: Draw gradient text on top
       console.log('🔍 RENDER ORDER: Step 6 - Drawing gradient text');
       targetCtx.drawImage(gradientCanvas, -gradientCanvas.width / 2, -gradientCanvas.height / 2);

       // Step 7: Draw front outlines on top of gradient text (using original circular system)
       console.log('🔍 RENDER ORDER: Step 7 - Drawing front outlines on top');
       if (obj.shadowMode === 'perspectiveShadow' && originalOutlineWidth > 0) {
           console.log('🔍 RENDER ORDER: Drawing perspective shadow front outline on top');
           const outlineColor = obj.perspectiveShadowOutlineColor;
           const outlineOpacity = obj.perspectiveShadowOutlineOpacity / 100;
           const outlineWidth = originalOutlineWidth;
           const outlineOffsetX = obj.perspectiveShadowOutlineOffsetX || 0;
           const outlineOffsetY = obj.perspectiveShadowOutlineOffsetY || 0;
           drawCircularFrontOutline(obj, targetCtx, outlineColor, outlineOpacity, outlineWidth, outlineOffsetX, outlineOffsetY);
       }
       if (obj.shadowMode === 'detailed3D' && originalD3dWidth > 0) {
           console.log('🔍 RENDER ORDER: Drawing detailed 3D front outline on top');
           const outlineColor = obj.d3dSecondaryColor;
           const outlineOpacity = obj.d3dSecondaryOpacity / 100;
           const outlineWidth = originalD3dWidth;
           const outlineOffsetX = obj.d3dSecondaryOffsetX || 0;
           const outlineOffsetY = obj.d3dSecondaryOffsetY || 0;
           drawCircularFrontOutline(obj, targetCtx, outlineColor, outlineOpacity, outlineWidth, outlineOffsetX, outlineOffsetY);
       }

       // Restore original gradient
       obj.gradient = originalGradient;

       console.log('🎨 GRADIENT MASK: Circular text with gradient mask complete');
   }

   // Standalone front outline drawing functions for gradient masking
   function drawPerspectiveFrontOutline(obj, targetCtx) {
       if (!obj.perspectiveShadowOutlineWidth || obj.perspectiveShadowOutlineWidth <= 0) return;

       const outlineColor = obj.perspectiveShadowOutlineColor || '#d1d5db';
       const outlineOpacity = (obj.perspectiveShadowOutlineOpacity !== undefined) ? obj.perspectiveShadowOutlineOpacity / 100 : 1.0;
       const outlineWidth = obj.perspectiveShadowOutlineWidth || 3;
       const outlineOffsetX = obj.perspectiveShadowOutlineOffsetX || 2;
       const outlineOffsetY = obj.perspectiveShadowOutlineOffsetY || -3;

       console.log('🔍 FRONT OUTLINE DEBUG: drawPerspectiveFrontOutline called', {
           distortType: obj.distortType,
           effectMode: obj.effectMode,
           outlineWidth,
           outlineColor,
           text: obj.text,
           hasDistortType: !!obj.distortType,
           hasEffectMode: !!obj.effectMode
       });

       // For circular text, we need to draw the outline following the circular path
       if (obj.distortType === 'circular' || obj.effectMode === 'circle') {
           console.log('🔍 FRONT OUTLINE: Drawing circular front outline');
           // For circular text, use no offset to align with text path
           drawCircularFrontOutline(obj, targetCtx, outlineColor, outlineOpacity, outlineWidth, 0, 0);
       } else {
           console.log('🔍 FRONT OUTLINE: Drawing straight front outline');
           // For non-circular text, draw straight outline
           targetCtx.save();
           setTextContextOn(targetCtx, obj);
           targetCtx.lineWidth = outlineWidth;
           targetCtx.strokeStyle = hexToRgba(outlineColor, outlineOpacity);
           targetCtx.lineJoin = 'round';
           targetCtx.strokeText((obj.text || '').toUpperCase(), outlineOffsetX, outlineOffsetY);
           targetCtx.restore();
       }

       console.log('🎨 GRADIENT MASK: Drew perspective front outline on top');
   }

   function drawDetailed3DFrontOutline(obj, targetCtx) {
       if (!obj.d3dSecondaryWidth || obj.d3dSecondaryWidth <= 0) return;

       const secondaryColorRgba = hexToRgba(obj.d3dSecondaryColor, obj.d3dSecondaryOpacity / 100);

       console.log('🔍 FRONT OUTLINE DEBUG: drawDetailed3DFrontOutline called', {
           distortType: obj.distortType,
           effectMode: obj.effectMode,
           outlineWidth: obj.d3dSecondaryWidth,
           outlineColor: obj.d3dSecondaryColor,
           text: obj.text
       });

       // For circular text, we need to draw the outline following the circular path
       if (obj.distortType === 'circular' || obj.effectMode === 'circle') {
           console.log('🔍 FRONT OUTLINE: Drawing circular detailed 3D front outline');
           const outlineColor = obj.d3dSecondaryColor;
           const outlineOpacity = obj.d3dSecondaryOpacity / 100;
           const outlineWidth = obj.d3dSecondaryWidth;
           // For circular text, use no offset to align with text path
           drawCircularFrontOutline(obj, targetCtx, outlineColor, outlineOpacity, outlineWidth, 0, 0);
       } else {
           console.log('🔍 FRONT OUTLINE: Drawing straight detailed 3D front outline');
           // For non-circular text, draw straight outline
           targetCtx.save();
           setTextContextOn(targetCtx, obj);
           targetCtx.lineWidth = obj.d3dSecondaryWidth;
           targetCtx.strokeStyle = secondaryColorRgba;
           targetCtx.lineJoin = 'round';
           targetCtx.strokeText((obj.text || '').toUpperCase(), obj.d3dSecondaryOffsetX, obj.d3dSecondaryOffsetY);
           targetCtx.restore();
       }

       console.log('🎨 GRADIENT MASK: Drew detailed 3D front outline on top');
   }

   // Draw front outline following circular path
   function drawCircularFrontOutline(obj, targetCtx, outlineColor, outlineOpacity, outlineWidth, outlineOffsetX, outlineOffsetY) {
       console.log('🔍 CIRCULAR OUTLINE: drawCircularFrontOutline called', {
           text: obj.text,
           outlineColor,
           outlineOpacity,
           outlineWidth,
           outlineOffsetX,
           outlineOffsetY,
           circleDiameter: obj.circleDiameter,
           circleKerning: obj.circleKerning,
           circleFlip: obj.circleFlip
       });

       // Use the SAME circular parameters as the main text
       const diameter = obj.circleDiameter;
       const kerning = obj.circleKerning;
       const flipped = obj.circleFlip;
       const text = (obj.text || '').toUpperCase();
       if (!text) return;

       const radius = diameter / 2;
       const letterSpacing = obj._effectiveLetterSpacing || obj.letterSpacing || 0;

       targetCtx.save();
       setTextContextOn(targetCtx, obj);
       targetCtx.lineWidth = outlineWidth;
       targetCtx.strokeStyle = hexToRgba(outlineColor, outlineOpacity);
       targetCtx.lineJoin = 'round';

       // Use the SAME angle calculation as drawCircularObjectInternal
       const contentArr = text.split('');
       const letterAngles = [];
       let totalAngle = 0;
       contentArr.forEach((letter) => {
           const letterWidth = targetCtx.measureText(letter).width + kerning;
           const letterAngle = radius > 0 ? (letterWidth / radius) * (180 / Math.PI) : 0;
           letterAngles.push(letterAngle);
           totalAngle += letterAngle;
       });

       // Use the SAME starting angle calculation as main text
       let currentAngleRad = (-totalAngle / 2) * Math.PI / 180;

       // Draw each character with the SAME positioning logic
       for (let i = 0; i < contentArr.length; i++) {
           const letter = contentArr[i];
           const letterAngleDeg = letterAngles[i];
           const letterAngleRad = letterAngleDeg * Math.PI / 180;
           const halfAngleRad = letterAngleRad / 2;
           currentAngleRad += halfAngleRad;

           // Use the SAME angle calculation as main text
           const angleToDraw = flipped ? currentAngleRad + Math.PI : currentAngleRad;
           const x = radius * Math.cos(angleToDraw) + outlineOffsetX;
           const y = radius * Math.sin(angleToDraw) + outlineOffsetY;

           targetCtx.save();
           targetCtx.translate(x, y);
           let rot = angleToDraw + Math.PI / 2;
           if (flipped) { rot += Math.PI; }
           targetCtx.rotate(rot);
           targetCtx.strokeText(letter, 0, 0);
           targetCtx.restore();

           currentAngleRad += halfAngleRad;
       }

       targetCtx.restore();
       console.log('🎨 GRADIENT MASK: Drew circular front outline');
   }

   // Function to draw front outline for Grid Distort effects
   function drawGridDistortFrontOutline(obj, targetCtx, outlineColor, outlineOpacity, outlineWidth, offsetX, offsetY, font) {
       console.log('🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system');
       console.log('🔍 FRONT OUTLINE #1 DETAILS:', {
           outlineColor,
           outlineOpacity,
           outlineWidth,
           offsetX,
           offsetY,
           effectMode: obj.effectMode,
           shadowMode: obj.shadowMode,
           hasGradient: obj.gradient && obj.gradient.type !== 'solid'
       });

       if (!font || !obj.gridDistort) {
           console.warn('Font or grid distort not available for front outline');
           return;
       }

       targetCtx.save();

       // Apply offset
       targetCtx.translate(offsetX, offsetY);

       // Set outline properties
       targetCtx.strokeStyle = outlineColor;
       targetCtx.globalAlpha = outlineOpacity;
       targetCtx.lineWidth = outlineWidth;

       // Use a simpler approach: draw grid distorted text with strokeText only
       const text = obj.text;
       setTextContextOn(targetCtx, obj);

       // Get text metrics for positioning
       const tempMeasureCtx = document.createElement('canvas').getContext('2d');
       setTextContextOn(tempMeasureCtx, obj);
       const textWidth = tempMeasureCtx.measureText(text).width;
       const textHeight = obj.fontSize;

       // Calculate grid bounds with padding
       const padding = obj.gridDistort.gridPadding;
       const gridLeft = -textWidth/2 - padding;
       const gridTop = -textHeight/2 - padding;
       const gridWidth = textWidth + padding * 2;
       const gridHeight = textHeight + padding * 2;

       // Draw each character with grid distortion (stroke only)
       const chars = text.split('');
       let currentX = -textWidth / 2;

       for (let i = 0; i < chars.length; i++) {
           const char = chars[i];
           const charWidth = tempMeasureCtx.measureText(char).width;
           const charCenterX = currentX + charWidth / 2;
           const charCenterY = 0;

           // Get warped position
           const warped = getWarpedPosition(
               charCenterX, charCenterY,
               obj.gridDistort.controlPoints, obj.gridDistort.gridCols, obj.gridDistort.gridRows,
               gridLeft, gridTop, gridWidth, gridHeight,
               obj.gridDistort.intensity, obj.gridDistort.verticalOnly
           );

           // Draw only the stroke (outline)
           targetCtx.strokeText(char, warped.x, warped.y);

           currentX += charWidth;
       }

       targetCtx.restore();
       console.log('🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)');
   }

   // Function to draw front outline for Curved text effects
   function drawCurvedFrontOutline(obj, targetCtx, outlineColor, outlineOpacity, outlineWidth, offsetX, offsetY) {
       console.log('🎨 GRADIENT MASK: Drawing Curved text front outline');

       targetCtx.save();

       // Apply offset
       targetCtx.translate(offsetX, offsetY);

       // Set outline properties
       targetCtx.strokeStyle = outlineColor;
       targetCtx.globalAlpha = outlineOpacity;
       targetCtx.lineWidth = outlineWidth;
       targetCtx.fillStyle = 'transparent';

       // Use the same curved text logic as the main curved text rendering
       const curveAmount = obj.curveAmount;
       const kerning = obj.curveKerning;
       const flip = obj.curveFlip;
       const text = (obj.text || '').toUpperCase();

       setTextContextOn(targetCtx, obj);
       const direction = flip ? -1 : 1;
       const curveFactor = Math.max(0.1, Math.abs(curveAmount) / 10);
       const tempMeasureCtx = document.createElement('canvas').getContext('2d');
       setTextContextOn(tempMeasureCtx, obj);
       const textWidthEst = tempMeasureCtx.measureText(text).width;
       const curveRadius = (Math.max(2000, textWidthEst * 1.5) / curveFactor);
       const chars = text.split('');
       let totalWidth = 0;
       const charWidths = chars.map(char => {
           const width = targetCtx.measureText(char).width + kerning;
           totalWidth += width;
           return width;
       });
       let currentX = -totalWidth / 2;

       // Draw each character with stroke only
       for (let i = 0; i < chars.length; i++) {
           const letter = chars[i];
           const displayWidth = charWidths[i];
           const charCenterX = currentX + displayWidth / 2;
           const angleOffset = (charCenterX / curveRadius) * direction;
           const yOffset = curveRadius * (Math.cos(angleOffset) - 1) * direction;
           const rot = angleOffset * direction;

           targetCtx.save();
           targetCtx.translate(charCenterX, yOffset);
           targetCtx.rotate(rot);

           // Draw only the stroke (outline)
           targetCtx.strokeText(letter, 0, 0);

           targetCtx.restore();
           currentX += displayWidth;
       }

       targetCtx.restore();
       console.log('🎨 GRADIENT MASK: Drew Curved text front outline');
   }

   function drawCircularObject(obj, targetCtx) { // Added targetCtx
       // Check if we need to apply gradient masking
       if (obj.gradient && obj.gradient.type !== 'solid') {
           drawCircularObjectWithGradientMask(obj, targetCtx);
           return;
       }

       // Otherwise draw normally
       drawCircularObjectInternal(obj, targetCtx);
   }

   function drawCircularObjectInternal(obj, targetCtx) { // Added targetCtx
       const diameter = obj.circleDiameter;
       const kerning = obj.circleKerning;
       const flipped = obj.circleFlip;
       const text = (obj.text || '').toUpperCase();
       if (!text) return;

       const radius = diameter / 2;
       setTextContextOn(targetCtx, obj); // Use targetCtx for initial measurement
       const contentArr = text.split('');
       const letterAngles = [];
       let totalAngle = 0;
       contentArr.forEach((letter) => {
           const letterWidth = targetCtx.measureText(letter).width + kerning; // Use targetCtx
           const letterAngle = radius > 0 ? (letterWidth / radius) * (180 / Math.PI) : 0;
           letterAngles.push(letterAngle);
           totalAngle += letterAngle;
       });
       let currentAngleRad = (-totalAngle / 2) * Math.PI / 180;
       for (let i = 0; i < contentArr.length; i++) {
           const letter = contentArr[i];
           const letterAngleDeg = letterAngles[i];
           const letterAngleRad = letterAngleDeg * Math.PI / 180;
           const halfAngleRad = letterAngleRad / 2;
           currentAngleRad += halfAngleRad;

           // Create letter object with original text for gradient calculation
           const letterObj = { ...obj, text: letter, originalText: obj.text };
           const letterInfo = renderSingleStyledLetter(letterObj, letter, letterCtx, letterCanvas.width, letterCanvas.height, i, contentArr.length);
           const angleToDraw = flipped ? currentAngleRad + Math.PI : currentAngleRad;
           const x = radius * Math.cos(angleToDraw);
           const y = radius * Math.sin(angleToDraw);
           targetCtx.save(); // Use targetCtx
           targetCtx.translate(x, y); // Use targetCtx
           let rot = angleToDraw + Math.PI / 2;
           if (flipped) { rot += Math.PI; }
           targetCtx.rotate(rot); // Use targetCtx
           try {
               // Calculate source rectangle with generous padding for effects
               const effectsPadding = Math.max(
                   letterSourcePadding * 2,                // Base padding
                   obj.fontSize * 0.5,                     // Scale with font size
                   obj.shadowOffsetX + obj.shadowBlur * 2, // Account for shadow
                   obj.d3dOffset + obj.d3dBlur * 2,        // Account for 3D effects
                   obj.blockShadowOffset * 1.5,            // Account for block shadow
                   obj.strokeWidth * 3                     // Account for stroke
               );

               const sourceW = letterInfo.width + effectsPadding * 2;
               const sourceH = letterInfo.height + effectsPadding * 2;
               const sourceX = letterInfo.centerX - sourceW / 2;
               const sourceY = letterInfo.centerY - sourceH / 2;
               const destX = -sourceW / 2;
               const destY = -sourceH / 2;

               if (sourceX >= 0 && sourceY >= 0 && sourceW > 0 && sourceH > 0 &&
                   sourceX + sourceW <= letterCanvas.width && sourceY + sourceH <= letterCanvas.height) {
                   // Draw the letter from offscreen canvas to the target canvas
                   targetCtx.drawImage(letterCanvas, sourceX, sourceY, sourceW, sourceH, destX, destY, sourceW, sourceH);
               } else {
                   // Fallback: Use a centered portion of the letter canvas
                   const safeWidth = Math.min(letterCanvas.width * 0.8, sourceW);
                   const safeHeight = Math.min(letterCanvas.height * 0.8, sourceH);
                   const safeSx = (letterCanvas.width - safeWidth) / 2;
                   const safeSy = (letterCanvas.height - safeHeight) / 2;

                   targetCtx.drawImage(letterCanvas, safeSx, safeSy, safeWidth, safeHeight,
                       -safeWidth/2, -safeHeight/2, safeWidth, safeHeight);
               }
           } catch(e) { console.error("DrawImage error in circle:", e); }
           targetCtx.restore(); // Use targetCtx
           currentAngleRad += halfAngleRad;
       }
   }
   function drawCurvedObject(obj, targetCtx) { // Added targetCtx
       const curveAmount = obj.curveAmount;
       const kerning = obj.curveKerning;
       const flip = obj.curveFlip;
       const text = (obj.text || '').toUpperCase();
       if (!text || curveAmount === 0) {
           drawNormalOrSkewObject(obj, targetCtx); // Pass targetCtx
           return;
       }

       // Check if we need to apply gradient masking
       if (obj.gradient && obj.gradient.type !== 'solid') {
           drawCurvedObjectWithGradientMask(obj, targetCtx);
           return;
       }
       setTextContextOn(targetCtx, obj); // Use targetCtx
       const direction = flip ? -1 : 1;
       const curveFactor = Math.max(0.1, Math.abs(curveAmount) / 10);
       const tempMeasureCtx = document.createElement('canvas').getContext('2d'); // Use separate context for measurement
       setTextContextOn(tempMeasureCtx, obj);
       const textWidthEst = tempMeasureCtx.measureText(text).width;
       const curveRadius = (Math.max(w, textWidthEst * 1.5) / curveFactor); // w is global canvas width, maybe use targetCtx.canvas.width? Needs check.
       const chars = text.split('');
       let totalWidth = 0;
       const charWidths = chars.map(char => {
           const width = targetCtx.measureText(char).width + kerning; // Use targetCtx
           totalWidth += width;
           return width;
       });
       let currentX = -totalWidth / 2;
       for (let i = 0; i < chars.length; i++) {
           const letter = chars[i];
           const displayWidth = charWidths[i];

           // Create letter object with original text for gradient calculation
           const letterObj = { ...obj, text: letter, originalText: obj.text };
           const letterInfo = renderSingleStyledLetter(letterObj, letter, letterCtx, letterCanvas.width, letterCanvas.height, i, chars.length);
           const charCenterX = currentX + displayWidth / 2;
           const angleOffset = (charCenterX / curveRadius) * direction;
           const yOffset = curveRadius * (Math.cos(angleOffset) - 1) * direction;
           const rot = angleOffset * direction;
           targetCtx.save(); // Use targetCtx
           targetCtx.translate(charCenterX, yOffset); // Use targetCtx
           targetCtx.rotate(rot); // Use targetCtx
           try {
               // Calculate source rectangle with generous padding for effects
               const effectsPadding = Math.max(
                   letterSourcePadding * 2,                // Base padding
                   obj.fontSize * 0.5,                     // Scale with font size
                   obj.shadowOffsetX + obj.shadowBlur * 2, // Account for shadow
                   obj.d3dOffset + obj.d3dBlur * 2,        // Account for 3D effects
                   obj.blockShadowOffset * 1.5,            // Account for block shadow
                   obj.strokeWidth * 3                     // Account for stroke
               );

               const sourceW = letterInfo.width + effectsPadding * 2;
               const sourceH = letterInfo.height + effectsPadding * 2;
               const sourceX = letterInfo.centerX - sourceW / 2;
               const sourceY = letterInfo.centerY - sourceH / 2;
               const destX = -sourceW / 2;
               const destY = -sourceH / 2;

               if (sourceX >= 0 && sourceY >= 0 && sourceW > 0 && sourceH > 0 &&
                   sourceX + sourceW <= letterCanvas.width && sourceY + sourceH <= letterCanvas.height) {
                   // Draw the letter from offscreen canvas to the target canvas
                   targetCtx.drawImage(letterCanvas, sourceX, sourceY, sourceW, sourceH, destX, destY, sourceW, sourceH);
               } else {
                   // Fallback: Use a centered portion of the letter canvas
                   const safeWidth = Math.min(letterCanvas.width * 0.8, sourceW);
                   const safeHeight = Math.min(letterCanvas.height * 0.8, sourceH);
                   const safeSx = (letterCanvas.width - safeWidth) / 2;
                   const safeSy = (letterCanvas.height - safeHeight) / 2;

                   targetCtx.drawImage(letterCanvas, safeSx, safeSy, safeWidth, safeHeight,
                       -safeWidth/2, -safeHeight/2, safeWidth, safeHeight);
               }
           } catch(e) { console.error("DrawImage error in curve:", e); }
           targetCtx.restore(); // Use targetCtx
           currentX += displayWidth;
       }
   }

   // Gradient masking function for curved text
   function drawCurvedObjectWithGradientMask(obj, targetCtx) {
       console.log('🎨 GRADIENT MASK: Drawing curved text with gradient mask');

       // Save original gradient
       const originalGradient = obj.gradient;

       // Step 1: Create a temporary canvas for effects and text with solid color
       const tempCanvas = document.createElement('canvas');
       tempCanvas.width = 2000;
       tempCanvas.height = 2000;
       const tempCtx = tempCanvas.getContext('2d');

       // Step 2: Draw effects and text with solid color to temp canvas
       tempCtx.save();
       tempCtx.translate(tempCanvas.width / 2, tempCanvas.height / 2);
       obj.gradient = { type: 'solid' };

       // Draw curved text with effects using the same logic as the main function
       const curveAmount = obj.curveAmount;
       const kerning = obj.curveKerning;
       const flip = obj.curveFlip;
       const text = (obj.text || '').toUpperCase();

       setTextContextOn(tempCtx, obj);
       const direction = flip ? -1 : 1;
       const curveFactor = Math.max(0.1, Math.abs(curveAmount) / 10);
       const tempMeasureCtx = document.createElement('canvas').getContext('2d');
       setTextContextOn(tempMeasureCtx, obj);
       const textWidthEst = tempMeasureCtx.measureText(text).width;
       const curveRadius = (Math.max(2000, textWidthEst * 1.5) / curveFactor);
       const chars = text.split('');
       let totalWidth = 0;
       const charWidths = chars.map(char => {
           const width = tempCtx.measureText(char).width + kerning;
           totalWidth += width;
           return width;
       });
       let currentX = -totalWidth / 2;

       for (let i = 0; i < chars.length; i++) {
           const letter = chars[i];
           const displayWidth = charWidths[i];

           const letterObj = { ...obj, text: letter, originalText: obj.text };
           const letterInfo = renderSingleStyledLetter(letterObj, letter, letterCtx, letterCanvas.width, letterCanvas.height, i, chars.length);
           const charCenterX = currentX + displayWidth / 2;
           const angleOffset = (charCenterX / curveRadius) * direction;
           const yOffset = curveRadius * (Math.cos(angleOffset) - 1) * direction;
           const rot = angleOffset * direction;

           tempCtx.save();
           tempCtx.translate(charCenterX, yOffset);
           tempCtx.rotate(rot);

           try {
               const effectsPadding = Math.max(
                   letterSourcePadding * 2,
                   obj.fontSize * 0.5,
                   obj.shadowOffsetX + obj.shadowBlur * 2,
                   obj.d3dOffset + obj.d3dBlur * 2,
                   obj.blockShadowOffset * 1.5,
                   obj.strokeWidth * 3
               );

               const sourceW = letterInfo.width + effectsPadding * 2;
               const sourceH = letterInfo.height + effectsPadding * 2;
               const sourceX = letterInfo.centerX - sourceW / 2;
               const sourceY = letterInfo.centerY - sourceH / 2;
               const destX = -sourceW / 2;
               const destY = -sourceH / 2;

               if (sourceX >= 0 && sourceY >= 0 && sourceW > 0 && sourceH > 0 &&
                   sourceX + sourceW <= letterCanvas.width && sourceY + sourceH <= letterCanvas.height) {
                   tempCtx.drawImage(letterCanvas, sourceX, sourceY, sourceW, sourceH, destX, destY, sourceW, sourceH);
               } else {
                   const safeWidth = Math.min(letterCanvas.width * 0.8, sourceW);
                   const safeHeight = Math.min(letterCanvas.height * 0.8, sourceH);
                   const safeSx = (letterCanvas.width - safeWidth) / 2;
                   const safeSy = (letterCanvas.height - safeHeight) / 2;

                   tempCtx.drawImage(letterCanvas, safeSx, safeSy, safeWidth, safeHeight,
                       -safeWidth/2, -safeHeight/2, safeWidth, safeHeight);
               }
           } catch(e) {
               console.error("DrawImage error in curved temp:", e);
           }

           tempCtx.restore();
           currentX += displayWidth;
       }

       tempCtx.restore();

       // Step 3: Create text-only canvas for masking
       const textCanvas = document.createElement('canvas');
       textCanvas.width = 2000;
       textCanvas.height = 2000;
       const textCtx = textCanvas.getContext('2d');

       // Draw curved text in white on transparent background (text only, no effects)
       textCtx.save();
       textCtx.translate(textCanvas.width / 2, textCanvas.height / 2);
       textCtx.fillStyle = 'white';

       // Create a temporary object without gradient and effects for shape rendering
       const tempObj = {
           ...obj,
           gradient: { type: 'solid' },
           shadowMode: 'none',
           strokeWidth: 0
       };

       // Use the existing curved text logic but with solid color
       const maskCurveAmount = tempObj.curveAmount;
       const maskKerning = tempObj.curveKerning;
       const maskFlip = tempObj.curveFlip;
       const maskText = (tempObj.text || '').toUpperCase();

       setTextContextOn(textCtx, tempObj);
       const maskDirection = maskFlip ? -1 : 1;
       const maskCurveFactor = Math.max(0.1, Math.abs(maskCurveAmount) / 10);
       const maskMeasureCtx = document.createElement('canvas').getContext('2d');
       setTextContextOn(maskMeasureCtx, tempObj);
       const maskTextWidthEst = maskMeasureCtx.measureText(maskText).width;
       const maskCurveRadius = (Math.max(2000, maskTextWidthEst * 1.5) / maskCurveFactor); // Use canvas width instead of global w
       const maskChars = maskText.split('');
       let maskTotalWidth = 0;
       const maskCharWidths = maskChars.map(char => {
           const width = textCtx.measureText(char).width + maskKerning;
           maskTotalWidth += width;
           return width;
       });
       let maskCurrentX = -maskTotalWidth / 2;

       for (let i = 0; i < maskChars.length; i++) {
           const letter = maskChars[i];
           const displayWidth = maskCharWidths[i];

           const letterObj = { ...tempObj, text: letter, originalText: tempObj.text };
           const letterInfo = renderSingleStyledLetter(letterObj, letter, letterCtx, letterCanvas.width, letterCanvas.height, i, maskChars.length);
           const charCenterX = maskCurrentX + displayWidth / 2;
           const angleOffset = (charCenterX / maskCurveRadius) * maskDirection;
           const yOffset = maskCurveRadius * (Math.cos(angleOffset) - 1) * maskDirection;
           const rot = angleOffset * maskDirection;

           textCtx.save();
           textCtx.translate(charCenterX, yOffset);
           textCtx.rotate(rot);

           try {
               const effectsPadding = Math.max(
                   letterSourcePadding * 2,
                   tempObj.fontSize * 0.5,
                   tempObj.shadowOffsetX + tempObj.shadowBlur * 2,
                   tempObj.d3dOffset + tempObj.d3dBlur * 2,
                   tempObj.blockShadowOffset * 1.5,
                   tempObj.strokeWidth * 3
               );

               const sourceW = letterInfo.width + effectsPadding * 2;
               const sourceH = letterInfo.height + effectsPadding * 2;
               const sourceX = letterInfo.centerX - sourceW / 2;
               const sourceY = letterInfo.centerY - sourceH / 2;
               const destX = -sourceW / 2;
               const destY = -sourceH / 2;

               if (sourceX >= 0 && sourceY >= 0 && sourceW > 0 && sourceH > 0 &&
                   sourceX + sourceW <= letterCanvas.width && sourceY + sourceH <= letterCanvas.height) {
                   textCtx.drawImage(letterCanvas, sourceX, sourceY, sourceW, sourceH, destX, destY, sourceW, sourceH);
               } else {
                   const safeWidth = Math.min(letterCanvas.width * 0.8, sourceW);
                   const safeHeight = Math.min(letterCanvas.height * 0.8, sourceH);
                   const safeSx = (letterCanvas.width - safeWidth) / 2;
                   const safeSy = (letterCanvas.height - safeHeight) / 2;

                   textCtx.drawImage(letterCanvas, safeSx, safeSy, safeWidth, safeHeight,
                       -safeWidth/2, -safeHeight/2, safeWidth, safeHeight);
               }
           } catch(e) {
               console.error("DrawImage error in curved mask:", e);
           }

           textCtx.restore();
           maskCurrentX += displayWidth;
       }

       textCtx.restore();

       // Step 4: Create gradient canvas
       const gradientCanvas = document.createElement('canvas');
       gradientCanvas.width = 2000;
       gradientCanvas.height = 2000;
       const gradientCtx = gradientCanvas.getContext('2d');

       // Create the gradient
       const gradientType = originalGradient.type;
       let gradient;

       if (gradientType === 'radial') {
           const centerX = gradientCanvas.width / 2;
           const centerY = gradientCanvas.height / 2;
           const radius = Math.max(gradientCanvas.width, gradientCanvas.height) / 3;
           gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
       } else {
           // Linear gradient
           const angle = (originalGradient.gradient.angle || 0) * Math.PI / 180;
           const cos = Math.cos(angle);
           const sin = Math.sin(angle);
           const centerX = gradientCanvas.width / 2;
           const centerY = gradientCanvas.height / 2;
           const length = Math.max(gradientCanvas.width, gradientCanvas.height);

           const x1 = centerX - (length / 2) * cos;
           const y1 = centerY - (length / 2) * sin;
           const x2 = centerX + (length / 2) * cos;
           const y2 = centerY + (length / 2) * sin;

           gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
       }

       // Add color stops
       if (originalGradient.gradient.colors) {
           originalGradient.gradient.colors.forEach(colorStop => {
               gradient.addColorStop(colorStop.position / 100, colorStop.color);
           });
       }

       // Fill with gradient
       gradientCtx.fillStyle = gradient;
       gradientCtx.fillRect(0, 0, gradientCanvas.width, gradientCanvas.height);

       // Apply text as mask to gradient
       gradientCtx.globalCompositeOperation = 'destination-in';
       gradientCtx.drawImage(textCanvas, 0, 0);

       // Step 5: Draw effects (with text cut out) to target canvas
       tempCtx.globalCompositeOperation = 'destination-out';
       tempCtx.drawImage(textCanvas, 0, 0);
       targetCtx.drawImage(tempCanvas, -tempCanvas.width / 2, -tempCanvas.height / 2);

       // Step 6: Draw gradient text on top
       targetCtx.drawImage(gradientCanvas, -gradientCanvas.width / 2, -gradientCanvas.height / 2);

       // Step 7: Draw front outlines on top of gradient text (for shadow effects)
       console.log('🔍 RENDER ORDER: Step 7 - Drawing front outlines on top for Curved text');

       // Store original outline widths
       const originalOutlineWidth = obj.perspectiveShadowOutlineWidth;
       const originalD3dWidth = obj.d3dSecondaryWidth;

       if (obj.shadowMode === 'perspectiveShadow' && originalOutlineWidth > 0) {
           console.log('🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Curved text');
           const outlineColor = obj.perspectiveShadowOutlineColor;
           const outlineOpacity = obj.perspectiveShadowOutlineOpacity / 100;
           const outlineWidth = originalOutlineWidth;
           const outlineOffsetX = obj.perspectiveShadowOutlineOffsetX || 0;
           const outlineOffsetY = obj.perspectiveShadowOutlineOffsetY || 0;
           drawCurvedFrontOutline(obj, targetCtx, outlineColor, outlineOpacity, outlineWidth, outlineOffsetX, outlineOffsetY);
       }
       if (obj.shadowMode === 'detailed3D' && originalD3dWidth > 0) {
           console.log('🔍 RENDER ORDER: Drawing detailed 3D front outline on top for Curved text');
           const outlineColor = obj.d3dSecondaryColor;
           const outlineOpacity = obj.d3dSecondaryOpacity / 100;
           const outlineWidth = originalD3dWidth;
           const outlineOffsetX = obj.d3dSecondaryOffsetX || 0;
           const outlineOffsetY = obj.d3dSecondaryOffsetY || 0;
           drawCurvedFrontOutline(obj, targetCtx, outlineColor, outlineOpacity, outlineWidth, outlineOffsetX, outlineOffsetY);
       }

       // Restore original gradient
       obj.gradient = originalGradient;

       console.log('🎨 GRADIENT MASK: Curved text with gradient mask complete');
   }

   // Gradient masking function for grid distorted text
   function drawGridDistortedTextWithGradientMask(textObj, targetCtx, font) {
       const callId = Math.random().toString(36).substring(2, 8);
       console.log(`🎨 GRADIENT MASK [${callId}]: Drawing grid distorted text with gradient mask`);

       // Save original gradient
       const originalGradient = textObj.gradient;

       // IMPORTANT: Store the original gradient state for front outline detection
       // This must be done BEFORE modifying textObj.gradient
       textObj._originalHasGradient = originalGradient && originalGradient.type !== 'solid';
       console.log('🔍 GRADIENT MASK: Stored original gradient state:', textObj._originalHasGradient);

       // Step 1: Create a temporary canvas for effects and text with solid color
       const tempCanvas = document.createElement('canvas');
       tempCanvas.width = 2000;
       tempCanvas.height = 2000;
       const tempCtx = tempCanvas.getContext('2d');

       // Step 2: Draw effects and text with solid color to temp canvas
       tempCtx.save();
       tempCtx.translate(tempCanvas.width / 2, tempCanvas.height / 2);
       textObj.gradient = { type: 'solid' };
       drawGridDistortedText(textObj, tempCtx, font);
       tempCtx.restore();

       // Step 3: Create text-only canvas for masking
       const textCanvas = document.createElement('canvas');
       textCanvas.width = 2000;
       textCanvas.height = 2000;
       const textCtx = textCanvas.getContext('2d');

       // Draw grid distorted text in white on transparent background (text only, no effects)
       textCtx.save();
       textCtx.translate(textCanvas.width / 2, textCanvas.height / 2);
       textCtx.fillStyle = 'white';

       // Create a temporary object without gradient and effects for shape rendering
       const tempObj = {
           ...textObj,
           gradient: { type: 'solid' },
           shadowMode: 'none',
           strokeWidth: 0
       };

       // Use the existing grid distort logic but with solid color
       drawGridDistortedText(tempObj, textCtx, font);

       textCtx.restore();

       // Step 4: Create gradient canvas
       const gradientCanvas = document.createElement('canvas');
       gradientCanvas.width = 2000;
       gradientCanvas.height = 2000;
       const gradientCtx = gradientCanvas.getContext('2d');

       // Create the gradient
       const gradientType = originalGradient.type;
       let gradient;

       if (gradientType === 'radial') {
           const centerX = gradientCanvas.width / 2;
           const centerY = gradientCanvas.height / 2;
           const radius = Math.max(gradientCanvas.width, gradientCanvas.height) / 3;
           gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
       } else {
           // Linear gradient
           const angle = (originalGradient.gradient.angle || 0) * Math.PI / 180;
           const cos = Math.cos(angle);
           const sin = Math.sin(angle);
           const centerX = gradientCanvas.width / 2;
           const centerY = gradientCanvas.height / 2;
           const length = Math.max(gradientCanvas.width, gradientCanvas.height);

           const x1 = centerX - (length / 2) * cos;
           const y1 = centerY - (length / 2) * sin;
           const x2 = centerX + (length / 2) * cos;
           const y2 = centerY + (length / 2) * sin;

           gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
       }

       // Add color stops
       if (originalGradient.gradient.colors) {
           originalGradient.gradient.colors.forEach(colorStop => {
               gradient.addColorStop(colorStop.position / 100, colorStop.color);
           });
       }

       // Fill with gradient
       gradientCtx.fillStyle = gradient;
       gradientCtx.fillRect(0, 0, gradientCanvas.width, gradientCanvas.height);

       // Apply text as mask to gradient
       gradientCtx.globalCompositeOperation = 'destination-in';
       gradientCtx.drawImage(textCanvas, 0, 0);

       // Step 5: Draw effects (with text cut out) to target canvas
       tempCtx.globalCompositeOperation = 'destination-out';
       tempCtx.drawImage(textCanvas, 0, 0);
       targetCtx.drawImage(tempCanvas, -tempCanvas.width / 2, -tempCanvas.height / 2);

       // Step 6: Draw gradient text on top
       targetCtx.drawImage(gradientCanvas, -gradientCanvas.width / 2, -gradientCanvas.height / 2);

       // Step 7: Draw front outlines on top of gradient text (for shadow effects)
       console.log('🔍 RENDER ORDER: Step 7 - Drawing front outlines on top for Grid Distort');

       // Store original outline widths
       const originalOutlineWidth = textObj.perspectiveShadowOutlineWidth;
       const originalD3dWidth = textObj.d3dSecondaryWidth;

       if (textObj.shadowMode === 'perspectiveShadow' && originalOutlineWidth > 0) {
           console.log(`🔍 FRONT OUTLINE SOURCE #4 [${callId}]: Drawing perspective shadow front outline from gradient masking (drawGridDistortedTextWithGradientMask)`);
           const outlineColor = textObj.perspectiveShadowOutlineColor;
           const outlineOpacity = textObj.perspectiveShadowOutlineOpacity / 100;
           const outlineWidth = originalOutlineWidth;
           const outlineOffsetX = textObj.perspectiveShadowOutlineOffsetX || 0;
           const outlineOffsetY = textObj.perspectiveShadowOutlineOffsetY || 0;
           drawGridDistortFrontOutline(textObj, targetCtx, outlineColor, outlineOpacity, outlineWidth, outlineOffsetX, outlineOffsetY, font);
       }
       if (textObj.shadowMode === 'detailed3D' && originalD3dWidth > 0) {
           console.log('🔍 RENDER ORDER: Drawing detailed 3D front outline on top for Grid Distort');
           const outlineColor = textObj.d3dSecondaryColor;
           const outlineOpacity = textObj.d3dSecondaryOpacity / 100;
           const outlineWidth = originalD3dWidth;
           const outlineOffsetX = textObj.d3dSecondaryOffsetX || 0;
           const outlineOffsetY = textObj.d3dSecondaryOffsetY || 0;
           drawGridDistortFrontOutline(textObj, targetCtx, outlineColor, outlineOpacity, outlineWidth, outlineOffsetX, outlineOffsetY, font);
       }

       // Restore original gradient
       textObj.gradient = originalGradient;

       // Clean up the temporary gradient state flag
       delete textObj._originalHasGradient;
       console.log('🔍 GRADIENT MASK: Cleaned up original gradient state flag');

       console.log(`🎨 GRADIENT MASK [${callId}]: Grid distorted text with gradient mask complete`);
   }

   // Draw text with Grid Distort effect
   function drawGridDistortObject(obj, targetCtx) {
       const drawCallId = Math.random().toString(36).substring(2, 8);
       console.log(`🔍 GRID DISTORT CALL [${drawCallId}]: drawGridDistortObject called for text: "${obj.text}"`);
       // Check if OpenType.js is loaded
       if (typeof opentype === 'undefined') {
           console.error('OpenType.js is required for Grid Distort effect');
           drawNormalOrSkewObject(obj, targetCtx);
           return;
       }

       // Initialize grid distort for this object if not already initialized
       if (!obj.gridDistort) {
           console.log('Creating gridDistort object for text:', obj.text);
           obj.gridDistort = {
               gridCols: obj.gridDistortCols || 2,
               gridRows: obj.gridDistortRows || 1,
               gridPadding: obj.gridDistortPadding || 120,
               intensity: (obj.gridDistortIntensity || 100) / 100,
               controlPoints: [],
               showGrid: true,
               lastFontSize: obj.fontSize,
               lastText: obj.text,
               relativeControlPoints: [],
               verticalOnly: obj.gridDistortVerticalOnly !== undefined ? obj.gridDistortVerticalOnly : true
           };

           // Initialize grid points
           initializeGridPoints(obj);
       }

       // Get the font for the text object
       const fontFamily = obj.fontFamily || 'Arial';
       const isBold = obj.bold || false;
       const isItalic = obj.italic || false;

       // Create a unique key for this font variant
       const fontKey = `${fontFamily}_${isBold ? 'bold' : 'regular'}_${isItalic ? 'italic' : 'normal'}`;

       // Get the appropriate font path based on style
       const fontPath = getFontPathForFamily(fontFamily, isBold, isItalic);

       // Load the font if not already loaded
       if (!window.loadedFonts || !window.loadedFonts[fontKey]) {
           if (!window.loadedFonts) window.loadedFonts = {};

           // Fallback to normal rendering while font loads
           drawNormalOrSkewObject(obj, targetCtx);

           try {
               // Load the font
               console.log('Loading font from:', fontPath, 'for', fontKey);
               opentype.load(fontPath, (err, font) => {
                   if (err) {
                       console.error('Could not load font: ' + fontPath, err);
                       // Try to load the regular variant as fallback
                       const fallbackPath = getFontPathForFamily(fontFamily, false, false);
                       if (fallbackPath !== fontPath) {
                           console.log('Trying fallback font:', fallbackPath);
                           opentype.load(fallbackPath, (fallbackErr, fallbackFont) => {
                               if (fallbackErr) {
                                   console.error('Could not load fallback font:', fallbackErr);
                                   // Continue with normal rendering on error
                                   drawNormalOrSkewObject(obj, targetCtx);
                                   return;
                               }

                               console.log('Fallback font loaded successfully:', fontFamily);

                               // Store the loaded font
                               window.loadedFonts[fontKey] = fallbackFont;

                               // Redraw the canvas to show the effect
                               update();
                           });
                           return;
                       }

                       // Continue with normal rendering on error
                       drawNormalOrSkewObject(obj, targetCtx);
                       return;
                   }

                   console.log('Font loaded successfully:', fontKey);

                   // Store the loaded font
                   window.loadedFonts[fontKey] = font;

                   // Redraw the canvas to show the effect
                   update();
               });
           } catch (e) {
               console.error('Error in font loading process:', e);
               // Fallback to normal rendering on exception
               drawNormalOrSkewObject(obj, targetCtx);
           }

           return;
       }

       // Update grid parameters if they've changed
       if (obj.gridDistort) {
           console.log('drawGridDistortObject check:', {
               currentText: obj.text,
               lastText: obj.gridDistort.lastText,
               currentFontSize: obj.fontSize,
               lastFontSize: obj.gridDistort.lastFontSize,
               hasRelativePoints: obj.gridDistort.relativeControlPoints ?
                                 obj.gridDistort.relativeControlPoints.length : 'No'
           });

           // Always check if text content has changed first
           if (!obj.gridDistort.lastText || obj.gridDistort.lastText !== obj.text) {
               console.log('Text content changed in drawGridDistortObject, reinitializing grid');
               // Don't update lastText here - let initializeGridPoints handle it
               // This ensures the text change is detected properly
               initializeGridPoints(obj);
           }
           // Then check other parameters
           else if (obj.gridDistort.gridCols !== obj.gridDistortCols ||
               obj.gridDistort.gridRows !== obj.gridDistortRows ||
               obj.gridDistort.gridPadding !== obj.gridDistortPadding ||
               !obj.gridDistort.lastFontSize || // Check if font size has changed
               obj.gridDistort.lastFontSize !== obj.fontSize) {

               obj.gridDistort.gridCols = obj.gridDistortCols || 2;
               obj.gridDistort.gridRows = obj.gridDistortRows || 1;
               obj.gridDistort.gridPadding = obj.gridDistortPadding || 120;
               obj.gridDistort.verticalOnly = obj.gridDistortVerticalOnly !== undefined ? obj.gridDistortVerticalOnly : true;

               // Don't update lastFontSize here - let initializeGridPoints handle it
               // This ensures the font size change is detected properly and distortion is preserved

               // Reinitialize grid points while preserving distortion
               initializeGridPoints(obj);
           }

           // Update intensity if it's changed
           obj.gridDistort.intensity = (obj.gridDistortIntensity || 100) / 100;
       }

       // Draw the grid distorted text using the fontKey created earlier
       const font = window.loadedFonts[fontKey];

       if (font) {
           console.log(`🔍 GRID DISTORT CALL [${drawCallId}]: Using main font: ${fontKey} - SINGLE RENDER`);
           // Check if we need to apply gradient masking
           if (obj.gradient && obj.gradient.type !== 'solid') {
               drawGridDistortedTextWithGradientMask(obj, targetCtx, font);
           } else {
               // Draw the text with grid distortion
               drawGridDistortedText(obj, targetCtx, font);
           }

           // Draw grid only if visibility is enabled AND the object is selected
           console.log('Grid visibility:', obj.gridDistort.showGrid, 'Selected:', obj === canvasObjects[selectedObjectIndex]);
           if (obj.gridDistort && obj.gridDistort.showGrid && obj === canvasObjects[selectedObjectIndex]) {
               console.log('Drawing grid...');
               drawGrid(obj, targetCtx);
           }

           // IMPORTANT: Return early to prevent fallback font rendering
           console.log(`🔍 GRID DISTORT CALL [${drawCallId}]: Main font successful - skipping fallback`);
           return;
       }

       // Only try fallback if main font completely failed to load
       console.warn(`🔍 GRID DISTORT CALL [${drawCallId}]: Font not loaded for ${fontKey}, trying regular variant`);
       const regularFontKey = `${fontFamily}_regular_normal`;
       const regularFont = window.loadedFonts[regularFontKey];

       if (regularFont) {
           console.log(`🔍 GRID DISTORT CALL [${drawCallId}]: Using fallback font: ${regularFontKey} - FALLBACK RENDER`);
           // Check if we need to apply gradient masking for fallback font too
           if (obj.gradient && obj.gradient.type !== 'solid') {
               drawGridDistortedTextWithGradientMask(obj, targetCtx, regularFont);
           } else {
               // Draw with regular font as fallback
               drawGridDistortedText(obj, targetCtx, regularFont);
           }

           // Draw grid if needed AND the object is selected
           if (obj.gridDistort && obj.gridDistort.showGrid && obj === canvasObjects[selectedObjectIndex]) {
               drawGrid(obj, targetCtx);
           }
       } else {
           console.warn(`🔍 GRID DISTORT CALL [${drawCallId}]: No fonts available, using normal rendering`);
           // Fallback to normal rendering if no font is loaded
           drawNormalOrSkewObject(obj, targetCtx);
       }
   }

   // Helper function to get font path for a font family
   function getFontPathForFamily(fontFamily, isBold = false, isItalic = false) {
       // Map font family to font file path using local fonts
       const fontMap = {
           'Arial': {
               regular: '/fonts/Arial.ttf',
               bold: '/fonts/Arial-Bold.ttf',
               italic: '/fonts/Arial-Italic.ttf',
               boldItalic: '/fonts/Arial-BoldItalic.ttf'
           },
           'Verdana': {
               regular: '/fonts/Verdana.ttf',
               bold: '/fonts/Verdana-Bold.ttf',
               italic: '/fonts/Verdana-Italic.ttf',
               boldItalic: '/fonts/Verdana-BoldItalic.ttf'
           },
           'Georgia': {
               regular: '/fonts/Georgia.ttf',
               bold: '/fonts/Georgia-Bold.ttf',
               italic: '/fonts/Georgia-Italic.ttf',
               boldItalic: '/fonts/Georgia-BoldItalic.ttf'
           },
           'Times New Roman': {
               regular: '/fonts/Times New Roman.ttf',
               bold: '/fonts/Times New Roman Bold.ttf',
               italic: '/fonts/Times New Roman Italic.ttf',
               boldItalic: '/fonts/Times New Roman Bold Italic.ttf'
           },
           'Courier New': {
               regular: '/fonts/CourierNew.ttf',
               bold: '/fonts/CourierNew-Bold.ttf',
               italic: '/fonts/CourierNew-Italic.ttf',
               boldItalic: '/fonts/CourierNew-BoldItalic.ttf'
           },
           'Impact': {
               regular: '/fonts/Impact.ttf',
               bold: '/fonts/Impact.ttf', // Impact doesn't typically have a bold variant
               italic: '/fonts/Impact.ttf', // Impact doesn't typically have an italic variant
               boldItalic: '/fonts/Impact.ttf' // Impact doesn't typically have a bold-italic variant
           },
           'Comic Sans MS': {
               regular: '/fonts/ComicSansMS.ttf',
               bold: '/fonts/ComicSansMS-Bold.ttf',
               italic: '/fonts/ComicSansMS.ttf', // Comic Sans MS might not have a specific italic file
               boldItalic: '/fonts/ComicSansMS-Bold.ttf' // Comic Sans MS might not have a specific bold-italic file
           },
           'Poppins': {
               regular: '/fonts/Poppins-Regular.ttf',
               bold: '/fonts/Poppins-Bold.ttf',
               italic: '/fonts/Poppins-Italic.ttf',
               boldItalic: '/fonts/Poppins-BoldItalic.ttf'
           }
       };

       // Determine which variant to use
       let variant = 'regular';
       if (isBold && isItalic) {
           variant = 'boldItalic';
       } else if (isBold) {
           variant = 'bold';
       } else if (isItalic) {
           variant = 'italic';
       }

       // Get the font map for the requested family
       const familyMap = fontMap[fontFamily];

       // If we have a map for this family, return the appropriate variant
       if (familyMap) {
           return familyMap[variant];
       }

       // Use Poppins as a fallback since it's likely to exist
       return fontMap['Poppins'][variant];
   }

   // Initialize grid control points for a text object
   function initializeGridPoints(textObj) {
       // Make sure gridDistort object exists
       if (!textObj.gridDistort) {
           console.log('Creating gridDistort object for text:', textObj.text);
           textObj.gridDistort = {
               gridCols: textObj.gridDistortCols || 2,
               gridRows: textObj.gridDistortRows || 1,
               gridPadding: textObj.gridDistortPadding || 120,
               intensity: (textObj.gridDistortIntensity || 100) / 100,
               controlPoints: [],
               showGrid: true,
               lastFontSize: textObj.fontSize,
               lastText: textObj.text,
               relativeControlPoints: [],
               verticalOnly: textObj.gridDistortVerticalOnly !== undefined ? textObj.gridDistortVerticalOnly : true
           };
       }

       // Get accurate text dimensions using a temporary canvas
       const tempCtx = document.createElement('canvas').getContext('2d');

       // Make sure to set the correct font style
       const fontStyle = `${textObj.italic ? 'italic ' : ''}${textObj.bold ? 'bold ' : ''}${textObj.fontSize}px "${textObj.fontFamily}"`;
       tempCtx.font = fontStyle;

       // Apply letter spacing if specified
       const letterSpacing = textObj.letterSpacing !== undefined ? textObj.letterSpacing : 0;

       console.log('Measuring text with font style:', fontStyle, 'letter spacing:', letterSpacing);

       // Calculate text dimensions with letter spacing
       let textWidth;
       if (letterSpacing === 0) {
           // Standard text measurement
           textWidth = tempCtx.measureText(textObj.text.toUpperCase()).width;
       } else {
           // Calculate width with letter spacing
           const letters = textObj.text.toUpperCase().split('');
           let totalWidth = 0;

           // Sum the width of each letter
           letters.forEach(letter => {
               totalWidth += tempCtx.measureText(letter).width;
           });

           // Add letter spacing between characters
           if (letters.length > 1) {
               totalWidth += letterSpacing * (letters.length - 1);
           }

           textWidth = totalWidth;
       }

       // Get metrics for height calculation
       const metrics = tempCtx.measureText('M'); // Use a representative character
       const textHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent || textObj.fontSize;

       // Store the measured dimensions on the text object for reference
       textObj.measuredWidth = textWidth;
       textObj.measuredHeight = textHeight;

       // Log the text and its dimensions for debugging
       console.log('Initializing grid for text:', textObj.text, {
           width: textWidth,
           height: textHeight,
           fontSize: textObj.fontSize
       });

       // Calculate grid bounds with padding that scales with font size
       // Use a percentage of the font size for padding (e.g., 50% of font size)
       const paddingScale = 0.5; // 50% of font size
       const basePadding = textObj.gridDistort.gridPadding;
       const scaledPadding = Math.max(basePadding, textObj.fontSize * paddingScale);

       // Store the grid bounds for later use
       textObj.gridDistort.gridBounds = {
           width: textWidth,
           height: textHeight,
           padding: scaledPadding
       };

       // Calculate grid dimensions using the padding from gridBounds
       const padding = textObj.gridDistort.gridBounds.padding;
       const gridWidth = textWidth + padding * 2;
       const gridHeight = textHeight + padding * 2;
       const gridLeft = -textWidth/2 - padding;
       const gridTop = -textHeight/2 - padding;

       // Check if we need to preserve distortion from previous text
       const textChanged = textObj.gridDistort &&
                          textObj.gridDistort.lastText !== undefined &&
                          textObj.gridDistort.lastText !== textObj.text;

       // Always check for font size changes, not just significant ones
       const fontSizeChanged = textObj.gridDistort &&
                              textObj.gridDistort.lastFontSize &&
                              textObj.gridDistort.lastFontSize !== textObj.fontSize;

       console.log('Text/font change check:', {
           currentText: textObj.text,
           lastText: textObj.gridDistort ? textObj.gridDistort.lastText : 'undefined',
           textChanged: textChanged,
           fontSizeChanged: fontSizeChanged,
           oldFontSize: textObj.gridDistort ? textObj.gridDistort.lastFontSize : 'undefined',
           newFontSize: textObj.fontSize,
           hasRelativePoints: textObj.gridDistort &&
                             textObj.gridDistort.relativeControlPoints &&
                             textObj.gridDistort.relativeControlPoints.length > 0
       });

       // If text or font size changed and we have previous control points with relative positions, use them
       if ((textChanged || fontSizeChanged) &&
           textObj.gridDistort.relativeControlPoints &&
           textObj.gridDistort.relativeControlPoints.length > 0) {

           console.log('Text/font changed, preserving distortion pattern');

           // Initialize control points array
           textObj.gridDistort.controlPoints = [];

           // Get grid dimensions, ensuring at least 1 for rows and columns
           const gridRows = Math.max(1, textObj.gridDistort.gridRows);
           const gridCols = Math.max(1, textObj.gridDistort.gridCols);

           // Apply the relative control points to the new grid size
           for (let row = 0; row < textObj.gridDistort.relativeControlPoints.length; row++) {
               const rowPoints = [];
               for (let col = 0; col < textObj.gridDistort.relativeControlPoints[row].length; col++) {
                   const relPoint = textObj.gridDistort.relativeControlPoints[row][col];

                   // Convert relative position (0-1) back to absolute position
                   const x = gridLeft + relPoint.x * gridWidth;
                   const y = gridTop + relPoint.y * gridHeight;

                   rowPoints.push({ x, y });
               }
               textObj.gridDistort.controlPoints.push(rowPoints);
           }

           // Update the last text and font size AFTER we've used the old values for scaling
           textObj.gridDistort.lastText = textObj.text;
           textObj.gridDistort.lastFontSize = textObj.fontSize;

           return; // Skip the rest of the initialization since we've restored the points
       }

       // If we're here, we need to create new control points
       // Initialize control points array
       textObj.gridDistort.controlPoints = [];

       // Get grid dimensions, ensuring at least 1 for rows and columns
       const gridRows = Math.max(1, textObj.gridDistort.gridRows);
       const gridCols = Math.max(1, textObj.gridDistort.gridCols);

       // Special case for 1x1 grid (just 4 corner points)
       if (gridRows === 1 && gridCols === 1) {
           // Create a 2x2 grid (4 corner points) instead of a single point
           const topRow = [];
           const bottomRow = [];

           // Top-left
           topRow.push({ x: gridLeft, y: gridTop });
           // Top-right
           topRow.push({ x: gridLeft + gridWidth, y: gridTop });

           // Bottom-left
           bottomRow.push({ x: gridLeft, y: gridTop + gridHeight });
           // Bottom-right
           bottomRow.push({ x: gridLeft + gridWidth, y: gridTop + gridHeight });

           textObj.gridDistort.controlPoints.push(topRow);
           textObj.gridDistort.controlPoints.push(bottomRow);
       }
       // Special case for 1 row - create 2 rows with points at the edges
       else if (gridRows === 1) {
           const topRow = [];
           const bottomRow = [];

           // Create evenly spaced columns
           for (let col = 0; col <= gridCols; col++) {
               const xFactor = col / gridCols;
               const x = gridLeft + xFactor * gridWidth;

               // Add points at top and bottom edges
               topRow.push({ x, y: gridTop });
               bottomRow.push({ x, y: gridTop + gridHeight });
           }

           textObj.gridDistort.controlPoints.push(topRow);
           textObj.gridDistort.controlPoints.push(bottomRow);
       }
       // Special case for 1 column - create 2 columns with points at the edges
       else if (gridCols === 1) {
           // Create evenly spaced rows
           for (let row = 0; row <= gridRows; row++) {
               const rowPoints = [];
               const yFactor = row / gridRows;
               const y = gridTop + yFactor * gridHeight;

               // Add points at left and right edges
               rowPoints.push({ x: gridLeft, y });
               rowPoints.push({ x: gridLeft + gridWidth, y });

               textObj.gridDistort.controlPoints.push(rowPoints);
           }
       }
       // Normal case - create a grid with the specified number of rows and columns
       else {
           // Create grid points - these are in object space (relative to object center)
           for (let row = 0; row <= gridRows; row++) {
               const rowPoints = [];
               for (let col = 0; col <= gridCols; col++) {
                   // Calculate evenly spaced points
                   const xFactor = col / gridCols;
                   const yFactor = row / gridRows;

                   const x = gridLeft + xFactor * gridWidth;
                   const y = gridTop + yFactor * gridHeight;

                   rowPoints.push({ x, y });
               }
               textObj.gridDistort.controlPoints.push(rowPoints);
           }
       }

       console.log('Grid initialized with points:', textObj.gridDistort.controlPoints.length, 'rows');

       // Store the relative positions for future text changes
       storeRelativeControlPoints(textObj);

       // Update the last text and font size
       textObj.gridDistort.lastText = textObj.text;
       textObj.gridDistort.lastFontSize = textObj.fontSize;
   }

   // Draw grid for visualization
   function drawGrid(textObj, targetCtx) {
       if (!textObj.gridDistort || !textObj.gridDistort.controlPoints ||
           !textObj.gridDistort.controlPoints.length) {
           console.warn('No grid control points to draw');
           return;
       }

       const controlPoints = textObj.gridDistort.controlPoints;
       const gridRows = textObj.gridDistort.gridRows || 2;
       const gridCols = textObj.gridDistort.gridCols || 3;

       // Draw grid lines
       targetCtx.save();
       targetCtx.strokeStyle = '#3b82f6';
       targetCtx.lineWidth = 2 / scale; // Adjust line width based on zoom level

       console.log('Drawing grid with', controlPoints.length, 'rows and',
                  controlPoints[0] ? controlPoints[0].length : 0, 'columns');

       // Special case for 1x1 grid - draw a rectangle using the 4 corner points
       if (gridRows === 1 && gridCols === 1) {
           if (controlPoints.length >= 2 && controlPoints[0].length >= 2) {
               // Draw the rectangle outline
               targetCtx.beginPath();

               // Top edge
               targetCtx.moveTo(controlPoints[0][0].x, controlPoints[0][0].y);
               targetCtx.lineTo(controlPoints[0][1].x, controlPoints[0][1].y);

               // Right edge
               targetCtx.lineTo(controlPoints[1][1].x, controlPoints[1][1].y);

               // Bottom edge
               targetCtx.lineTo(controlPoints[1][0].x, controlPoints[1][0].y);

               // Left edge (back to start)
               targetCtx.lineTo(controlPoints[0][0].x, controlPoints[0][0].y);

               targetCtx.stroke();
           }
       }
       // Special case for 1 row - draw only the top and bottom edges
       else if (gridRows === 1) {
           if (controlPoints.length >= 2) {
               // Top edge
               targetCtx.beginPath();
               for (let col = 0; col < controlPoints[0].length; col++) {
                   const point = controlPoints[0][col];
                   if (col === 0) {
                       targetCtx.moveTo(point.x, point.y);
                   } else {
                       targetCtx.lineTo(point.x, point.y);
                   }
               }
               targetCtx.stroke();

               // Bottom edge
               targetCtx.beginPath();
               for (let col = 0; col < controlPoints[1].length; col++) {
                   const point = controlPoints[1][col];
                   if (col === 0) {
                       targetCtx.moveTo(point.x, point.y);
                   } else {
                       targetCtx.lineTo(point.x, point.y);
                   }
               }
               targetCtx.stroke();

               // Draw vertical lines connecting top and bottom
               if (gridCols > 0) {
                   for (let col = 0; col < controlPoints[0].length; col++) {
                       targetCtx.beginPath();
                       targetCtx.moveTo(controlPoints[0][col].x, controlPoints[0][col].y);
                       targetCtx.lineTo(controlPoints[1][col].x, controlPoints[1][col].y);
                       targetCtx.stroke();
                   }
               }
           }
       }
       // Special case for 1 column - draw only the left and right edges
       else if (gridCols === 1) {
           if (controlPoints[0] && controlPoints[0].length >= 2) {
               // Left edge
               targetCtx.beginPath();
               for (let row = 0; row < controlPoints.length; row++) {
                   const point = controlPoints[row][0];
                   if (row === 0) {
                       targetCtx.moveTo(point.x, point.y);
                   } else {
                       targetCtx.lineTo(point.x, point.y);
                   }
               }
               targetCtx.stroke();

               // Right edge
               targetCtx.beginPath();
               for (let row = 0; row < controlPoints.length; row++) {
                   const point = controlPoints[row][1];
                   if (row === 0) {
                       targetCtx.moveTo(point.x, point.y);
                   } else {
                       targetCtx.lineTo(point.x, point.y);
                   }
               }
               targetCtx.stroke();

               // Draw horizontal lines connecting left and right
               for (let row = 0; row < controlPoints.length; row++) {
                   targetCtx.beginPath();
                   targetCtx.moveTo(controlPoints[row][0].x, controlPoints[row][0].y);
                   targetCtx.lineTo(controlPoints[row][1].x, controlPoints[row][1].y);
                   targetCtx.stroke();
               }
           }
       }
       // Normal case - draw full grid
       else {
           // Draw horizontal grid lines
           for (let row = 0; row < controlPoints.length; row++) {
               targetCtx.beginPath();
               for (let col = 0; col < controlPoints[row].length; col++) {
                   const point = controlPoints[row][col];
                   if (col === 0) {
                       targetCtx.moveTo(point.x, point.y);
                   } else {
                       targetCtx.lineTo(point.x, point.y);
                   }
               }
               targetCtx.stroke();
           }

           // Draw vertical grid lines
           if (controlPoints[0]) {
               for (let col = 0; col < controlPoints[0].length; col++) {
                   targetCtx.beginPath();
                   for (let row = 0; row < controlPoints.length; row++) {
                       const point = controlPoints[row][col];
                       if (row === 0) {
                           targetCtx.moveTo(point.x, point.y);
                       } else {
                           targetCtx.lineTo(point.x, point.y);
                       }
                   }
                   targetCtx.stroke();
               }
           }
       }

       // Draw control points
       targetCtx.fillStyle = '#3b82f6';
       for (let row = 0; row < controlPoints.length; row++) {
           for (let col = 0; col < controlPoints[row].length; col++) {
               const point = controlPoints[row][col];
               targetCtx.beginPath();
               targetCtx.arc(point.x, point.y, 10 / scale, 0, Math.PI * 2); // Increased point size for better visibility
               targetCtx.fill();
           }
       }

       // Draw a border around the grid area
       if (textObj.gridDistort.gridBounds) {
           const bounds = textObj.gridDistort.gridBounds;
           const scaledPadding = bounds.padding; // This is already scaled with font size
           const width = bounds.width;
           const height = bounds.height;

           targetCtx.strokeStyle = '#ff6b6b'; // Different color for the border
           targetCtx.lineWidth = 1 / scale;
           targetCtx.setLineDash([5 / scale, 5 / scale]);

           // Draw rectangle around the text area (without padding)
           targetCtx.strokeRect(-width/2, -height/2, width, height);

           // Draw rectangle around the grid area (with padding)
           targetCtx.strokeStyle = '#3b82f6';
           targetCtx.strokeRect(-width/2 - scaledPadding, -height/2 - scaledPadding,
                               width + scaledPadding * 2, height + scaledPadding * 2);

           targetCtx.setLineDash([]); // Reset dash pattern
       }

       targetCtx.restore();
   }

   // Function to store relative positions of control points
   function storeRelativeControlPoints(textObj) {
       if (!textObj || !textObj.gridDistort || !textObj.gridDistort.controlPoints ||
           !textObj.gridDistort.gridBounds) {
           console.warn('Cannot store relative control points - missing required properties');
           return;
       }

       // Calculate grid dimensions
       const padding = textObj.gridDistort.gridBounds.padding;
       const textWidth = textObj.gridDistort.gridBounds.width;
       const textHeight = textObj.gridDistort.gridBounds.height;
       const gridWidth = textWidth + padding * 2;
       const gridHeight = textHeight + padding * 2;
       const gridLeft = -textWidth/2 - padding;
       const gridTop = -textHeight/2 - padding;

       // Initialize relative control points array
       textObj.gridDistort.relativeControlPoints = [];

       // Convert absolute positions to relative positions (0-1 range)
       for (let row = 0; row < textObj.gridDistort.controlPoints.length; row++) {
           const rowPoints = [];
           for (let col = 0; col < textObj.gridDistort.controlPoints[row].length; col++) {
               const point = textObj.gridDistort.controlPoints[row][col];

               // Convert to relative position (0-1 range)
               const relX = (point.x - gridLeft) / gridWidth;
               const relY = (point.y - gridTop) / gridHeight;

               rowPoints.push({ x: relX, y: relY });
           }
           textObj.gridDistort.relativeControlPoints.push(rowPoints);
       }

       console.log('Stored relative control points for preserving distortion', {
           gridDimensions: { width: gridWidth, height: gridHeight, left: gridLeft, top: gridTop },
           pointCount: textObj.gridDistort.controlPoints.length *
                      (textObj.gridDistort.controlPoints[0] ? textObj.gridDistort.controlPoints[0].length : 0),
           relativePointsCount: textObj.gridDistort.relativeControlPoints.length *
                               (textObj.gridDistort.relativeControlPoints[0] ?
                                textObj.gridDistort.relativeControlPoints[0].length : 0)
       });
   }

   // Variables to track grid point dragging
   let isDraggingGridPoint = false;
   let draggedPointRow = -1;
   let draggedPointCol = -1;
   let draggedObject = null;

   // Find if a point was clicked
   function findGridPointAt(obj, worldX, worldY) {
       if (!obj || !obj.gridDistort || !obj.gridDistort.controlPoints) {
           return { hit: false };
       }

       // Convert world coordinates to object-local coordinates
       const dx = worldX - obj.x;
       const dy = worldY - obj.y;
       const angleRad = -obj.rotation * Math.PI / 180;
       const cos = Math.cos(angleRad);
       const sin = Math.sin(angleRad);
       const localX = dx * cos - dy * sin;
       const localY = dx * sin + dy * cos;

       // Check each control point
       const controlPoints = obj.gridDistort.controlPoints;
       const hitRadius = 20 / scale; // Increased hit radius to make points easier to click

       for (let row = 0; row < controlPoints.length; row++) {
           for (let col = 0; col < controlPoints[row].length; col++) {
               const point = controlPoints[row][col];
               const dist = Math.sqrt(
                   Math.pow(point.x - localX, 2) +
                   Math.pow(point.y - localY, 2)
               );

               if (dist <= hitRadius) {
                   return {
                       hit: true,
                       row: row,
                       col: col
                   };
               }
           }
       }

       return { hit: false };
   }

   // Get warped position based on grid control points
   function getWarpedPosition(x, y, controlPoints, cols, rows, gridLeft, gridTop, gridWidth, gridHeight, intensity, verticalOnly = false) {
       try {
           // Handle special cases for 1 row or 1 column
           if (cols < 1 || rows < 1 || !controlPoints || !controlPoints.length) {
               return { x, y }; // Return original position for invalid grid
           }

           // Special case for 1x1 grid (just 4 corner points)
           if (cols === 1 && rows === 1) {
               // For a 1x1 grid, we just use the 4 corner points directly
               const p00 = controlPoints[0][0];
               const p10 = controlPoints[0][1];
               const p01 = controlPoints[1][0];
               const p11 = controlPoints[1][1];

               // Normalize coordinates to 0-1 range within the grid
               const u = Math.max(0, Math.min(1, (x - gridLeft) / gridWidth));
               const v = Math.max(0, Math.min(1, (y - gridTop) / gridHeight));

               // Smooth interpolation factors
               const fx2 = u * u * (3 - 2 * u);
               const fy2 = v * v * (3 - 2 * v);

               // Bilinear interpolation
               const topX = p00.x * (1 - fx2) + p10.x * fx2;
               const botX = p01.x * (1 - fx2) + p11.x * fx2;
               const topY = p00.y * (1 - fx2) + p10.y * fx2;
               const botY = p01.y * (1 - fx2) + p11.y * fx2;

               // Final interpolated position
               const warpedX = topX * (1 - fy2) + botX * fy2;
               const warpedY = topY * (1 - fy2) + botY * fy2;

               return {
                   // If verticalOnly is true, only apply the vertical distortion
                   x: verticalOnly ? x : x + (warpedX - x) * intensity,
                   y: y + (warpedY - y) * intensity
               };
           }

           // Special case for 1 row (which now has 2 rows of control points - top and bottom edges)
           if (rows === 1) {
               // For 1 row, we interpolate between top and bottom rows
               const u = Math.max(0, Math.min(1, (x - gridLeft) / gridWidth));
               const v = Math.max(0, Math.min(1, (y - gridTop) / gridHeight));

               // Get column index
               const cellX = Math.min(u * cols, cols - 0.001);
               const ix = Math.floor(cellX);
               const fx = cellX - ix;

               // Safety check
               if (ix < 0 || ix >= cols || !controlPoints[0] || !controlPoints[1] ||
                   !controlPoints[0][ix] || !controlPoints[0][ix+1] ||
                   !controlPoints[1][ix] || !controlPoints[1][ix+1]) {
                   return { x, y };
               }

               // Get the four control points (top and bottom row)
               const p00 = controlPoints[0][ix];     // top left
               const p10 = controlPoints[0][ix + 1]; // top right
               const p01 = controlPoints[1][ix];     // bottom left
               const p11 = controlPoints[1][ix + 1]; // bottom right

               // Smooth interpolation factors
               const fx2 = fx * fx * (3 - 2 * fx);
               const fy2 = v; // Linear interpolation for vertical (only 2 points)

               // Bilinear interpolation
               const topX = p00.x * (1 - fx2) + p10.x * fx2;
               const botX = p01.x * (1 - fx2) + p11.x * fx2;
               const topY = p00.y * (1 - fx2) + p10.y * fx2;
               const botY = p01.y * (1 - fx2) + p11.y * fx2;

               // Final interpolated position
               const warpedX = topX * (1 - fy2) + botX * fy2;
               const warpedY = topY * (1 - fy2) + botY * fy2;

               return {
                   // If verticalOnly is true, only apply the vertical distortion
                   x: verticalOnly ? x : x + (warpedX - x) * intensity,
                   y: y + (warpedY - y) * intensity
               };
           }

           // Special case for 1 column (which now has 2 columns of control points - left and right edges)
           if (cols === 1) {
               // For 1 column, we interpolate between left and right columns
               const u = Math.max(0, Math.min(1, (x - gridLeft) / gridWidth));
               const v = Math.max(0, Math.min(1, (y - gridTop) / gridHeight));

               // Get row index
               const cellY = Math.min(v * rows, rows - 0.001);
               const iy = Math.floor(cellY);
               const fy = cellY - iy;

               // Safety check
               if (iy < 0 || iy >= rows || !controlPoints[iy] || !controlPoints[iy+1] ||
                   !controlPoints[iy][0] || !controlPoints[iy][1] ||
                   !controlPoints[iy+1][0] || !controlPoints[iy+1][1]) {
                   return { x, y };
               }

               // Get the four control points (left and right columns)
               const p00 = controlPoints[iy][0];     // top left
               const p10 = controlPoints[iy][1];     // top right
               const p01 = controlPoints[iy+1][0];   // bottom left
               const p11 = controlPoints[iy+1][1];   // bottom right

               // Smooth interpolation factors
               const fx2 = u; // Linear interpolation for horizontal (only 2 points)
               const fy2 = fy * fy * (3 - 2 * fy);

               // Bilinear interpolation
               const topX = p00.x * (1 - fx2) + p10.x * fx2;
               const botX = p01.x * (1 - fx2) + p11.x * fx2;
               const topY = p00.y * (1 - fx2) + p10.y * fx2;
               const botY = p01.y * (1 - fx2) + p11.y * fx2;

               // Final interpolated position
               const warpedX = topX * (1 - fy2) + botX * fy2;
               const warpedY = topY * (1 - fy2) + botY * fy2;

               return {
                   x: x + (warpedX - x) * intensity,
                   y: y + (warpedY - y) * intensity
               };
           }

           // Normal case (multiple rows and columns)
           // Normalize coordinates to 0-1 range within the grid
           const nx = (x - gridLeft) / gridWidth;
           const ny = (y - gridTop) / gridHeight;

           // Clamp to valid range
           const u = Math.max(0, Math.min(1, nx));
           const v = Math.max(0, Math.min(1, ny));

           // Find grid cell
           const cellX = Math.min(u * cols, cols - 0.001);
           const cellY = Math.min(v * rows, rows - 0.001);

           // Get indices of the four corners
           const ix = Math.floor(cellX);
           const iy = Math.floor(cellY);

           // Safety check for valid indices
           if (ix < 0 || iy < 0 || ix >= cols || iy >= rows ||
               !controlPoints[iy] || !controlPoints[iy][ix] ||
               !controlPoints[iy][ix+1] || !controlPoints[iy+1] ||
               !controlPoints[iy+1][ix] || !controlPoints[iy+1][ix+1]) {
               return { x, y }; // Return original position if out of bounds
           }

           // Fractional position within the cell
           const fx = cellX - ix;
           const fy = cellY - iy;

           // Get the four control points around this position
           const p00 = controlPoints[iy][ix];
           const p10 = controlPoints[iy][ix + 1];
           const p01 = controlPoints[iy + 1][ix];
           const p11 = controlPoints[iy + 1][ix + 1];

           // Smooth interpolation factors (using cubic hermite)
           const fx2 = fx * fx * (3 - 2 * fx);
           const fy2 = fy * fy * (3 - 2 * fy);

           // Bilinear interpolation
           const topX = p00.x * (1 - fx2) + p10.x * fx2;
           const botX = p01.x * (1 - fx2) + p11.x * fx2;
           const topY = p00.y * (1 - fx2) + p10.y * fx2;
           const botY = p01.y * (1 - fx2) + p11.y * fx2;

           // Final interpolated position
           const warpedX = topX * (1 - fy2) + botX * fy2;
           const warpedY = topY * (1 - fy2) + botY * fy2;

           // Apply intensity factor
           const origX = x;
           const origY = y;

           return {
               // If verticalOnly is true, only apply the vertical distortion
               x: verticalOnly ? origX : origX + (warpedX - origX) * intensity,
               y: origY + (warpedY - origY) * intensity
           };
       } catch (e) {
           console.error('Error in getWarpedPosition:', e);
           return { x, y }; // Return original position on error
       }
   }

   // Draw grid distorted text using OpenType.js
   function drawGridDistortedText(textObj, targetCtx, font) {
       if (!font) {
           console.warn('Font not loaded for grid distort effect');
           return;
       }

       // Check if we have a valid text object
       if (!textObj || !textObj.text) {
           console.warn('Invalid text object for grid distort effect');
           return;
       }

       // Get text properties
       const text = textObj.text.toUpperCase();
       const fontSize = textObj.fontSize || 100; // Default to 100 if not set

       // Note: Opacity is handled individually for text and stroke
       // Do not apply global opacity here as it would affect stroke independence

       // Log the text style for debugging
       console.log('Drawing grid distorted text with style:', {
           text: text,
           fontSize: fontSize,
           fontFamily: textObj.fontFamily,
           bold: textObj.bold,
           italic: textObj.italic,
           opacity: (textObj.opacity !== undefined) ? textObj.opacity / 100 : 1,
           letterSpacing: textObj._effectiveLetterSpacing,
           fontKey: `${textObj.fontFamily}_${textObj.bold ? 'bold' : 'regular'}_${textObj.italic ? 'italic' : 'normal'}`
       });

       // Create a path for the text with letter spacing
       let path;
       try {
           // Make sure the _effectiveLetterSpacing property is set
           if (textObj._effectiveLetterSpacing === undefined) {
               textObj._effectiveLetterSpacing = textObj.letterSpacing || 0;
           }

           // Get letter spacing value
           const letterSpacing = textObj._effectiveLetterSpacing;

           console.log('Creating text path with letter spacing:', letterSpacing);

           if (letterSpacing === 0) {
               // Standard text path creation
               path = font.getPath(text, 0, 0, fontSize);
           } else {
               // Create a composite path with letter spacing
               path = new opentype.Path();
               let currentX = 0;

               // Process each character with letter spacing
               for (let i = 0; i < text.length; i++) {
                   const char = text[i];
                   // Get path for this character
                   const charPath = font.getPath(char, currentX, 0, fontSize);

                   // Add this character's path commands to the composite path
                   if (charPath && charPath.commands) {
                       for (const cmd of charPath.commands) {
                           path.commands.push(Object.assign({}, cmd));
                       }
                   }

                   // Calculate character width for positioning the next character
                   const charWidth = font.getAdvanceWidth(char, fontSize);

                   // Move to the next character position with letter spacing
                   currentX += charWidth + letterSpacing;
               }
           }
       } catch (e) {
           console.error('Error creating text path:', e);
           return;
       }

       // Get path bounds
       let bounds;
       try {
           bounds = path.getBoundingBox();
       } catch (e) {
           console.error('Error getting path bounds:', e);
           return;
       }

       // Use the measured dimensions if available, otherwise use the path bounds
       const textWidth = textObj.measuredWidth || (bounds.x2 - bounds.x1);
       const textHeight = textObj.measuredHeight || (bounds.y2 - bounds.y1);

       // Log the dimensions for debugging
       console.log('Text dimensions:', {
           measuredWidth: textObj.measuredWidth,
           measuredHeight: textObj.measuredHeight,
           pathWidth: bounds.x2 - bounds.x1,
           pathHeight: bounds.y2 - bounds.y1,
           finalWidth: textWidth,
           finalHeight: textHeight
       });

       // Calculate grid bounds with padding that scales with font size and accounts for stroke
       const paddingScale = 0.5; // 50% of font size
       const basePadding = textObj.gridDistort.gridPadding;
       const scaledPadding = Math.max(basePadding, fontSize * paddingScale);

       // Add extra padding for stroke to prevent clipping
       const strokePadding = (textObj.strokeMode === "stroke" && textObj.strokeWidth) ?
           Math.max(textObj.strokeWidth * 30, 150) : 0; // 30x stroke width with 150px minimum

       // Add extra padding based on distortion intensity to account for extreme distortions
       // Use a more generous multiplier to ensure we capture all possible distortions
       const intensityPadding = (textObj.gridDistort.intensity / 100) * Math.max(textWidth, textHeight) * 0.5;

       const totalPadding = scaledPadding + strokePadding + intensityPadding;

       // Since we're already translated to textObj.x, textObj.y in drawTextObject,
       // we need to use 0,0 as the center point here, not textObj.x, textObj.y
       const gridLeft = -textWidth/2 - totalPadding;
       const gridTop = -textHeight/2 - totalPadding;
       const gridWidth = textWidth + totalPadding * 2;
       const gridHeight = textHeight + totalPadding * 2;

       console.log('Grid bounds calculation:', {
           gridLeft, gridTop, gridWidth, gridHeight, textWidth, textHeight,
           scaledPadding, strokePadding, intensityPadding, totalPadding,
           strokeMode: textObj.strokeMode, strokeWidth: textObj.strokeWidth,
           intensity: textObj.gridDistort.intensity,
           basePadding: textObj.gridDistort.gridPadding,
           fontSize: fontSize
       });

       // Set text properties - let normal gradient system handle gradients
       targetCtx.fillStyle = textObj.color;
       console.log('🎨 Grid Distort: Set fillStyle to solid color:', textObj.color);

       // Create a warped path based on the original path
       const warpedPath = new opentype.Path();

       // Center offset to position text at object center
       // This is the key difference from the previous implementation
       const centerX = -textWidth / 2 - bounds.x1;
       const centerY = -textHeight / 2 - bounds.y1;

       // Process each command in the original path
       for (let i = 0; i < path.commands.length; i++) {
           const cmd = path.commands[i];

           switch (cmd.type) {
               case 'M': // Move to
                   {
                       const x = cmd.x + centerX;
                       const y = cmd.y + centerY;
                       const warped = getWarpedPosition(
                           x, y,
                           textObj.gridDistort.controlPoints,
                           textObj.gridDistort.gridCols,
                           textObj.gridDistort.gridRows,
                           gridLeft, gridTop, gridWidth, gridHeight,
                           textObj.gridDistort.intensity,
                           textObj.gridDistortVerticalOnly
                       );
                       warpedPath.moveTo(warped.x, warped.y);
                   }
                   break;
               case 'L': // Line to
                   {
                       const x = cmd.x + centerX;
                       const y = cmd.y + centerY;
                       const warped = getWarpedPosition(
                           x, y,
                           textObj.gridDistort.controlPoints,
                           textObj.gridDistort.gridCols,
                           textObj.gridDistort.gridRows,
                           gridLeft, gridTop, gridWidth, gridHeight,
                           textObj.gridDistort.intensity,
                           textObj.gridDistortVerticalOnly
                       );
                       warpedPath.lineTo(warped.x, warped.y);
                   }
                   break;
               case 'C': // Cubic curve
                   {
                       const x1 = cmd.x1 + centerX;
                       const y1 = cmd.y1 + centerY;
                       const x2 = cmd.x2 + centerX;
                       const y2 = cmd.y2 + centerY;
                       const x = cmd.x + centerX;
                       const y = cmd.y + centerY;

                       const warped1 = getWarpedPosition(
                           x1, y1,
                           textObj.gridDistort.controlPoints,
                           textObj.gridDistort.gridCols,
                           textObj.gridDistort.gridRows,
                           gridLeft, gridTop, gridWidth, gridHeight,
                           textObj.gridDistort.intensity,
                           textObj.gridDistortVerticalOnly
                       );

                       const warped2 = getWarpedPosition(
                           x2, y2,
                           textObj.gridDistort.controlPoints,
                           textObj.gridDistort.gridCols,
                           textObj.gridDistort.gridRows,
                           gridLeft, gridTop, gridWidth, gridHeight,
                           textObj.gridDistort.intensity,
                           textObj.gridDistortVerticalOnly
                       );

                       const warped = getWarpedPosition(
                           x, y,
                           textObj.gridDistort.controlPoints,
                           textObj.gridDistort.gridCols,
                           textObj.gridDistort.gridRows,
                           gridLeft, gridTop, gridWidth, gridHeight,
                           textObj.gridDistort.intensity,
                           textObj.gridDistortVerticalOnly
                       );

                       warpedPath.curveTo(warped1.x, warped1.y, warped2.x, warped2.y, warped.x, warped.y);
                   }
                   break;
               case 'Q': // Quadratic curve
                   {
                       const x1 = cmd.x1 + centerX;
                       const y1 = cmd.y1 + centerY;
                       const x = cmd.x + centerX;
                       const y = cmd.y + centerY;

                       const warped1 = getWarpedPosition(
                           x1, y1,
                           textObj.gridDistort.controlPoints,
                           textObj.gridDistort.gridCols,
                           textObj.gridDistort.gridRows,
                           gridLeft, gridTop, gridWidth, gridHeight,
                           textObj.gridDistort.intensity,
                           textObj.gridDistortVerticalOnly
                       );

                       const warped = getWarpedPosition(
                           x, y,
                           textObj.gridDistort.controlPoints,
                           textObj.gridDistort.gridCols,
                           textObj.gridDistort.gridRows,
                           gridLeft, gridTop, gridWidth, gridHeight,
                           textObj.gridDistort.intensity,
                           textObj.gridDistortVerticalOnly
                       );

                       warpedPath.quadraticCurveTo(warped1.x, warped1.y, warped.x, warped.y);
                   }
                   break;
               case 'Z': // Close path
                   warpedPath.closePath();
                   break;
           }
       }

       // Apply shadow effects before drawing the path
       targetCtx.save();

       // Apply standard shadow if enabled
       if (textObj.shadowMode === "shadow") {
           targetCtx.shadowColor = textObj.shadowColor;
           targetCtx.shadowOffsetX = textObj.shadowOffsetX;
           targetCtx.shadowOffsetY = textObj.shadowOffsetY;
           targetCtx.shadowBlur = textObj.shadowBlur;
       }

       // Apply other shadow types if enabled
       if (textObj.shadowMode === "blockShadow") {
           applyBlockShadow(targetCtx, textObj, 0, 0, warpedPath);
       } else if (textObj.shadowMode === "perspectiveShadow") {
           applyPerspectiveShadow(targetCtx, textObj, 0, 0, warpedPath);
       } else if (textObj.shadowMode === "lineShadow") {
           applyLineShadow(targetCtx, textObj, 0, 0, warpedPath);
       } else if (textObj.shadowMode === "detailed3D") {
           applyDetailed3D_ExtrusionOnly(targetCtx, textObj, 0, 0, warpedPath);
       }

       // Apply stroke if enabled (before fill) - outward only
       if (textObj.strokeMode === "stroke") {
           // Apply stroke with independent opacity - outward only like shapes
           const strokeOpacity = (textObj.strokeOpacity !== undefined) ? textObj.strokeOpacity / 100 : 1;
           const strokeColor = hexToRgba(textObj.strokeColor, strokeOpacity);

           // Create a temporary canvas for outward-only stroke - make it much larger to accommodate stroke
           const strokeCanvas = document.createElement('canvas');
           // Use very generous extra space - account for distortion displacement + stroke width
           const baseExtraSpace = Math.max(textObj.strokeWidth * 60, 400);
           const distortionExtraSpace = (textObj.gridDistort.intensity / 100) * Math.max(textWidth, textHeight) * 1.5;
           const extraStrokeSpace = baseExtraSpace + distortionExtraSpace;

           strokeCanvas.width = targetCtx.canvas.width + extraStrokeSpace * 2;
           strokeCanvas.height = targetCtx.canvas.height + extraStrokeSpace * 2;
           const strokeCtx = strokeCanvas.getContext('2d');

           console.log('Stroke canvas setup:', {
               originalCanvasSize: { width: targetCtx.canvas.width, height: targetCtx.canvas.height },
               strokeCanvasSize: { width: strokeCanvas.width, height: strokeCanvas.height },
               baseExtraSpace: baseExtraSpace,
               distortionExtraSpace: distortionExtraSpace,
               totalExtraSpace: extraStrokeSpace,
               strokeWidth: textObj.strokeWidth,
               intensity: textObj.gridDistort.intensity
           });

           // Translate to account for the extra space
           strokeCtx.translate(extraStrokeSpace, extraStrokeSpace);

           // Disable image smoothing for sharp strokes
           strokeCtx.imageSmoothingEnabled = false;

           strokeCtx.save();
           // Reset globalAlpha to ensure stroke has independent opacity
           strokeCtx.globalAlpha = 1;
           // Use a moderately thicker stroke that will be partially cut out
           // Scale the multiplier based on stroke width to avoid overly thick strokes for small widths
           const strokeMultiplier = textObj.strokeWidth <= 1 ? 5 :
                                   textObj.strokeWidth <= 2 ? 4 :
                                   textObj.strokeWidth <= 5 ? 3 : 2;
           const effectiveStrokeWidth = Math.max(textObj.strokeWidth * strokeMultiplier, 8);

           console.log('Stroke width calculation:', {
               originalStrokeWidth: textObj.strokeWidth,
               strokeMultiplier: strokeMultiplier,
               effectiveStrokeWidth: effectiveStrokeWidth
           });

           strokeCtx.lineWidth = effectiveStrokeWidth;
           strokeCtx.strokeStyle = strokeColor;
           strokeCtx.lineJoin = 'round';
           strokeCtx.lineCap = 'round';

           // Create paths for stroking and filling
           const strokePath = new Path2D();
           const fillPath = new Path2D();

           // Convert OpenType.js path to Canvas Path2D
           for (let i = 0; i < warpedPath.commands.length; i++) {
               const cmd = warpedPath.commands[i];
               switch (cmd.type) {
                   case 'M':
                       strokePath.moveTo(cmd.x, cmd.y);
                       fillPath.moveTo(cmd.x, cmd.y);
                       break;
                   case 'L':
                       strokePath.lineTo(cmd.x, cmd.y);
                       fillPath.lineTo(cmd.x, cmd.y);
                       break;
                   case 'C':
                       strokePath.bezierCurveTo(cmd.x1, cmd.y1, cmd.x2, cmd.y2, cmd.x, cmd.y);
                       fillPath.bezierCurveTo(cmd.x1, cmd.y1, cmd.x2, cmd.y2, cmd.x, cmd.y);
                       break;
                   case 'Q':
                       strokePath.quadraticCurveTo(cmd.x1, cmd.y1, cmd.x, cmd.y);
                       fillPath.quadraticCurveTo(cmd.x1, cmd.y1, cmd.x, cmd.y);
                       break;
                   case 'Z':
                       strokePath.closePath();
                       fillPath.closePath();
                       break;
               }
           }

           // Draw the thick stroke
           strokeCtx.stroke(strokePath);

           // Use destination-out to cut out the interior (normal text area)
           strokeCtx.globalCompositeOperation = 'destination-out';
           strokeCtx.fillStyle = 'black'; // Color doesn't matter for destination-out
           strokeCtx.fill(fillPath);
           strokeCtx.restore();

           // Draw the outward-only stroke to the target canvas, accounting for the offset
           targetCtx.save();
           targetCtx.globalAlpha = 1; // Reset target context opacity for stroke
           targetCtx.drawImage(strokeCanvas, -extraStrokeSpace, -extraStrokeSpace);
           targetCtx.restore();
       }

       // Draw the warped path with fill color and opacity
       const textOpacity = (textObj.opacity !== undefined) ? textObj.opacity / 100 : 1;

       // Apply text opacity only to the fill (not stroke)
       targetCtx.save();
       targetCtx.globalAlpha = textOpacity;
       warpedPath.fill = textObj.color;
       warpedPath.draw(targetCtx);
       targetCtx.restore();

       // Apply decoration effects if enabled
       if (textObj.decorationMode && textObj.decorationMode !== "noDecoration") {
           // Handle fill decoration (patterns, gradients, etc.)
           applyDecoration(targetCtx, textObj, warpedPath);
       }

       // REMOVED: All front outline logic from drawGridDistortedText
       // Front outlines for Grid Distort are now ONLY handled by the gradient masking system
       console.log('🔍 FRONT OUTLINE SOURCE #2: All front outlines removed from drawGridDistortedText - handled by gradient masking only');

       // Log the effective letter spacing for debugging
       console.log('Grid Distort text rendered with letter spacing:', textObj._effectiveLetterSpacing || textObj.letterSpacing || 0);

       targetCtx.restore();
   }

   // Apply block shadow to a path
   function applyBlockShadow(targetCtx, textObj, x, y, path) {
       // If no path is provided, use the standard text-based function
       if (!path) {
           // Use the original function for text objects
           const color = textObj.blockShadowColor;
           const opacity = textObj.blockShadowOpacity / 100;
           const offset = textObj.blockShadowOffset;
           const angleDeg = textObj.blockShadowAngle;
           const blur = textObj.blockShadowBlur;
           const offsetCoords = calculateOffset(offset, angleDeg);

           targetCtx.save();
           setTextContextOn(targetCtx, textObj);
           targetCtx.fillStyle = hexToRgba(color, opacity);

           if (blur > 0) {
               targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
               targetCtx.shadowBlur = blur;
               targetCtx.shadowOffsetX = 0;
               targetCtx.shadowOffsetY = 0;
           }

           const steps = Math.max(10, Math.floor(offset / 1.5));
           for (let i = steps; i >= 1; i--) {
               const progress = i / steps;
               const currentX = x + offsetCoords.x * progress;
               const currentY = y + offsetCoords.y * progress;

               if (blur > 5 && i < steps) {
                   targetCtx.shadowColor = 'transparent';
               }

               targetCtx.fillText((textObj.text || '').toUpperCase(), currentX, currentY);

               if (blur > 5 && i < steps) {
                   targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
               }
           }

           targetCtx.restore();
           return;
       }

       // Path-based block shadow implementation
       const color = textObj.blockShadowColor;
       const opacity = textObj.blockShadowOpacity / 100;
       const offset = textObj.blockShadowOffset;
       const angleDeg = textObj.blockShadowAngle;
       const blur = textObj.blockShadowBlur;
       const offsetCoords = calculateOffset(offset, angleDeg);

       targetCtx.save();
       targetCtx.fillStyle = hexToRgba(color, opacity);

       if (blur > 0) {
           targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
           targetCtx.shadowBlur = blur;
           targetCtx.shadowOffsetX = 0;
           targetCtx.shadowOffsetY = 0;
       }

       // Create a Path2D object from the OpenType.js path
       const shadowPath = new Path2D();
       for (let i = 0; i < path.commands.length; i++) {
           const cmd = path.commands[i];
           switch (cmd.type) {
               case 'M':
                   shadowPath.moveTo(cmd.x, cmd.y);
                   break;
               case 'L':
                   shadowPath.lineTo(cmd.x, cmd.y);
                   break;
               case 'C':
                   shadowPath.bezierCurveTo(cmd.x1, cmd.y1, cmd.x2, cmd.y2, cmd.x, cmd.y);
                   break;
               case 'Q':
                   shadowPath.quadraticCurveTo(cmd.x1, cmd.y1, cmd.x, cmd.y);
                   break;
               case 'Z':
                   shadowPath.closePath();
                   break;
           }
       }

       // Draw the shadow in steps
       const steps = Math.max(10, Math.floor(offset / 1.5));
       for (let i = steps; i >= 1; i--) {
           const progress = i / steps;

           targetCtx.save();
           targetCtx.translate(offsetCoords.x * progress, offsetCoords.y * progress);

           if (blur > 5 && i < steps) {
               targetCtx.shadowColor = 'transparent';
           }

           targetCtx.fill(shadowPath);

           if (blur > 5 && i < steps) {
               targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
           }

           targetCtx.restore();
       }

       targetCtx.restore();
   }

   // Apply perspective shadow to a path
   function applyPerspectiveShadow(targetCtx, textObj, x, y, path) {
       // Generate a unique ID for this rendering pass to track in logs
       const renderPassId = Math.random().toString(36).substring(2, 8);

       console.log(`=== PERSPECTIVE SHADOW START (${renderPassId}) ===`);
       console.log(`[${renderPassId}] Text object:`, textObj.text);

       // If no path is provided, use the standard text-based function
       if (!path) {
           // Use the original function for text objects
           const color = textObj.perspectiveShadowColor || '#000000';
           const opacity = (textObj.perspectiveShadowOpacity !== undefined) ? textObj.perspectiveShadowOpacity / 100 : 0.6;

           // Use more reasonable default values for offset and angle
           // Default offset should be much smaller (40px instead of 103px)
           const offset = (textObj.perspectiveShadowOffset !== undefined) ?
               Math.min(textObj.perspectiveShadowOffset, 100) : 40;

           // Default angle should be -45 degrees (down and to the right)
           const angleDeg = (textObj.perspectiveShadowAngle !== undefined) ?
               textObj.perspectiveShadowAngle : -45;

           const blur = (textObj.perspectiveShadowBlur !== undefined) ?
               textObj.perspectiveShadowBlur : 5;

           const perspectiveIntensity = (textObj.perspectiveShadowIntensity || 60) / 100;
           const offsetCoords = calculateOffset(offset, angleDeg);

           // Get outline properties
           const outlineColor = textObj.perspectiveShadowOutlineColor || '#00FF00';
           const outlineOpacity = (textObj.perspectiveShadowOutlineOpacity !== undefined) ?
               textObj.perspectiveShadowOutlineOpacity / 100 : 1.0;
           const outlineWidth = textObj.perspectiveShadowOutlineWidth || 4;
           const outlineOffsetX = textObj.perspectiveShadowOutlineOffsetX || -5;
           const outlineOffsetY = textObj.perspectiveShadowOutlineOffsetY || -5;

           console.log(`[${renderPassId}] Perspective Shadow properties:`, {
               color,
               opacity,
               offset,
               angleDeg,
               blur,
               perspectiveIntensity,
               shadowMode: textObj.shadowMode,
               offsetCoords,
               outlineColor,
               outlineOpacity,
               outlineWidth,
               outlineOffsetX,
               outlineOffsetY
           });

           targetCtx.save();
           setTextContextOn(targetCtx, textObj);
           // Use a lower base opacity for smoother blending between shadow layers
           const baseOpacity = Math.min(0.8, opacity);
           targetCtx.fillStyle = hexToRgba(color, baseOpacity);

           if (blur > 0) {
               targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
               targetCtx.shadowBlur = blur;
               targetCtx.shadowOffsetX = 0;
               targetCtx.shadowOffsetY = 0;
           }

           // Calculate number of steps based on offset
           // Use more steps to create a more solid block-like appearance
           // Minimum of 50 steps, and scale up with offset
           const steps = Math.max(50, Math.floor(offset * 2.5));
           console.log(`[${renderPassId}] Number of shadow steps:`, steps);

           // Draw shadows from back to front (farthest to closest)
           // This is the correct order for perspective shadows
           for (let i = 1; i <= steps; i++) {
               // Calculate progress from 0.0 (farthest from text) to 1.0 (closest to text)
               const progress = i / steps;

               // Calculate distance ratio - 0.0 is farthest from text, 1.0 is closest
               const distanceRatio = i / steps;

               // Apply scaling that makes distant shadows smaller
               // Higher intensity = more dramatic scaling at distance (smaller end point)
               // The intensity now controls how small the farthest shadow gets
               // With intensity=1%, the scale will be almost the same as the original text (0.95)
               // With intensity=100%, the scale will be very small at the farthest point (0.05)
               const minScale = 0.95 - (perspectiveIntensity * 0.9); // Minimum scale at farthest point (0.05 to 0.95)
               const scaleProgress = minScale + ((1 - minScale) * distanceRatio);

               if (i % 5 === 0 || i === 1 || i === steps) {
                   console.log(`[${renderPassId}] Shadow step ${i}/${steps}: distanceRatio=${distanceRatio.toFixed(2)}, scaleProgress=${scaleProgress.toFixed(3)}`);
               }

               // Calculate position for this shadow layer
               // Use the original position (x,y) as the center point
               // and apply the offset relative to that position
               // We're drawing from farthest to closest, so the farthest shadow (i=1)
               // should have the largest offset, and the closest shadow (i=steps) should have the smallest offset
               // Invert the progress to get the correct offset
               const offsetProgress = 1 - progress;
               const posX = x + (offsetCoords.x * offsetProgress * offset);
               const posY = y + (offsetCoords.y * offsetProgress * offset);

               if (i % 5 === 0 || i === 1 || i === steps) {
                   console.log(`[${renderPassId}] Shadow position: (${posX.toFixed(1)}, ${posY.toFixed(1)}), scale: ${scaleProgress.toFixed(3)}`);
               }

               // Save state before transformations
               targetCtx.save();

               // Move to the position where we'll draw this shadow layer
               targetCtx.translate(posX, posY);

               // Apply scaling for perspective effect
               targetCtx.scale(scaleProgress, scaleProgress);

               // Handle shadow blur
               if (blur > 5 && i < steps) {
                   targetCtx.shadowColor = 'transparent';
               }

               // Draw the text at origin (0,0) since we've already translated
               targetCtx.fillText((textObj.text || '').toUpperCase(), 0, 0);

               // Restore shadow if needed
               if (blur > 5 && i < steps) {
                   targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
               }

               // Restore the context state
               targetCtx.restore();
           }

           // We no longer need to draw the main text or outline here
           // The main text is drawn in renderStyledObjectToOffscreen
           // The outline is drawn by applyPerspectiveShadow_FrontOutline

           console.log(`[${renderPassId}] === PERSPECTIVE SHADOW END ===`);
           targetCtx.restore();
           return;
       }

       // Path-based perspective shadow implementation
       const color = textObj.perspectiveShadowColor;
       const opacity = textObj.perspectiveShadowOpacity / 100;
       const offset = textObj.perspectiveShadowOffset;
       const angleDeg = textObj.perspectiveShadowAngle;
       const blur = textObj.perspectiveShadowBlur;
       const perspectiveIntensity = (textObj.perspectiveShadowIntensity || 60) / 100;
       const offsetCoords = calculateOffset(offset, angleDeg);

       // Get outline properties
       const outlineColor = textObj.perspectiveShadowOutlineColor || '#00FF00';
       const outlineOpacity = (textObj.perspectiveShadowOutlineOpacity !== undefined) ?
           textObj.perspectiveShadowOutlineOpacity / 100 : 1.0;
       const outlineWidth = textObj.perspectiveShadowOutlineWidth || 4;
       const outlineOffsetX = textObj.perspectiveShadowOutlineOffsetX || -5;
       const outlineOffsetY = textObj.perspectiveShadowOutlineOffsetY || -5;

       console.log(`[${renderPassId}] Path-based perspective shadow properties:`, {
           color,
           opacity,
           offset,
           angleDeg,
           blur,
           perspectiveIntensity,
           shadowMode: textObj.shadowMode,
           offsetCoords,
           outlineColor,
           outlineOpacity,
           outlineWidth,
           outlineOffsetX,
           outlineOffsetY
       });

       // Create a Path2D object from the OpenType.js path
       const shadowPath = new Path2D();
       for (let i = 0; i < path.commands.length; i++) {
           const cmd = path.commands[i];
           switch (cmd.type) {
               case 'M':
                   shadowPath.moveTo(cmd.x, cmd.y);
                   break;
               case 'L':
                   shadowPath.lineTo(cmd.x, cmd.y);
                   break;
               case 'C':
                   shadowPath.bezierCurveTo(cmd.x1, cmd.y1, cmd.x2, cmd.y2, cmd.x, cmd.y);
                   break;
               case 'Q':
                   shadowPath.quadraticCurveTo(cmd.x1, cmd.y1, cmd.x, cmd.y);
                   break;
               case 'Z':
                   shadowPath.closePath();
                   break;
           }
       }

       targetCtx.save();
       // Use a lower base opacity for smoother blending between shadow layers
       const baseOpacity = Math.min(0.8, opacity);
       targetCtx.fillStyle = hexToRgba(color, baseOpacity);

       if (blur > 0) {
           targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
           targetCtx.shadowBlur = blur;
           targetCtx.shadowOffsetX = 0;
           targetCtx.shadowOffsetY = 0;
       }

       // Draw the shadow in steps with perspective scaling
       // Use more steps to create a more solid block-like appearance
       const steps = Math.max(50, Math.floor(offset * 2.5));
       console.log(`[${renderPassId}] Number of path shadow steps:`, steps);

       // Draw shadows from back to front (farthest to closest)
       // This is the correct order for perspective shadows
       for (let i = 1; i <= steps; i++) {
           // Calculate progress from 0.0 (farthest from text) to 1.0 (closest to text)
           const progress = i / steps;
           // Calculate distance ratio - 0.0 is farthest from text, 1.0 is closest
           const distanceRatio = i / steps;
           // Apply scaling that makes distant shadows smaller
           // Higher intensity = more dramatic scaling at distance (smaller end point)
           // The intensity now controls how small the farthest shadow gets
           // With intensity=1%, the scale will be almost the same as the original text (0.95)
           // With intensity=100%, the scale will be very small at the farthest point (0.05)
           const minScale = 0.95 - (perspectiveIntensity * 0.9); // Minimum scale at farthest point (0.05 to 0.95)
           const scaleProgress = minScale + ((1 - minScale) * distanceRatio);

           if (i % 5 === 0 || i === 1 || i === steps) {
               console.log(`[${renderPassId}] Path shadow step ${i}/${steps}: scale=${scaleProgress.toFixed(3)}`);
           }

           targetCtx.save();

           // Invert the progress to get the correct offset (same as in the text-based version)
           // This ensures the shadow is drawn in the correct direction
           const offsetProgress = 1 - progress;

           // Apply both translation and scaling for perspective effect
           targetCtx.translate(offsetCoords.x * offsetProgress * offset, offsetCoords.y * offsetProgress * offset);
           targetCtx.scale(scaleProgress, scaleProgress);

           if (blur > 5 && i < steps) {
               targetCtx.shadowColor = 'transparent';
           }

           targetCtx.fill(shadowPath);

           if (blur > 5 && i < steps) {
               targetCtx.shadowColor = hexToRgba(color, opacity * 0.8);
           }

           targetCtx.restore();
       }

       // We no longer need to draw the main path or outline here
       // The main path is drawn in renderStyledObjectToOffscreen
       // The outline is drawn by applyPerspectiveShadow_FrontOutline

       console.log(`[${renderPassId}] === PERSPECTIVE SHADOW PATH END ===`);
       targetCtx.restore();
   }

   // Apply line shadow to a path
   function applyLineShadow(targetCtx, textObj, x, y, path) {
       // If no path is provided, use the standard text-based function
       if (!path) {
           // Use the original function for text objects
           const color = textObj.lineShadowColor;
           const distance = textObj.lineShadowDist;
           const angleDeg = textObj.lineShadowAngle;
           const thickness = Math.max(1, textObj.lineShadowThickness);
           const fullOffset = calculateOffset(distance, angleDeg);
           const cutterDistance = Math.max(0, distance - thickness);
           const cutterOffset = calculateOffset(cutterDistance, angleDeg);

           targetCtx.save();
           setTextContextOn(targetCtx, textObj);
           targetCtx.fillStyle = color;
           targetCtx.fillText((textObj.text || '').toUpperCase(), x + fullOffset.x, y + fullOffset.y);

           targetCtx.globalCompositeOperation = 'destination-out';
           targetCtx.fillStyle = 'black';
           targetCtx.fillText((textObj.text || '').toUpperCase(), x + cutterOffset.x, y + cutterOffset.y);

           targetCtx.restore();
           return;
       }

       // Path-based line shadow implementation
       const color = textObj.lineShadowColor;
       const distance = textObj.lineShadowDist;
       const angleDeg = textObj.lineShadowAngle;
       const thickness = Math.max(1, textObj.lineShadowThickness);
       const fullOffset = calculateOffset(distance, angleDeg);
       const cutterDistance = Math.max(0, distance - thickness);
       const cutterOffset = calculateOffset(cutterDistance, angleDeg);

       // Create a Path2D object from the OpenType.js path
       const shadowPath = new Path2D();
       for (let i = 0; i < path.commands.length; i++) {
           const cmd = path.commands[i];
           switch (cmd.type) {
               case 'M':
                   shadowPath.moveTo(cmd.x, cmd.y);
                   break;
               case 'L':
                   shadowPath.lineTo(cmd.x, cmd.y);
                   break;
               case 'C':
                   shadowPath.bezierCurveTo(cmd.x1, cmd.y1, cmd.x2, cmd.y2, cmd.x, cmd.y);
                   break;
               case 'Q':
                   shadowPath.quadraticCurveTo(cmd.x1, cmd.y1, cmd.x, cmd.y);
                   break;
               case 'Z':
                   shadowPath.closePath();
                   break;
           }
       }

       targetCtx.save();

       // Draw the shadow
       targetCtx.save();
       targetCtx.translate(fullOffset.x, fullOffset.y);
       targetCtx.fillStyle = color;
       targetCtx.fill(shadowPath);
       targetCtx.restore();

       // Cut out the inner part
       targetCtx.save();
       targetCtx.translate(cutterOffset.x, cutterOffset.y);
       targetCtx.globalCompositeOperation = 'destination-out';
       targetCtx.fillStyle = 'black';
       targetCtx.fill(shadowPath);
       targetCtx.restore();

       targetCtx.restore();
   }

   // Apply detailed 3D extrusion to a path
   function applyDetailed3D_ExtrusionOnly(targetCtx, textObj, x, y, path) {
       // If no path is provided, use the standard text-based function
       if (!path) {
           // Use the original function for text objects
           const primaryColorRgba = hexToRgba(textObj.d3dPrimaryColor, textObj.d3dPrimaryOpacity / 100);
           const offset = textObj.d3dOffset;
           const angle = textObj.d3dAngle;
           const blur = textObj.d3dBlur;

           targetCtx.save();
           setTextContextOn(targetCtx, textObj);

           const totalOffset = calculateOffset(offset, angle);
           const steps = Math.max(30, Math.floor(offset));

           for (let i = steps; i >= 1; i--) {
               const progress = i / steps;
               const currentOffset = {
                   x: totalOffset.x * progress,
                   y: totalOffset.y * progress
               };

               targetCtx.fillStyle = primaryColorRgba;
               targetCtx.fillText((textObj.text || '').toUpperCase(), x + currentOffset.x, y + currentOffset.y);
           }

           if (blur > 0) {
               targetCtx.save();
               targetCtx.shadowColor = primaryColorRgba;
               targetCtx.shadowBlur = blur;
               targetCtx.shadowOffsetX = 0;
               targetCtx.shadowOffsetY = 0;
               targetCtx.fillStyle = primaryColorRgba;
               targetCtx.fillText((textObj.text || '').toUpperCase(), x + totalOffset.x, y + totalOffset.y);
               targetCtx.restore();
           }

           targetCtx.restore();
           return;
       }

       // Path-based detailed 3D implementation
       const primaryColorRgba = hexToRgba(textObj.d3dPrimaryColor, textObj.d3dPrimaryOpacity / 100);
       const offset = textObj.d3dOffset;
       const angle = textObj.d3dAngle;
       const blur = textObj.d3dBlur;

       // Create a Path2D object from the OpenType.js path
       const shadowPath = new Path2D();
       for (let i = 0; i < path.commands.length; i++) {
           const cmd = path.commands[i];
           switch (cmd.type) {
               case 'M':
                   shadowPath.moveTo(cmd.x, cmd.y);
                   break;
               case 'L':
                   shadowPath.lineTo(cmd.x, cmd.y);
                   break;
               case 'C':
                   shadowPath.bezierCurveTo(cmd.x1, cmd.y1, cmd.x2, cmd.y2, cmd.x, cmd.y);
                   break;
               case 'Q':
                   shadowPath.quadraticCurveTo(cmd.x1, cmd.y1, cmd.x, cmd.y);
                   break;
               case 'Z':
                   shadowPath.closePath();
                   break;
           }
       }

       targetCtx.save();

       const totalOffset = calculateOffset(offset, angle);
       const steps = Math.max(30, Math.floor(offset));

       for (let i = steps; i >= 1; i--) {
           const progress = i / steps;

           targetCtx.save();
           targetCtx.translate(totalOffset.x * progress, totalOffset.y * progress);
           targetCtx.fillStyle = primaryColorRgba;
           targetCtx.fill(shadowPath);
           targetCtx.restore();
       }

       if (blur > 0) {
           targetCtx.save();
           targetCtx.translate(totalOffset.x, totalOffset.y);
           targetCtx.shadowColor = primaryColorRgba;
           targetCtx.shadowBlur = blur;
           targetCtx.shadowOffsetX = 0;
           targetCtx.shadowOffsetY = 0;
           targetCtx.fillStyle = primaryColorRgba;
           targetCtx.fill(shadowPath);
           targetCtx.restore();
       }

       targetCtx.restore();
   }

   // Apply decoration to a path
   function applyDecoration(ctx, textObj, path) {
       // Skip if no decoration mode is set
       if (!textObj.decorationMode || textObj.decorationMode === "noDecoration") {
           return;
       }

       // Create a Path2D object from the OpenType.js path
       const decorPath = new Path2D();
       for (let i = 0; i < path.commands.length; i++) {
           const cmd = path.commands[i];
           switch (cmd.type) {
               case 'M':
                   decorPath.moveTo(cmd.x, cmd.y);
                   break;
               case 'L':
                   decorPath.lineTo(cmd.x, cmd.y);
                   break;
               case 'C':
                   decorPath.bezierCurveTo(cmd.x1, cmd.y1, cmd.x2, cmd.y2, cmd.x, cmd.y);
                   break;
               case 'Q':
                   decorPath.quadraticCurveTo(cmd.x1, cmd.y1, cmd.x, cmd.y);
                   break;
               case 'Z':
                   decorPath.closePath();
                   break;
           }
       }

       // Apply different decoration types
       switch (textObj.decorationMode) {
           case 'horizontalLines':
               // Create a pattern of horizontal lines
               const hLineCanvas = document.createElement('canvas');
               const hLineSize = Math.max(10, textObj.hLineDist || 10);
               hLineCanvas.width = hLineSize;
               hLineCanvas.height = hLineSize;
               const hLineCtx = hLineCanvas.getContext('2d');

               hLineCtx.strokeStyle = textObj.hLineColor || '#000000';
               hLineCtx.lineWidth = Math.max(1, textObj.hLineWeight || 2);
               hLineCtx.beginPath();
               hLineCtx.moveTo(0, hLineSize / 2);
               hLineCtx.lineTo(hLineSize, hLineSize / 2);
               hLineCtx.stroke();

               const hLinePattern = ctx.createPattern(hLineCanvas, 'repeat');
               ctx.fillStyle = hLinePattern;
               ctx.fill(decorPath);
               break;

           case 'colorCut':
               // Apply color cut decoration with a simpler approach
               const cutDistance = textObj.ccDist || 50; // Default to 50% if not set
               const cutColor = textObj.ccColor || '#00FF00'; // Default to green if not set
               const fillDirection = textObj.ccFillDir || 'top'; // Default to top if not set

               // First fill the entire path with the text color
               ctx.fill(decorPath);

               // Create a clipping path
               ctx.save();
               ctx.beginPath();
               ctx.clip(decorPath);

               // For grid distorted text, we need a different approach
               // Since we're already translated to the object's position, we can use 0,0 as the center

               // Estimate the text height based on the font size
               // For grid distorted text, we need to use a larger multiplier
               // to ensure the effect covers the entire distorted text
               const heightMultiplier = textObj.effectMode === 'grid-distort' ? 3.0 : 1.5;
               const estimatedHeight = textObj.fontSize * heightMultiplier;

               // Calculate the cut position based on the estimated height
               const cutPercent = cutDistance / 100;
               const cutY = fillDirection === 'top'
                   ? -estimatedHeight/2 + (estimatedHeight * cutPercent)
                   : estimatedHeight/2 - (estimatedHeight * cutPercent);

               // Apply the color cut
               ctx.fillStyle = cutColor;

               if (fillDirection === 'top') {
                   // Fill from top to cut line
                   ctx.fillRect(-10000, -10000, 20000, cutY + 10000);
               } else {
                   // Fill from cut line to bottom
                   ctx.fillRect(-10000, cutY, 20000, 20000);
               }

               ctx.restore();
               break;

           case 'obliqueLines':
               // Special handling for grid distorted text
               if (textObj.effectMode === 'grid-distort') {
                   // For grid distorted text, we'll use a direct drawing approach instead of a pattern
                   // First fill with the text color
                   ctx.fill(decorPath);

                   // Create a clipping path to constrain the lines to the text shape
                   ctx.save();
                   ctx.beginPath();
                   ctx.clip(decorPath);

                   // Get the color for the lines
                   const lineColor = textObj.oLineColor || '#000000';
                   ctx.strokeStyle = lineColor;

                   // Get the line weight
                   const lineWeight = Math.max(1, textObj.oLineWeight || 2);
                   ctx.lineWidth = lineWeight;

                   // Get the line spacing
                   const lineSpacing = Math.max(10, textObj.oLineDist || 10);

                   // Calculate the bounds of the text to draw lines across
                   // We'll use a large area to ensure we cover the entire text
                   const bounds = {
                       left: -1000,
                       top: -1000,
                       right: 1000,
                       bottom: 1000,
                       width: 2000,
                       height: 2000
                   };

                   // Draw diagonal lines across the entire text area
                   ctx.beginPath();

                   // Draw lines from top-left to bottom-right
                   for (let x = bounds.left; x < bounds.right; x += lineSpacing) {
                       ctx.moveTo(x, bounds.top);
                       ctx.lineTo(x + bounds.width, bounds.top + bounds.height);
                   }

                   // Draw additional lines to ensure coverage
                   for (let y = bounds.top; y < bounds.bottom; y += lineSpacing) {
                       ctx.moveTo(bounds.left, y);
                       ctx.lineTo(bounds.left + bounds.width, y + bounds.height);
                   }

                   ctx.stroke();
                   ctx.restore();
               } else {
                   // Standard pattern-based approach for non-distorted text
                   const oLineCanvas = document.createElement('canvas');
                   const oLineSize = Math.max(10, textObj.oLineDist || 10);
                   oLineCanvas.width = oLineSize;
                   oLineCanvas.height = oLineSize;
                   const oLineCtx = oLineCanvas.getContext('2d');

                   oLineCtx.strokeStyle = textObj.oLineColor || '#000000';
                   oLineCtx.lineWidth = Math.max(1, textObj.oLineWeight || 2);
                   oLineCtx.beginPath();
                   oLineCtx.moveTo(0, oLineSize);
                   oLineCtx.lineTo(oLineSize, 0);
                   oLineCtx.stroke();

                   const oLinePattern = ctx.createPattern(oLineCanvas, 'repeat');
                   ctx.fillStyle = oLinePattern;
                   ctx.fill(decorPath);
               }
               break;

           case 'concentricCircles':
               // Create a pattern of concentric circles
               const ccCanvas = document.createElement('canvas');
               const ccSize = Math.max(20, textObj.ccDist || 20);
               ccCanvas.width = ccSize;
               ccCanvas.height = ccSize;
               const ccCtx = ccCanvas.getContext('2d');

               ccCtx.strokeStyle = textObj.ccColor || '#000000';
               ccCtx.lineWidth = 1;
               ccCtx.beginPath();
               ccCtx.arc(ccSize / 2, ccSize / 2, ccSize / 4, 0, Math.PI * 2);
               ccCtx.stroke();

               const ccPattern = ctx.createPattern(ccCanvas, 'repeat');
               ctx.fillStyle = ccPattern;
               ctx.fill(decorPath);
               break;

           case 'fishingLines':
               // Create a pattern of fishing lines
               const flCanvas = document.createElement('canvas');
               const flSize = Math.max(20, textObj.flcDist || 20);
               flCanvas.width = flSize;
               flCanvas.height = flSize;
               const flCtx = flCanvas.getContext('2d');

               flCtx.strokeStyle = textObj.flcColor || '#000000';
               flCtx.lineWidth = Math.max(1, textObj.flcWeight || 2);

               // Draw vertical lines with varying thickness
               const spacing = Math.max(4, textObj.flcSpacing || 5);
               for (let x = 0; x < flSize; x += spacing) {
                   flCtx.beginPath();
                   flCtx.moveTo(x, 0);
                   flCtx.lineTo(x, flSize);
                   flCtx.stroke();
               }

               const flPattern = ctx.createPattern(flCanvas, 'repeat');
               ctx.fillStyle = flPattern;
               ctx.fill(decorPath);
               break;
       }
   }

   // --- Main Drawing Logic Per Object ---
   function drawTextObject(obj, targetCtx) { // Added targetCtx
       if (!obj || obj.type !== 'text' || !obj.text) return;
       targetCtx.save(); // Use targetCtx
       targetCtx.translate(obj.x, obj.y); // Use targetCtx
       targetCtx.rotate(obj.rotation * Math.PI / 180); // Use targetCtx
       if (obj.effectMode === 'skew') {
           const skewXRad = obj.skewX / 100;
           const skewYRad = obj.skewY / 100;
           targetCtx.transform(1, skewYRad, skewXRad, 1, 0, 0); // Use targetCtx
       }
       targetCtx.textBaseline = "middle"; // Use targetCtx
       switch (obj.effectMode) {
           case 'normal': case 'skew': drawNormalOrSkewObject(obj, targetCtx); break; // Pass targetCtx
           case 'warp': drawWarpedObject(obj, targetCtx); break; // Pass targetCtx
           case 'circle': drawCircularObject(obj, targetCtx); break; // Pass targetCtx
           case 'curve': drawCurvedObject(obj, targetCtx); break; // Pass targetCtx
           case 'grid-distort':
               // Initialize grid if needed
               if (!obj.gridDistort) {
                   obj.gridDistort = {
                       gridCols: obj.gridDistortCols || 3,
                       gridRows: obj.gridDistortRows || 2,
                       gridPadding: obj.gridDistortPadding || 120,
                       intensity: (obj.gridDistortIntensity || 100) / 100,
                       controlPoints: [],
                       showGrid: true,
                       lastFontSize: obj.fontSize, // Store the current font size
                       lastText: obj.text // Store the current text content
                   };
                   // Set vertical-only mode based on radio button
                   obj.gridDistortVerticalOnly = obj.gridDistortVerticalOnly || false;
                   initializeGridPoints(obj);
               }
               drawGridDistortObject(obj, targetCtx);
               break; // Pass targetCtx
           case 'mesh':
               // Try to use the object's own mesh handler first, then fall back to active handler
               if (obj._meshWarpHandler) {
                   console.log('[MeshRender] Using object\'s own mesh handler for:', obj.text || obj.id);
                   obj._meshWarpHandler.drawWarpedText(targetCtx);
               } else if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler && activeMeshWarpHandler.selectedTextObject === obj) {
                   console.log('[MeshRender] Using active mesh handler for:', obj.text || obj.id);
                   activeMeshWarpHandler.drawWarpedText(targetCtx);
               } else {
                   // Fallback if no handler is available for this object
                   console.warn("Mesh effect selected, but no handler found for object:", obj.id);
                   drawNormalOrSkewObject(obj, targetCtx);
               }
               break;
           default:
               setTextContextOn(targetCtx, obj); // Use targetCtx
               targetCtx.fillStyle = obj.color; // Use targetCtx
               targetCtx.fillText(obj.text.toUpperCase() + ' (Unknown Effect)', 0, 0); // Use targetCtx
       }
       targetCtx.restore(); // Use targetCtx
   }
   function drawImageObject(obj, targetCtx) { // Added targetCtx
       if (!obj || obj.type !== 'image' || !obj.image) return;
       targetCtx.save(); // Use targetCtx
       targetCtx.translate(obj.x, obj.y); // Use targetCtx
       targetCtx.rotate(obj.rotation * Math.PI / 180); // Use targetCtx
       const scaledWidth = obj.originalWidth * obj.scale;
       const scaledHeight = obj.originalHeight * obj.scale;

       // Apply stroke/border if enabled (BEFORE drawing the main image)
       if (obj.strokeMode === 'standard') {
           const strokeWidth = obj.strokeWidth || 3;
           const strokeColor = obj.strokeColor || '#000000';

           // Apply independent stroke opacity (default 100% if not specified)
           const strokeOpacity = (obj.strokeOpacity !== undefined) ? obj.strokeOpacity / 100 : 1;
           targetCtx.save();
           targetCtx.globalAlpha = strokeOpacity;

           if (obj.imageUrl && obj.imageUrl.toLowerCase().endsWith('.svg')) {
               // Check if we need to regenerate the stroke cache
               const strokeCacheKey = `${obj.imageUrl}_${strokeWidth}_${strokeColor}_${scaledWidth}_${scaledHeight}`;

               if (!obj.strokeCache || obj.strokeCacheKey !== strokeCacheKey) {
                   // Use high-resolution rendering for ultra-sharp strokes
                   const hiResScale = 2; // 2x resolution for crisp rendering (more stable than 3x)
                   const padding = Math.max(strokeWidth * 8, 60); // 8x multiplier, 60px minimum

                   // Create high-resolution stroke canvas
                   const strokeCanvas = document.createElement('canvas');
                   strokeCanvas.width = (scaledWidth + padding * 2) * hiResScale;
                   strokeCanvas.height = (scaledHeight + padding * 2) * hiResScale;
                   const strokeCtx = strokeCanvas.getContext('2d');

                   // Clear the canvas to ensure no artifacts
                   strokeCtx.clearRect(0, 0, strokeCanvas.width, strokeCanvas.height);

                   // Scale up the context for high-resolution rendering
                   strokeCtx.scale(hiResScale, hiResScale);

                   // Improve stroke sharpness with pixel-perfect rendering
                   strokeCtx.imageSmoothingEnabled = false;
                   strokeCtx.webkitImageSmoothingEnabled = false;
                   strokeCtx.mozImageSmoothingEnabled = false;
                   strokeCtx.msImageSmoothingEnabled = false;

                   // Center everything in the padded canvas (use original coordinates since context is scaled)
                   const centerX = Math.floor((scaledWidth + padding * 2) / 2);
                   const centerY = Math.floor((scaledHeight + padding * 2) / 2);

                   // KNOCKOUT STROKE RENDERING: Create true outline effect
                   // This creates a clean outline by cutting out the interior of the stroke

                   // Step 1: Create the stroke outline by drawing the shape multiple times in stroke color
                   const directions = [];
                   const steps = Math.max(8, Math.ceil(strokeWidth / 2));
                   for (let i = 0; i < steps; i++) {
                       const angle = (i / steps) * Math.PI * 2;
                       const dx = Math.cos(angle) * strokeWidth;
                       const dy = Math.sin(angle) * strokeWidth;
                       directions.push([dx, dy]);
                   }

                   // Create high-resolution colored version of the image for stroke
                   const colorPadding = Math.max(strokeWidth * 8, 80);
                   const colorCanvas = document.createElement('canvas');
                   colorCanvas.width = (scaledWidth + colorPadding) * hiResScale;
                   colorCanvas.height = (scaledHeight + colorPadding) * hiResScale;
                   const colorCtx = colorCanvas.getContext('2d');
                   colorCtx.clearRect(0, 0, colorCanvas.width, colorCanvas.height);

                   // Scale up the color context
                   colorCtx.scale(hiResScale, hiResScale);
                   colorCtx.imageSmoothingEnabled = false;
                   colorCtx.webkitImageSmoothingEnabled = false;
                   colorCtx.mozImageSmoothingEnabled = false;
                   colorCtx.msImageSmoothingEnabled = false;

                   const colorImagePadding = Math.max(strokeWidth * 4, 40);
                   colorCtx.drawImage(obj.image, Math.floor(colorImagePadding), Math.floor(colorImagePadding), scaledWidth, scaledHeight);
                   colorCtx.globalCompositeOperation = 'source-in';
                   colorCtx.fillStyle = strokeColor;
                   colorCtx.fillRect(0, 0, colorCanvas.width, colorCanvas.height);

                   // Step 2: Draw stroke outline in all directions
                   for (const [dx, dy] of directions) {
                       strokeCtx.drawImage(colorCanvas,
                           Math.floor(centerX - (scaledWidth + colorPadding) / 2 + dx),
                           Math.floor(centerY - (scaledHeight + colorPadding) / 2 + dy),
                           scaledWidth + colorPadding,
                           scaledHeight + colorPadding);
                   }

                   // Step 3: KNOCKOUT - Cut out the interior using destination-out compositing
                   strokeCtx.globalCompositeOperation = 'destination-out';
                   strokeCtx.drawImage(obj.image,
                       Math.floor(centerX - scaledWidth / 2),
                       Math.floor(centerY - scaledHeight / 2),
                       scaledWidth, scaledHeight);

                   // Reset compositing operation
                   strokeCtx.globalCompositeOperation = 'source-over';

                   // Cache the result
                   obj.strokeCache = strokeCanvas;
                   obj.strokeCacheKey = strokeCacheKey;
               }

               // Draw the cached stroke (knockout outline) with high-resolution scaling
               targetCtx.save();
               targetCtx.shadowColor = 'transparent';
               targetCtx.shadowBlur = 0;
               targetCtx.shadowOffsetX = 0;
               targetCtx.shadowOffsetY = 0;

               // Enable high-quality scaling for the final render
               targetCtx.imageSmoothingEnabled = true;
               targetCtx.imageSmoothingQuality = 'high';

               // Calculate padding for final drawing (same as cache generation)
               const finalPadding = Math.max(strokeWidth * 8, 60);
               const finalWidth = (scaledWidth + finalPadding * 2);
               const finalHeight = (scaledHeight + finalPadding * 2);

               // Draw the knockout stroke outline
               targetCtx.drawImage(obj.strokeCache,
                   -finalWidth / 2,
                   -finalHeight / 2,
                   finalWidth,
                   finalHeight);
               targetCtx.restore();
           } else {
               // For regular images (JPG, PNG), use simple rectangle stroke
               targetCtx.strokeStyle = strokeColor;
               targetCtx.lineWidth = strokeWidth;
               targetCtx.shadowColor = 'transparent';
               targetCtx.shadowBlur = 0;
               targetCtx.shadowOffsetX = 0;
               targetCtx.shadowOffsetY = 0;
               targetCtx.strokeRect(-scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight);
           }

           // Restore stroke opacity context
           targetCtx.restore();
       }

       // Apply image opacity (separate from stroke opacity)
       const imageOpacity = (obj.opacity !== undefined) ? obj.opacity / 100 : 1;
       targetCtx.globalAlpha = imageOpacity;

       // Apply shadow if enabled
       if (obj.shadowMode === 'standard') {
           const shadowOpacity = (obj.shadowOpacity !== undefined) ? obj.shadowOpacity / 100 : 1;
           // Convert hex color to rgba with opacity
           const shadowColor = hexToRgba(obj.shadowColor || '#000000', shadowOpacity);
           targetCtx.shadowColor = shadowColor;
           targetCtx.shadowOffsetX = obj.shadowOffsetX || 5;
           targetCtx.shadowOffsetY = obj.shadowOffsetY || 5;
           targetCtx.shadowBlur = obj.shadowBlur || 10;
       }

       // Check if this is an SVG with gradient
       if (obj.fillType === 'gradient' && obj.gradient && obj.imageUrl && obj.imageUrl.toLowerCase().endsWith('.svg')) {
           console.log('🎨 Drawing SVG with gradient:', obj.gradient);

           // Create bounds for gradient
           const bounds = {
               x: -scaledWidth / 2,
               y: -scaledHeight / 2,
               width: scaledWidth,
               height: scaledHeight
           };

           // Create an offscreen canvas for the gradient effect
           const offscreenCanvas = document.createElement('canvas');
           offscreenCanvas.width = scaledWidth;
           offscreenCanvas.height = scaledHeight;
           const offscreenCtx = offscreenCanvas.getContext('2d');

           // Draw the gradient on the offscreen canvas
           const gradientBounds = {
               x: 0,
               y: 0,
               width: scaledWidth,
               height: scaledHeight
           };
           applySVGGradientFill(offscreenCtx, obj, gradientBounds);
           offscreenCtx.fillRect(0, 0, scaledWidth, scaledHeight);

           // Draw the original SVG as a mask
           offscreenCtx.globalCompositeOperation = 'destination-in';
           try {
               offscreenCtx.drawImage(obj.image, 0, 0, scaledWidth, scaledHeight);
           } catch (e) {
               console.error("Error drawing SVG mask:", e);
           }

           // Draw the final result to the main canvas
           try {
               targetCtx.drawImage(offscreenCanvas, bounds.x, bounds.y);
           } catch (e) {
               console.error("Error drawing gradient SVG:", e);
               // Fallback to normal image
               targetCtx.drawImage(obj.image, bounds.x, bounds.y, scaledWidth, scaledHeight);
           }
       } else {
           // Normal image drawing
           try {
               targetCtx.drawImage( obj.image, -scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight ); // Use targetCtx
           } catch (e) {
               console.error("Error drawing image:", e, obj);
               targetCtx.fillStyle = 'red'; // Use targetCtx
               targetCtx.fillRect(-scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight); // Use targetCtx
               targetCtx.fillStyle = 'white'; // Use targetCtx
               targetCtx.fillText('ERR', 0, 0); // Use targetCtx
           }
       }



       targetCtx.restore(); // Use targetCtx
   }
   function drawSelectionBox(obj) { if (!obj || !obj.isSelected) return; const bounds = calculateObjectBounds(obj); if (bounds.width === 0 || bounds.height === 0) return; const rotatedBounds = getRotatedBoundingBox(bounds, obj.rotation); const effectivePadding = selectionBoxPadding / scale; const effectiveLineWidth = 1 / scale; const paddedX = rotatedBounds.x - effectivePadding; const paddedY = rotatedBounds.y - effectivePadding; const paddedWidth = rotatedBounds.width + effectivePadding * 2; const paddedHeight = rotatedBounds.height + effectivePadding * 2; ctx.save(); ctx.strokeStyle = 'rgba(0, 100, 255, 0.8)'; ctx.lineWidth = effectiveLineWidth; ctx.setLineDash([4 / scale, 4 / scale]); ctx.strokeRect(paddedX, paddedY, paddedWidth, paddedHeight); ctx.restore(); }

   // --- Main Update/Render Function ---
   function update() {
       ctx.save();
       ctx.setTransform(1, 0, 0, 1, 0, 0);
       // Clear with background color first
       ctx.fillStyle = canvasBackgroundColor;
       ctx.fillRect(0, 0, canvas.clientWidth, canvas.clientHeight);
       // ctx.clearRect(0, 0, canvas.clientWidth, canvas.clientHeight); // Original clear removed
       ctx.restore();

       ctx.save();
       ctx.translate(offsetX, offsetY);
       ctx.scale(scale, scale);

       // Draw Artboard if exists (on main canvas only)
       if (artboard && !artboard.isSelected && ctx === canvas.getContext('2d')) { // Check if drawing on main canvas
           ctx.save();
           ctx.strokeStyle = '#bdbdbd';
           ctx.lineWidth = 2 / scale;
           ctx.setLineDash([6 / scale, 4 / scale]);
           ctx.strokeRect(artboard.x, artboard.y, artboard.width, artboard.height);

           ctx.setLineDash([]);
           ctx.font = `${16 / scale}px sans-serif`;
           ctx.fillStyle = '#bdbdbd';
           ctx.fillText('Artboard', artboard.x + 5 / scale, artboard.y + 20 / scale);
           ctx.restore();
       }

       // Draw all objects - Pass the main context 'ctx'
       canvasObjects.forEach((obj) => {
           if (obj.type === 'text') { drawTextObject(obj, ctx); } // Pass ctx
           else if (obj.type === 'image') { drawImageObject(obj, ctx); } // Pass ctx
       });

       // Draw selection box (on main canvas only)
       if (selectedObjectIndex !== -1 && ctx === canvas.getContext('2d')) { // Check if drawing on main canvas
           drawSelectionBox(canvasObjects[selectedObjectIndex]);
       }

       // Draw Artboard on top if selected (on main canvas only)
       if (artboard && artboard.isSelected && ctx === canvas.getContext('2d')) { // Check if drawing on main canvas
           ctx.save();

           // Use different styling based on edit mode
           if (isArtboardEditMode) {
               // More prominent styling in edit mode
               ctx.strokeStyle = '#4ade80'; // Green color to match confirm button
               ctx.lineWidth = 3 / scale;
               ctx.setLineDash([8 / scale, 4 / scale]);
           } else {
               // Normal styling when not in edit mode
               ctx.strokeStyle = '#bdbdbd';
               ctx.lineWidth = 2 / scale;
               ctx.setLineDash([6 / scale, 4 / scale]);
           }

           ctx.strokeRect(artboard.x, artboard.y, artboard.width, artboard.height);

           ctx.setLineDash([]);
           ctx.font = `${16 / scale}px sans-serif`;
           ctx.fillStyle = isArtboardEditMode ? '#4ade80' : '#bdbdbd';
           ctx.fillText('Artboard', artboard.x + 5 / scale, artboard.y + 20 / scale);

           // Only draw resize handles when in artboard edit mode
           if (isArtboardEditMode) {
               // Draw resize handles
               const size = 8 / scale;
               const half = size / 2;
               const corners = [
                   [artboard.x, artboard.y],
                   [artboard.x + artboard.width, artboard.y],
                   [artboard.x, artboard.y + artboard.height],
                   [artboard.x + artboard.width, artboard.y + artboard.height]
               ];
               ctx.fillStyle = '#4ade80'; // Green color for handles
               for (const [x, y] of corners) {
                   ctx.fillRect(x - half, y - half, size, size);
               }
           }
           ctx.restore();
       }

       // No need to redraw objects here again

       // Draw selection box again if needed (on main canvas only)
       if (selectedObjectIndex !== -1 && ctx === canvas.getContext('2d')) { // Check if drawing on main canvas
           drawSelectionBox(canvasObjects[selectedObjectIndex]);

           // Draw mesh grid if needed and if the function exists
           if (canvasObjects[selectedObjectIndex].type === 'text' &&
               canvasObjects[selectedObjectIndex].effectMode === 'mesh') {
               // Check if the mesh handler is active for this object
               if (typeof activeMeshWarpHandler !== 'undefined' &&
                   activeMeshWarpHandler &&
                   activeMeshWarpHandler.selectedTextObject === canvasObjects[selectedObjectIndex]) {
                   // Use the handler's drawMeshGrid method directly
                   activeMeshWarpHandler.drawMeshGrid(ctx);
               } else {
                   console.warn("Mesh effect selected, but no active handler found for object:", canvasObjects[selectedObjectIndex].id);
               }
           }
       }

       ctx.restore();
       updateZoomDisplay();
   }
   function updateZoomDisplay() { zoomLevelSpan.textContent = `${Math.round(scale * 100)}%`; }

   // --- Event Handlers & Listeners ---
   function handleAddTextObject() {
    const text = iText.value.trim();
    if (!text) {
        alert("Please enter text before adding.");
        return;
    }

    const viewCenterX = canvas.clientWidth / 2;
    const viewCenterY = canvas.clientHeight / 2;
    const worldCenter = canvasToWorld(viewCenterX, viewCenterY);

    // Create new text object with default settings
    const newObjOptions = {
        text: text,
        x: worldCenter.x + (Math.random() * 40 - 20) / scale,
        y: worldCenter.y + (Math.random() * 40 - 20) / scale,
        color: iTextColor.value,
        fontFamily: iFontFamily.value,
        fontSize: parseInt(iFontSize.value, 10),
        bold: iBold.checked,
        italic: iItalic.checked,
        rotation: parseInt(iTextRotation.value, 10),
        // Always set effectMode to 'normal' for new text objects
        effectMode: 'normal',
        // Keep other properties from UI for convenience
        skewX: parseInt(skewSlider.value, 10),
        skewY: parseInt(skewYSlider.value, 10),
        warpCurve: parseInt(iCurve.value, 10),
        warpOffset: parseInt(iOffset.value, 10),
        warpHeight: parseInt(iHeight.value, 10),
        warpBottom: parseInt(iBottom.value, 10),
        warpTriangle: iTriangle.checked,
        warpShiftCenter: parseInt(iShiftCenter.value, 10),
        circleDiameter: parseInt(iDiameter.value, 10),
        circleKerning: parseInt(iKerning.value, 10),
        circleFlip: iFlip.checked,
        curveAmount: parseInt(iCurveAmount.value, 10),
        curveKerning: parseInt(iCurveKerning.value, 10),
        curveFlip: iCurveFlip.checked,
        // Grid Distort parameters (will be used only when user explicitly selects grid-distort)
        gridDistortCols: iGridDistortCols ? parseInt(iGridDistortCols.value, 10) : 2,
        gridDistortRows: iGridDistortRows ? parseInt(iGridDistortRows.value, 10) : 1,
        gridDistortPadding: iGridDistortPadding ? parseInt(iGridDistortPadding.value, 10) : 120,
        gridDistortIntensity: iGridDistortIntensity ? parseInt(iGridDistortIntensity.value, 10) : 100,
        gridDistortVerticalOnly: true, // Set vertical only as default
        // Reset shadow and decoration modes to default for new text
        shadowMode: 'noShadow',
        shadowColor: shadowColorPicker.value,
        shadowOffsetX: parseInt(shadowOffsetXSlider.value, 10),
        shadowOffsetY: parseInt(shadowOffsetYSlider.value, 10),
        shadowBlur: parseInt(shadowBlurSlider.value, 10),
        blockShadowColor: blockShadowColorPicker.value,
        blockShadowOpacity: parseInt(blockShadowOpacitySlider.value, 10),
        blockShadowOffset: parseInt(blockShadowOffsetSlider.value, 10),
        blockShadowAngle: parseInt(blockShadowAngleSlider.value, 10),
        blockShadowBlur: parseInt(blockShadowBlurSlider.value, 10),
        blockShadowPerspective: false,
        blockShadowPerspectiveIntensity: 50,
        lineShadowColor: lineShadowColorPicker.value,
        lineShadowDist: parseInt(lineShadowDistanceSlider.value, 10),
        lineShadowAngle: parseInt(lineShadowAngleSlider.value, 10),
        lineShadowThickness: parseInt(lineShadowThicknessSlider.value, 10),
        d3dPrimaryColor: detailed3DPrimaryColorPicker.value,
        d3dPrimaryOpacity: parseInt(detailed3DPrimaryOpacitySlider.value, 10),
        d3dOffset: parseInt(detailed3DOffsetSlider.value, 10),
        d3dAngle: parseInt(detailed3DAngleSlider.value, 10),
        d3dBlur: parseInt(detailed3DBlurSlider.value, 10),
        d3dSecondaryColor: detailed3DSecondaryColorPicker.value,
        d3dSecondaryOpacity: parseInt(detailed3DSecondaryOpacitySlider.value, 10),
        d3dSecondaryWidth: parseInt(detailed3DSecondaryWidthSlider.value, 10),
        d3dSecondaryOffsetX: parseInt(detailed3DSecondaryOffsetXSlider.value, 10),
        d3dSecondaryOffsetY: parseInt(detailed3DSecondaryOffsetYSlider.value, 10),
        strokeMode: 'noStroke',
        strokeWidth: parseInt(strokeWidthSlider.value, 10),
        strokeColor: strokeColorPicker.value,
        decorationMode: 'noDecoration',
        hLineWeight: parseInt(hWeight.value, 10),
        hLineDist: parseInt(hDistance.value, 10),
        hLineColor: hColor.value,
        ccDist: parseInt(ccDistance.value, 10),
        ccColor: ccColor.value,
        ccFillDir: ccFillTop.checked ? 'top' : 'bottom',
        oLineWeight: parseInt(oWeight.value, 10),
        oLineDist: parseInt(oDistance.value, 10),
        oLineColor: oColor.value,
        flcDist: parseInt(flcDistance.value, 10),
        flcColor: flcColor.value,
        flcWeight: parseInt(flcMaxWeight.value, 10),
        flcSpacing: parseInt(flcSpacing.value, 10),
        flcDir: flcFillTop.checked ? 'top' : 'bottom',
    };

    const newObj = createTextObject(newObjOptions);

    // Ensure gridDistort is initialized with showGrid set to false
    if (newObj.gridDistort) {
        newObj.gridDistort.showGrid = false;
    }

    if (selectedObjectIndex !== -1) {
        canvasObjects[selectedObjectIndex].isSelected = false;
    }

    canvasObjects.push(newObj);
    selectedObjectIndex = canvasObjects.length - 1;
    newObj.isSelected = true;

    // Update UI to reflect the new object's properties
    updateUIFromSelectedObject();

    // Make sure the effect mode dropdown is set to 'normal'
    if (effectModeSelect) {
        effectModeSelect.value = 'normal';
    }

    update();

    // Save state for undo/redo
    saveState('Add Text');
}
   // **** ADDED SEMICOLON ****
   function handleAddImage(file) { if (!file || !file.type.startsWith('image/')) { alert('Please select a valid image file.'); return; } const reader = new FileReader(); reader.onload = function(event) { const img = new Image(); img.onload = function() { const viewCenterX = canvas.clientWidth / 2; const viewCenterY = canvas.clientHeight / 2; const worldCenter = canvasToWorld(viewCenterX, viewCenterY); const newObj = createImageObject(img, { x: worldCenter.x + (Math.random() * 40 - 20) / scale, y: worldCenter.y + (Math.random() * 40 - 20) / scale, imageUrl: event.target.result /* Store data URL initially? Or null? Let's use data URL */ }); if (selectedObjectIndex !== -1) { canvasObjects[selectedObjectIndex].isSelected = false; } canvasObjects.push(newObj); selectedObjectIndex = canvasObjects.length - 1; newObj.isSelected = true; updateUIFromSelectedObject(); update(); saveState('Add Image'); }; img.onerror = function() { alert('Error loading image.'); }; img.src = event.target.result; }; reader.readAsDataURL(file); }
   function handleDeleteObject() { if (selectedObjectIndex !== -1) { const deletedObject = canvasObjects[selectedObjectIndex]; canvasObjects.splice(selectedObjectIndex, 1); selectedObjectIndex = -1; updateUIFromSelectedObject(); update(); saveState(`Delete ${deletedObject.type === 'text' ? 'Text' : 'Image'}`); } }
   function handleMouseDown(e) {
       const coords = getCanvasCoordinates(e);

       // Middle mouse button for panning
       if (e.button === 1) {
           isPanning = true;
           isDraggingObject = false;
           isDraggingGridPoint = false;
           panStartX = coords.x;
           panStartY = coords.y;
           canvasArea.classList.add('panning');
           e.preventDefault();
           return;
       }

       // If in artboard edit mode, prevent object selection
       if (isArtboardEditMode && artboard && artboard.isSelected) {
           // Only allow artboard corner interactions which are handled separately
           return;
       }

       // Left mouse button
       if (e.button === 0) {
           const worldCoords = canvasToWorld(coords.x, coords.y);

           // --- Check for Grid Point Hit FIRST ---
           if (selectedObjectIndex !== -1 &&
               canvasObjects[selectedObjectIndex].type === 'text' &&
               canvasObjects[selectedObjectIndex].effectMode === 'grid-distort' &&
               canvasObjects[selectedObjectIndex].gridDistort &&
               canvasObjects[selectedObjectIndex].gridDistort.showGrid) {

               // Log for debugging
               console.log('Checking for grid point hit');

               const result = findGridPointAt(
                   canvasObjects[selectedObjectIndex],
                   worldCoords.x,
                   worldCoords.y
               );

               if (result.hit) {
                   console.log('Grid point hit:', result.row, result.col);
                   isDraggingGridPoint = true;
                   isDraggingObject = false;
                   draggedPointRow = result.row;
                   draggedPointCol = result.col;
                   draggedObject = canvasObjects[selectedObjectIndex];
                   canvas.classList.add('dragging-grid-point');
                   return; // Stop further processing
               }
           }

           // --- Check for Mesh Point Hit NEXT ---
           // Check if mesh mode is active and if the click hit a control point
           if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler &&
               selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex] === activeMeshWarpHandler.selectedTextObject) {
               // activeMeshWarpHandler.handleMouseDown(e) will set its internal isDragging state
               // We don't need to call it directly here if it's already attached to the canvas mousedown
               // Instead, we check if the handler *is currently* dragging after its own listener ran.
               // Note: This relies on the mesh handler's listener running *before* this one.
               // If event listener order is not guaranteed, call the handler's check method directly:
               const hitPointIndex = activeMeshWarpHandler.findPointAt(worldCoords.x, worldCoords.y);
               if (hitPointIndex !== -1) {
                    // If a mesh point was hit, let the mesh handler manage the drag.
                    // We might need to explicitly call its mousedown logic if stopPropagation isn't enough
                    // activeMeshWarpHandler.handleMouseDown(e); // Call if needed
                    console.log("Main mouse down: Mesh point hit, preventing object drag.");
                    isDraggingObject = false; // Ensure object dragging is off
                    return; // Stop further processing in this handler
               }
           }
           // --- End Mesh Point Check ---


           // --- Object Hit Testing (if no mesh point was hit) ---
           let hitIndex = -1;
           for (let i = canvasObjects.length - 1; i >= 0; i--) {
               const obj = canvasObjects[i];
               const bounds = calculateObjectBounds(obj);
               const dx = worldCoords.x - obj.x;
               const dy = worldCoords.y - obj.y;
               const angleRad = -obj.rotation * Math.PI / 180;
               const cos = Math.cos(angleRad);
               const sin = Math.sin(angleRad);
               const localClickX = dx * cos - dy * sin;
               const localClickY = dx * sin + dy * cos;
               const hit = localClickX >= -bounds.width / 2 && localClickX <= bounds.width / 2 &&
                           localClickY >= -bounds.height / 2 && localClickY <= bounds.height / 2;
               if (hit) {
                   hitIndex = i;
                   break;
               }
           }

           if (hitIndex !== -1) {
               // Clicked on an object
               isDraggingObject = true;
               isPanning = false;
               if (selectedObjectIndex !== hitIndex) {
                   if (selectedObjectIndex !== -1) {
                       canvasObjects[selectedObjectIndex].isSelected = false;
                   }
                   selectedObjectIndex = hitIndex;
                   canvasObjects[selectedObjectIndex].isSelected = true;

                   // Activate mesh warp handler if the selected object has mesh effect
                   const selectedObject = canvasObjects[selectedObjectIndex];
                   console.log('[Selection] Selected object:', selectedObject.text || selectedObject.type, 'effectMode:', selectedObject.effectMode, 'hasMeshHandler:', !!selectedObject._meshWarpHandler);
                   if (selectedObject.type === 'text' && selectedObject.effectMode === 'mesh' && selectedObject._meshWarpHandler) {
                       console.log('[Selection] Activating mesh warp handler for text object:', selectedObject.text);
                       console.log('[Selection] Handler control points:', selectedObject._meshWarpHandler.controlPoints.length);
                       activeMeshWarpHandler = selectedObject._meshWarpHandler;
                       activeMeshWarpHandler.selectedTextObject = selectedObject;
                       console.log('[Selection] Active mesh warp handler set:', !!activeMeshWarpHandler);
                   } else if (selectedObject.type !== 'text') {
                       // When selecting non-text objects (shapes/images), don't deactivate mesh handlers
                       // This allows mesh warp text to continue rendering properly
                       console.log('[Selection] Selected non-text object, keeping mesh handlers active for text objects');
                       activeMeshWarpHandler = null; // Clear active handler for interaction, but keep text handlers intact
                   } else if (selectedObject.type === 'text' && selectedObject.effectMode !== 'mesh') {
                       // Only deactivate if switching to a text object without mesh effect
                       console.log('[Selection] Switching to non-mesh text object, clearing active mesh handler');
                       activeMeshWarpHandler = null;
                   }
               }
               dragStartX = coords.x;
               dragStartY = coords.y;
               dragInitialObjectX = canvasObjects[selectedObjectIndex].x;
               dragInitialObjectY = canvasObjects[selectedObjectIndex].y;

               // --- BEGIN FIX V2: Store initial mesh points on drag start ---
               if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler &&
                   activeMeshWarpHandler.selectedTextObject === canvasObjects[selectedObjectIndex] &&
                   activeMeshWarpHandler.controlPoints) {
                   // Store a deep copy of the control points at the start of the drag
                   dragInitialControlPoints = activeMeshWarpHandler.controlPoints.map(p => ({ ...p }));
                   console.log("Stored initial mesh points for drag");
               } else {
                   dragInitialControlPoints = null; // Ensure it's null if not dragging a mesh object
               }
               // --- END FIX V2 ---

               canvas.classList.add('dragging');
               updateUIFromSelectedObject();
               update();
           } else {
               // Clicked on empty space
               if (selectedObjectIndex !== -1) {
                   canvasObjects[selectedObjectIndex].isSelected = false;
                   selectedObjectIndex = -1;
                   updateUIFromSelectedObject();
                   update();
               }
               dragInitialControlPoints = null; // Clear stored points if clicking empty space
           }
       }
   }
   function handleMouseMove(e) {
        // --- Check Grid Point Drag FIRST ---
        if (isDraggingGridPoint && draggedObject && draggedPointRow !== -1 && draggedPointCol !== -1) {
            console.log('Dragging grid point:', draggedPointRow, draggedPointCol);

            const coords = getCanvasCoordinates(e);
            const worldCoords = canvasToWorld(coords.x, coords.y);

            // Convert world coordinates to object-local coordinates
            const dx = worldCoords.x - draggedObject.x;
            const dy = worldCoords.y - draggedObject.y;
            const angleRad = -draggedObject.rotation * Math.PI / 180;
            const cos = Math.cos(angleRad);
            const sin = Math.sin(angleRad);
            const localX = dx * cos - dy * sin;
            const localY = dx * sin + dy * cos;

            console.log('Moving point to local coordinates:', localX, localY);

            // Update the control point position
            if (draggedObject.gridDistort &&
                draggedObject.gridDistort.controlPoints &&
                draggedObject.gridDistort.controlPoints[draggedPointRow] &&
                draggedObject.gridDistort.controlPoints[draggedPointRow][draggedPointCol]) {

                // Store the previous position for logging
                const prevX = draggedObject.gridDistort.controlPoints[draggedPointRow][draggedPointCol].x;
                const prevY = draggedObject.gridDistort.controlPoints[draggedPointRow][draggedPointCol].y;

                // Update the position
                draggedObject.gridDistort.controlPoints[draggedPointRow][draggedPointCol].x = localX;
                draggedObject.gridDistort.controlPoints[draggedPointRow][draggedPointCol].y = localY;

                console.log('Updated point position:',
                    `(${prevX.toFixed(2)}, ${prevY.toFixed(2)}) -> (${localX.toFixed(2)}, ${localY.toFixed(2)})`);

                // Store relative positions for preserving distortion when text changes
                storeRelativeControlPoints(draggedObject);

                update();
            } else {
                console.warn('Could not update control point - invalid grid structure');
            }

            return;
        }

        // --- Check Mesh Drag NEXT ---
        if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler && activeMeshWarpHandler.isDragging) {
            // If the mesh handler is dragging, let it handle the move and prevent object drag
            // activeMeshWarpHandler.handleMouseMove(e); // Already handled by its own listener
            isDraggingObject = false; // Ensure object drag state is off
            return;
        }
        // --- End Mesh Drag Check ---

        // Original Mouse Move Logic (Panning or Object Drag)
        if (!isPanning && !isDraggingObject) return;
        const coords = getCanvasCoordinates(e);
        if (isPanning) {
            const dx = coords.x - panStartX;
            const dy = coords.y - panStartY;
            offsetX += dx;
            offsetY += dy;
            panStartX = coords.x;
            panStartY = coords.y;
            update();
        } else if (isDraggingObject && selectedObjectIndex !== -1) {
            const dragDeltaX = coords.x - dragStartX;
            const dragDeltaY = coords.y - dragStartY;
            // Calculate TOTAL displacement from the start of the drag
            const totalWorldDeltaX = (coords.x - dragStartX) / scale;
            const totalWorldDeltaY = (coords.y - dragStartY) / scale;

            const draggedObject = canvasObjects[selectedObjectIndex];
            // Update object's position based on total delta
            draggedObject.x = dragInitialObjectX + totalWorldDeltaX;
            draggedObject.y = dragInitialObjectY + totalWorldDeltaY;

            // --- BEGIN FIX V2: Update mesh control points based on total delta ---
            if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler &&
                activeMeshWarpHandler.selectedTextObject === draggedObject &&
                dragInitialControlPoints && // Check if we stored initial points
                activeMeshWarpHandler.controlPoints &&
                dragInitialControlPoints.length === activeMeshWarpHandler.controlPoints.length) {

                // Set current points based on their initial drag position + total delta
                for (let i = 0; i < activeMeshWarpHandler.controlPoints.length; i++) {
                    if (dragInitialControlPoints[i]) { // Check if specific initial point exists
                       activeMeshWarpHandler.controlPoints[i].x = dragInitialControlPoints[i].x + totalWorldDeltaX;
                       activeMeshWarpHandler.controlPoints[i].y = dragInitialControlPoints[i].y + totalWorldDeltaY;
                    }
                }
                // console.log("Updated mesh points based on total drag delta"); // Optional log
            }
            // --- END FIX V2 ---

            update();
        }
    }
   function handleMouseUp(e) {
       // --- Check Grid Point Drag FIRST ---
       if (isDraggingGridPoint) {
           isDraggingGridPoint = false;
           draggedPointRow = -1;
           draggedPointCol = -1;
           draggedObject = null;
           canvas.classList.remove('dragging-grid-point');
           update();
           return;
       }

       // --- Check Mesh Drag NEXT ---
       if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler && activeMeshWarpHandler.isDragging) {
           // Let the mesh handler finish its drag
           // activeMeshWarpHandler.handleMouseUp(e); // Already handled by its own listener
           // No need to return early, just ensure object drag state is correct below
       }
       // --- End Mesh Drag Check ---

       if (isPanning) {
           isPanning = false;
           canvasArea.classList.remove('panning');
       }
       if (isDraggingObject) {
           isDraggingObject = false;
           canvas.classList.remove('dragging');

           // Save state for undo/redo when object movement ends
           if (selectedObjectIndex !== -1) {
               const selectedObject = canvasObjects[selectedObjectIndex];
               saveState(`Move ${selectedObject.type === 'text' ? 'Text' : 'Image'}`);
           }
       }
       // Ensure mesh dragging state is also reset if mouseup happens here
       if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler) {
             activeMeshWarpHandler.isDragging = false;
             activeMeshWarpHandler.draggingPointIndex = -1;
        }
        // --- BEGIN FIX V2: Clear stored initial points on mouse up ---
        dragInitialControlPoints = null;
        // --- END FIX V2 ---
    }
   function handleMouseLeave(e) {
        // Call mouse up to stop any dragging if mouse leaves canvas
        handleMouseUp(e);
   }
   function handleWheel(e) { e.preventDefault(); const coords = getCanvasCoordinates(e); const worldPosBeforeZoom = canvasToWorld(coords.x, coords.y); const delta = -e.deltaY * ZOOM_SENSITIVITY; const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale * Math.exp(delta))); offsetX = coords.x - worldPosBeforeZoom.x * newScale; offsetY = coords.y - worldPosBeforeZoom.y * newScale; scale = newScale; update(); }
   function zoom(factor, centerOnCanvas = true) { let centerX, centerY; if(centerOnCanvas){ centerX = canvas.clientWidth / 2; centerY = canvas.clientHeight / 2; } else { centerX = panStartX ?? canvas.clientWidth / 2; centerY = panStartY ?? canvas.clientHeight / 2; } const worldPosBeforeZoom = canvasToWorld(centerX, centerY); const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale * factor)); offsetX = centerX - worldPosBeforeZoom.x * newScale; offsetY = centerY - worldPosBeforeZoom.y * newScale; scale = newScale; update(); }

   // Attach Input Listeners
   iText.oninput = () => { if (selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex].type === 'text') { updateSelectedObjectFromUI('text', iText.value); } }; addEditTextBtn.onclick = () => { if (selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex].type === 'text') { iText.focus(); } else { handleAddTextObject(); } }; deleteTextBtn.onclick = handleDeleteObject; iTextColor.oninput = (e) => updateSelectedObjectFromUI('color', e.target.value); iFontFamily.onchange = (e) => updateSelectedObjectFromUI('fontFamily', e.target.value); iBold.onchange = (e) => updateSelectedObjectFromUI('bold', e.target.checked); iItalic.onchange = (e) => updateSelectedObjectFromUI('italic', e.target.checked); iFontSize.oninput = (e) => updateSelectedObjectFromUI('fontSize', parseInt(e.target.value, 10));

   // Letter spacing and opacity controls
   const iLetterSpacing = document.getElementById('iLetterSpacing');
   const iOpacity = document.getElementById('iOpacity');

   if (iLetterSpacing) {
       iLetterSpacing.oninput = (e) => updateSelectedObjectFromUI('letterSpacing', parseInt(e.target.value, 10));
   }

   if (iOpacity) {
       iOpacity.oninput = (e) => updateSelectedObjectFromUI('opacity', parseInt(e.target.value, 10));
   }

   // Main rotation slider
   iTextRotation.oninput = (e) => {
       const rotationValue = parseInt(e.target.value, 10);
       updateSelectedObjectFromUI('rotation', rotationValue);
       // Sync with circular rotation slider if it exists
       if (document.getElementById('iCircleRotation')) {
           document.getElementById('iCircleRotation').value = rotationValue;
           document.getElementById('vCircleRotation').textContent = rotationValue + '°';
       }
   };

   effectModeSelect.onchange = (e) => updateSelectedObjectFromUI('effectMode', e.target.value); skewSlider.oninput = (e) => updateSelectedObjectFromUI('skewX', parseInt(e.target.value, 10)); skewYSlider.oninput = (e) => updateSelectedObjectFromUI('skewY', parseInt(e.target.value, 10)); iCurve.oninput = (e) => updateSelectedObjectFromUI('warpCurve', parseInt(e.target.value, 10)); iOffset.oninput = (e) => updateSelectedObjectFromUI('warpOffset', parseInt(e.target.value, 10)); iHeight.oninput = (e) => updateSelectedObjectFromUI('warpHeight', parseInt(e.target.value, 10)); iBottom.oninput = (e) => updateSelectedObjectFromUI('warpBottom', parseInt(e.target.value, 10)); iTriangle.onchange = (e) => updateSelectedObjectFromUI('warpTriangle', e.target.checked); iShiftCenter.oninput = (e) => updateSelectedObjectFromUI('warpShiftCenter', parseInt(e.target.value, 10)); iDiameter.oninput = (e) => updateSelectedObjectFromUI('circleDiameter', parseInt(e.target.value, 10)); iKerning.oninput = (e) => updateSelectedObjectFromUI('circleKerning', parseInt(e.target.value, 10));

   // Circular rotation slider
   const iCircleRotation = document.getElementById('iCircleRotation');
   if (iCircleRotation) {
       iCircleRotation.oninput = (e) => {
           const rotationValue = parseInt(e.target.value, 10);
           updateSelectedObjectFromUI('rotation', rotationValue);
           // Sync with main rotation slider
           iTextRotation.value = rotationValue;
           vTextRotation.textContent = rotationValue + '°';
           document.getElementById('vCircleRotation').textContent = rotationValue + '°';
       };
   }

   iFlip.onchange = (e) => updateSelectedObjectFromUI('circleFlip', e.target.checked); iCurveAmount.oninput = (e) => updateSelectedObjectFromUI('curveAmount', parseInt(e.target.value, 10)); iCurveKerning.oninput = (e) => updateSelectedObjectFromUI('curveKerning', parseInt(e.target.value, 10)); iCurveFlip.onchange = (e) => updateSelectedObjectFromUI('curveFlip', e.target.checked);

   // Perspective Shadow Outline controls
   const perspectiveShadowOutlineColorPicker = document.getElementById('perspectiveShadowOutlineColor');
   const perspectiveShadowOutlineOpacitySlider = document.getElementById('perspectiveShadowOutlineOpacity');
   const perspectiveShadowOutlineWidthSlider = document.getElementById('perspectiveShadowOutlineWidth');
   const perspectiveShadowOutlineOffsetXSlider = document.getElementById('perspectiveShadowOutlineOffsetX');
   const perspectiveShadowOutlineOffsetYSlider = document.getElementById('perspectiveShadowOutlineOffsetY');

   if (perspectiveShadowOutlineColorPicker) perspectiveShadowOutlineColorPicker.oninput = (e) => updateSelectedObjectFromUI('perspectiveShadowOutlineColor', e.target.value);
   if (perspectiveShadowOutlineOpacitySlider) perspectiveShadowOutlineOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('perspectiveShadowOutlineOpacity', parseInt(e.target.value, 10));
   if (perspectiveShadowOutlineWidthSlider) perspectiveShadowOutlineWidthSlider.oninput = (e) => updateSelectedObjectFromUI('perspectiveShadowOutlineWidth', parseInt(e.target.value, 10));
   if (perspectiveShadowOutlineOffsetXSlider) perspectiveShadowOutlineOffsetXSlider.oninput = (e) => updateSelectedObjectFromUI('perspectiveShadowOutlineOffsetX', parseInt(e.target.value, 10));
   if (perspectiveShadowOutlineOffsetYSlider) perspectiveShadowOutlineOffsetYSlider.oninput = (e) => updateSelectedObjectFromUI('perspectiveShadowOutlineOffsetY', parseInt(e.target.value, 10));

   // Grid Distort controls
   const iGridDistortCols = document.getElementById('iGridDistortCols');
   const iGridDistortRows = document.getElementById('iGridDistortRows');
   const iGridDistortPadding = document.getElementById('iGridDistortPadding');
   const iGridDistortIntensity = document.getElementById('iGridDistortIntensity');
   const gridDistortDirectionBoth = document.getElementById('gridDistortDirectionBoth');
   const gridDistortDirectionVertical = document.getElementById('gridDistortDirectionVertical');
   const resetGridDistortBtn = document.getElementById('resetGridDistortBtn');
   const toggleGridDistortBtn = document.getElementById('toggleGridDistortBtn');

   if (iGridDistortCols) iGridDistortCols.oninput = (e) => updateSelectedObjectFromUI('gridDistortCols', parseInt(e.target.value, 10));
   if (iGridDistortRows) iGridDistortRows.oninput = (e) => updateSelectedObjectFromUI('gridDistortRows', parseInt(e.target.value, 10));
   if (iGridDistortPadding) iGridDistortPadding.oninput = (e) => updateSelectedObjectFromUI('gridDistortPadding', parseInt(e.target.value, 10));
   if (iGridDistortIntensity) iGridDistortIntensity.oninput = (e) => updateSelectedObjectFromUI('gridDistortIntensity', parseInt(e.target.value, 10));
   if (gridDistortDirectionBoth) gridDistortDirectionBoth.onchange = (e) => {
       if (e.target.checked) {
           updateSelectedObjectFromUI('gridDistortVerticalOnly', false);
       }
   };
   if (gridDistortDirectionVertical) gridDistortDirectionVertical.onchange = (e) => {
       if (e.target.checked) {
           updateSelectedObjectFromUI('gridDistortVerticalOnly', true);
       }
   };

   if (resetGridDistortBtn) {
       resetGridDistortBtn.onclick = () => {
           if (selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex].type === 'text') {
               const selectedObject = canvasObjects[selectedObjectIndex];
               if (selectedObject.gridDistort) {
                   // Reset grid points to their initial positions
                   initializeGridPoints(selectedObject);
                   update();
               }
           }
       };
   }

   if (toggleGridDistortBtn) {
       toggleGridDistortBtn.onclick = () => {
           if (selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex].type === 'text') {
               const selectedObject = canvasObjects[selectedObjectIndex];

               // Initialize gridDistort if it doesn't exist
               if (!selectedObject.gridDistort) {
                   console.log('Creating gridDistort object for text:', selectedObject.text);
                   selectedObject.gridDistort = {
                       gridCols: selectedObject.gridDistortCols || 3,
                       gridRows: selectedObject.gridDistortRows || 2,
                       gridPadding: selectedObject.gridDistortPadding || 120,
                       intensity: (selectedObject.gridDistortIntensity || 100) / 100,
                       controlPoints: [],
                       showGrid: true,
                       lastFontSize: selectedObject.fontSize,
                       lastText: selectedObject.text,
                       relativeControlPoints: []
                   };

                   // Initialize grid points
                   initializeGridPoints(selectedObject);
               }

               // Toggle grid visibility
               selectedObject.gridDistort.showGrid = !selectedObject.gridDistort.showGrid;
               console.log('Grid visibility toggled:', selectedObject.gridDistort.showGrid);

               // Update button text to reflect current state
               toggleGridDistortBtn.textContent = selectedObject.gridDistort.showGrid ? 'Hide Grid' : 'Show Grid';

               // Show a tooltip to inform the user that the grid is only visible when the text is selected
               if (selectedObject.gridDistort.showGrid) {
                   console.log('Grid will only be visible when this text object is selected');
                   // If there's a toast function available, use it
                   if (typeof window.showToast === 'function') {
                       window.showToast('Grid will only be visible when this text is selected', 'info');
                   }
               }

               // Force effect mode to grid-distort if it's not already
               if (selectedObject.effectMode !== 'grid-distort') {
                   selectedObject.effectMode = 'grid-distort';
                   if (effectModeSelect) {
                       effectModeSelect.value = 'grid-distort';
                   }
                   updateBodyClass(selectedObject);
               }

               // Make sure grid points are initialized if they don't exist
               if (!selectedObject.gridDistort.controlPoints ||
                   selectedObject.gridDistort.controlPoints.length === 0) {
                   initializeGridPoints(selectedObject);
               }

               update();
           }
       };

       // Initialize button text based on current state
       if (selectedObjectIndex !== -1 &&
           canvasObjects[selectedObjectIndex].type === 'text') {

           // Make sure gridDistort exists
           if (!canvasObjects[selectedObjectIndex].gridDistort) {
               canvasObjects[selectedObjectIndex].gridDistort = {
                   gridCols: canvasObjects[selectedObjectIndex].gridDistortCols || 3,
                   gridRows: canvasObjects[selectedObjectIndex].gridDistortRows || 2,
                   gridPadding: canvasObjects[selectedObjectIndex].gridDistortPadding || 120,
                   intensity: (canvasObjects[selectedObjectIndex].gridDistortIntensity || 100) / 100,
                   controlPoints: [],
                   showGrid: true,
                   lastFontSize: canvasObjects[selectedObjectIndex].fontSize,
                   lastText: canvasObjects[selectedObjectIndex].text,
                   relativeControlPoints: []
               };
           }

           // Set button text based on grid visibility
           toggleGridDistortBtn.textContent = canvasObjects[selectedObjectIndex].gridDistort.showGrid ?
               'Hide Grid' : 'Show Grid';
       } else {
           // Default text when no text object is selected
           toggleGridDistortBtn.textContent = 'Show Grid';
       }
   }

   // Effect mode control
   effectModeSelect.onchange = (e) => {
       const newMode = e.target.value;
       updateSelectedObjectFromUI('effectMode', newMode);

       // If switching to grid-distort, make sure grid is initialized
       if (newMode === 'grid-distort' && selectedObjectIndex !== -1) {
           const selectedObject = canvasObjects[selectedObjectIndex];
           if (selectedObject && selectedObject.type === 'text') {
               console.log('Switching to grid-distort mode, initializing grid');

               // Set default values for Grid Distort
               selectedObject.gridDistortCols = 2;
               selectedObject.gridDistortRows = 1;
               selectedObject.gridDistortPadding = 120;
               selectedObject.gridDistortIntensity = 100;
               selectedObject.gridDistortVerticalOnly = true;

               // Update UI to reflect these values
               if (iGridDistortCols) {
                   iGridDistortCols.value = 2;
                   document.getElementById('vGridDistortCols').textContent = '2';
               }
               if (iGridDistortRows) {
                   iGridDistortRows.value = 1;
                   document.getElementById('vGridDistortRows').textContent = '1';
               }
               if (iGridDistortPadding) {
                   iGridDistortPadding.value = 120;
                   document.getElementById('vGridDistortPadding').textContent = '120px';
               }
               if (iGridDistortIntensity) {
                   iGridDistortIntensity.value = 100;
                   document.getElementById('vGridDistortIntensity').textContent = '100%';
               }
               if (gridDistortDirectionVertical && gridDistortDirectionBoth) {
                   gridDistortDirectionVertical.checked = true;
                   gridDistortDirectionBoth.checked = false;
               }

               // Initialize grid if not already initialized
               if (!selectedObject.gridDistort ||
                   !selectedObject.gridDistort.controlPoints ||
                   selectedObject.gridDistort.controlPoints.length === 0) {

                   initializeGridPoints(selectedObject);
                   console.log('Grid initialized for text:', selectedObject.text);
               }

               // Make sure the grid is visible by default
               if (selectedObject.gridDistort) {
                   selectedObject.gridDistort.showGrid = true;

                   // Update the toggle button text
                   if (toggleGridDistortBtn) {
                       toggleGridDistortBtn.textContent = 'Hide Grid';
                   }
               }
           }
       }

       // If switching to mesh warp, make sure the mesh handler is initialized
       // This is handled by the event listener in mesh-warp-implementation.js,
       // but we need to force an update to make sure the grid appears immediately
       if (newMode === 'mesh') {
           // Force a redraw to ensure the mesh grid appears
           update();
       }
   };

   // Shadow controls
   shadowSelect.onchange = (e) => {
       console.log('=== SHADOW MODE CHANGE DEBUG ===');
       const shadowMode = e.target.value;
       console.log('Shadow mode changed to:', shadowMode);

       // Check if we have a selected object
       if (selectedObjectIndex === -1) {
           console.error('No object selected when changing shadow mode!');
           return;
       }

       const selectedObject = canvasObjects[selectedObjectIndex];
       console.log('Selected object before update:', {
           id: selectedObject.id,
           text: selectedObject.text,
           shadowMode: selectedObject.shadowMode,
           blockShadowPerspective: selectedObject.blockShadowPerspective,
           blockShadowPerspectiveIntensity: selectedObject.blockShadowPerspectiveIntensity
       });

       updateSelectedObjectFromUI('shadowMode', shadowMode);

       console.log('Selected object after update:', {
           id: selectedObject.id,
           text: selectedObject.text,
           shadowMode: selectedObject.shadowMode,
           blockShadowPerspective: selectedObject.blockShadowPerspective,
           blockShadowPerspectiveIntensity: selectedObject.blockShadowPerspectiveIntensity
       });

       // Enable/disable the perspective toggle based on shadow mode
       if (blockShadowPerspective) {
           blockShadowPerspective.disabled = (shadowMode !== 'blockShadow');
           console.log('Perspective toggle disabled:', blockShadowPerspective.disabled);

           // If switching to block shadow and perspective was previously enabled, make sure it's still checked
           if (shadowMode === 'blockShadow' && selectedObject.blockShadowPerspective) {
               console.log('Ensuring perspective checkbox is checked');
               blockShadowPerspective.checked = true;

               // Show the perspective control
               const perspectiveControl = document.querySelector('.perspective-control');
               if (perspectiveControl) {
                   perspectiveControl.style.display = 'block';
                   console.log('Perspective control display set to: block');
               }
           }
       } else {
           console.error('Could not find blockShadowPerspective checkbox!');
       }

       // Show/hide shadow controls based on shadow mode
       // Only hide shadow-related parameter controls, not all parameter controls
       document.querySelectorAll('.shadow-param, .block-shadow-param, .perspective-shadow-param, .line-shadow-param, .detailed-3d-param').forEach(el => {
           el.style.display = 'none';
       });

       if (shadowMode === 'shadow') {
           const control = document.querySelector('.shadow-param');
           if (control) {
               control.style.display = 'block';
               console.log('Showing shadow-param controls');
           } else {
               console.error('Could not find shadow-param element!');
           }
       } else if (shadowMode === 'blockShadow') {
           const control = document.querySelector('.block-shadow-param');
           if (control) {
               control.style.display = 'block';
               console.log('Showing block-shadow-param controls');
           } else {
               console.error('Could not find block-shadow-param element!');
           }
       } else if (shadowMode === 'perspectiveShadow') {
           const control = document.querySelector('.perspective-shadow-param');
           if (control) {
               control.style.display = 'block';
               console.log('Showing perspective-shadow-param controls');
           } else {
               console.error('Could not find perspective-shadow-param element!');
           }
       } else if (shadowMode === 'lineShadow') {
           const control = document.querySelector('.line-shadow-param');
           if (control) {
               control.style.display = 'block';
               console.log('Showing line-shadow-param controls');
           } else {
               console.error('Could not find line-shadow-param element!');
           }
       } else if (shadowMode === 'detailed3D') {
           const control = document.querySelector('.detailed-3d-param');
           if (control) {
               control.style.display = 'block';
               console.log('Showing detailed-3d-param controls');
           } else {
               console.error('Could not find detailed-3d-param element!');
           }
       }

       // Force a redraw to ensure the effect is applied
       console.log('Forcing redraw...');
       update();
       console.log('=== END SHADOW MODE CHANGE DEBUG ===');
   };
   shadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('shadowColor', e.target.value);
   shadowOffsetXSlider.oninput = (e) => updateSelectedObjectFromUI('shadowOffsetX', parseInt(e.target.value, 10));
   shadowOffsetYSlider.oninput = (e) => updateSelectedObjectFromUI('shadowOffsetY', parseInt(e.target.value, 10));
   shadowBlurSlider.oninput = (e) => updateSelectedObjectFromUI('shadowBlur', parseInt(e.target.value, 10));
   blockShadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('blockShadowColor', e.target.value);
   blockShadowOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowOpacity', parseInt(e.target.value, 10));
   blockShadowOffsetSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowOffset', parseInt(e.target.value, 10));
   blockShadowAngleSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowAngle', parseInt(e.target.value, 10));
   blockShadowBlurSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowBlur', parseInt(e.target.value, 10));
   blockShadowPerspective.onchange = (e) => {
       console.log('=== PERSPECTIVE TOGGLE DEBUG ===');
       console.log('Perspective toggle changed to:', e.target.checked);

       // Check if we have a selected object
       if (selectedObjectIndex === -1) {
           console.error('No object selected when toggling perspective!');
           return;
       }

       const selectedObject = canvasObjects[selectedObjectIndex];
       console.log('Selected object before update:', {
           id: selectedObject.id,
           text: selectedObject.text,
           shadowMode: selectedObject.shadowMode,
           blockShadowPerspective: selectedObject.blockShadowPerspective,
           blockShadowPerspectiveIntensity: selectedObject.blockShadowPerspectiveIntensity
       });

       // Verify shadow mode is correct
       if (selectedObject.shadowMode !== 'blockShadow') {
           console.warn('Shadow mode is not blockShadow! Current mode:', selectedObject.shadowMode);
           console.log('Forcing shadow mode to blockShadow');
           selectedObject.shadowMode = 'blockShadow';
           if (shadowSelect) {
               shadowSelect.value = 'blockShadow';
           }
       }

       updateSelectedObjectFromUI('blockShadowPerspective', e.target.checked);

       // Show/hide and enable/disable the perspective intensity slider based on the toggle
       const perspectiveControl = document.querySelector('.perspective-control');
       if (perspectiveControl) {
           perspectiveControl.style.display = e.target.checked ? 'block' : 'none';
           console.log('Perspective control display set to:', perspectiveControl.style.display);
       } else {
           console.error('Could not find perspective-control element!');
       }

       if (blockShadowPerspectiveIntensity) {
           blockShadowPerspectiveIntensity.disabled = !e.target.checked;
           console.log('Perspective intensity slider disabled:', blockShadowPerspectiveIntensity.disabled);
       } else {
           console.error('Could not find blockShadowPerspectiveIntensity element!');
       }

       // Verify the object was updated correctly
       console.log('Selected object after update:', {
           id: selectedObject.id,
           text: selectedObject.text,
           shadowMode: selectedObject.shadowMode,
           blockShadowPerspective: selectedObject.blockShadowPerspective,
           blockShadowPerspectiveIntensity: selectedObject.blockShadowPerspectiveIntensity
       });

       // Force a redraw to ensure the effect is applied or removed
       console.log('Forcing redraw...');
       update();
       console.log('=== END PERSPECTIVE TOGGLE DEBUG ===');
   };
   blockShadowPerspectiveIntensity.oninput = (e) => {
       console.log('=== PERSPECTIVE INTENSITY DEBUG ===');
       const value = parseInt(e.target.value, 10);
       console.log('Perspective intensity changed to:', value);

       // Check if we have a selected object
       if (selectedObjectIndex === -1) {
           console.error('No object selected when changing perspective intensity!');
           return;
       }

       const selectedObject = canvasObjects[selectedObjectIndex];
       if (selectedObject.type !== 'text') {
           console.error('Selected object is not a text object!');
           return;
       }

       console.log('Selected object before update:', {
           id: selectedObject.id,
           text: selectedObject.text,
           shadowMode: selectedObject.shadowMode,
           blockShadowPerspective: selectedObject.blockShadowPerspective,
           blockShadowPerspectiveIntensity: selectedObject.blockShadowPerspectiveIntensity
       });

       // Verify shadow mode is correct
       if (selectedObject.shadowMode !== 'blockShadow') {
           console.warn('Shadow mode is not blockShadow! Current mode:', selectedObject.shadowMode);
           console.log('Forcing shadow mode to blockShadow');
           selectedObject.shadowMode = 'blockShadow';
           if (shadowSelect) {
               shadowSelect.value = 'blockShadow';
           }
       }

       // Ensure perspective is enabled
       if (!selectedObject.blockShadowPerspective) {
           console.log('Forcing perspective mode to be enabled');
           selectedObject.blockShadowPerspective = true;

           if (blockShadowPerspective) {
               blockShadowPerspective.checked = true;
               console.log('Updated checkbox state to checked');
           } else {
               console.error('Could not find blockShadowPerspective checkbox!');
           }

           // Show the perspective control
           const perspectiveControl = document.querySelector('.perspective-control');
           if (perspectiveControl) {
               perspectiveControl.style.display = 'block';
               console.log('Perspective control display set to: block');
           } else {
               console.error('Could not find perspective-control element!');
           }
       }

       console.log('Updating blockShadowPerspectiveIntensity to:', value);
       updateSelectedObjectFromUI('blockShadowPerspectiveIntensity', value);

       // Verify the object was updated correctly
       console.log('Selected object after update:', {
           id: selectedObject.id,
           text: selectedObject.text,
           shadowMode: selectedObject.shadowMode,
           blockShadowPerspective: selectedObject.blockShadowPerspective,
           blockShadowPerspectiveIntensity: selectedObject.blockShadowPerspectiveIntensity
       });

       // Force a redraw to ensure the effect is applied
       console.log('Forcing redraw...');
       update();
       console.log('=== END PERSPECTIVE INTENSITY DEBUG ===');
   };

   // Perspective Shadow controls
   const perspectiveShadowColorPicker = document.getElementById('perspectiveShadowColor');
   const perspectiveShadowOpacitySlider = document.getElementById('perspectiveShadowOpacity');
   const perspectiveShadowOffsetSlider = document.getElementById('perspectiveShadowOffset');
   const perspectiveShadowAngleSlider = document.getElementById('perspectiveShadowAngle');
   const perspectiveShadowBlurSlider = document.getElementById('perspectiveShadowBlur');
   const perspectiveShadowIntensitySlider = document.getElementById('perspectiveShadowIntensity');

   if (perspectiveShadowColorPicker) perspectiveShadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('perspectiveShadowColor', e.target.value);
   if (perspectiveShadowOpacitySlider) perspectiveShadowOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('perspectiveShadowOpacity', parseInt(e.target.value, 10));
   if (perspectiveShadowOffsetSlider) perspectiveShadowOffsetSlider.oninput = (e) => updateSelectedObjectFromUI('perspectiveShadowOffset', parseInt(e.target.value, 10));
   if (perspectiveShadowAngleSlider) perspectiveShadowAngleSlider.oninput = (e) => updateSelectedObjectFromUI('perspectiveShadowAngle', parseInt(e.target.value, 10));
   if (perspectiveShadowBlurSlider) perspectiveShadowBlurSlider.oninput = (e) => updateSelectedObjectFromUI('perspectiveShadowBlur', parseInt(e.target.value, 10));

   if (perspectiveShadowIntensitySlider) {
       perspectiveShadowIntensitySlider.oninput = (e) => {
           console.log('=== PERSPECTIVE SHADOW INTENSITY DEBUG ===');
           const value = parseInt(e.target.value, 10);
           console.log('Perspective shadow intensity changed to:', value);

           // Check if we have a selected object
           if (selectedObjectIndex === -1) {
               console.error('No object selected when changing perspective shadow intensity!');
               return;
           }

           const selectedObject = canvasObjects[selectedObjectIndex];
           if (selectedObject.type !== 'text') {
               console.error('Selected object is not a text object!');
               return;
           }

           console.log('Selected object before update:', {
               id: selectedObject.id,
               text: selectedObject.text,
               shadowMode: selectedObject.shadowMode,
               perspectiveShadowIntensity: selectedObject.perspectiveShadowIntensity
           });

           // Verify shadow mode is correct
           if (selectedObject.shadowMode !== 'perspectiveShadow') {
               console.warn('Shadow mode is not perspectiveShadow! Current mode:', selectedObject.shadowMode);
               console.log('Forcing shadow mode to perspectiveShadow');
               selectedObject.shadowMode = 'perspectiveShadow';
               if (shadowSelect) {
                   shadowSelect.value = 'perspectiveShadow';
               }
           }

           console.log('Updating perspectiveShadowIntensity to:', value);
           updateSelectedObjectFromUI('perspectiveShadowIntensity', value);

           // Verify the object was updated correctly
           console.log('Selected object after update:', {
               id: selectedObject.id,
               text: selectedObject.text,
               shadowMode: selectedObject.shadowMode,
               perspectiveShadowIntensity: selectedObject.perspectiveShadowIntensity
           });

           // Force a redraw to ensure the effect is applied
           console.log('Forcing redraw...');
           update();
           console.log('=== END PERSPECTIVE SHADOW INTENSITY DEBUG ===');
       };
   }

   lineShadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('lineShadowColor', e.target.value);
   lineShadowDistanceSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowDist', parseInt(e.target.value, 10));
   lineShadowAngleSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowAngle', parseInt(e.target.value, 10));
   lineShadowThicknessSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowThickness', parseInt(e.target.value, 10));
   detailed3DPrimaryColorPicker.oninput = (e) => updateSelectedObjectFromUI('d3dPrimaryColor', e.target.value);
   detailed3DPrimaryOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('d3dPrimaryOpacity', parseInt(e.target.value, 10));
   detailed3DOffsetSlider.oninput = (e) => updateSelectedObjectFromUI('d3dOffset', parseInt(e.target.value, 10));
   detailed3DAngleSlider.oninput = (e) => updateSelectedObjectFromUI('d3dAngle', parseInt(e.target.value, 10));
   detailed3DBlurSlider.oninput = (e) => updateSelectedObjectFromUI('d3dBlur', parseInt(e.target.value, 10));
   detailed3DSecondaryColorPicker.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryColor', e.target.value);
   detailed3DSecondaryOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOpacity', parseInt(e.target.value, 10));
   detailed3DSecondaryWidthSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryWidth', parseInt(e.target.value, 10));
   detailed3DSecondaryOffsetXSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOffsetX', parseInt(e.target.value, 10));
   detailed3DSecondaryOffsetYSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOffsetY', parseInt(e.target.value, 10));

   // Stroke and decoration controls
   strokeToggle.onchange = (e) => updateSelectedObjectFromUI('strokeMode', e.target.value);
   strokeWidthSlider.oninput = (e) => updateSelectedObjectFromUI('strokeWidth', parseInt(e.target.value, 10));
   strokeColorPicker.oninput = (e) => updateSelectedObjectFromUI('strokeColor', e.target.value);
   strokeOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('strokeOpacity', parseInt(e.target.value, 10));
   linesDecorationSelect.onchange = (e) => updateSelectedObjectFromUI('decorationMode', e.target.value);
   hWeight.oninput = (e) => updateSelectedObjectFromUI('hLineWeight', parseInt(e.target.value, 10));
   hDistance.oninput = (e) => updateSelectedObjectFromUI('hLineDist', parseInt(e.target.value, 10));
   hColor.oninput = (e) => updateSelectedObjectFromUI('hLineColor', e.target.value);
   ccDistance.oninput = (e) => updateSelectedObjectFromUI('ccDist', parseInt(e.target.value, 10));
   ccColor.oninput = (e) => updateSelectedObjectFromUI('ccColor', e.target.value);
   ccFillTop.onchange = () => updateSelectedObjectFromUI('ccFillDir', 'top');
   ccFillBottom.onchange = () => updateSelectedObjectFromUI('ccFillDir', 'bottom');
   oWeight.oninput = (e) => updateSelectedObjectFromUI('oLineWeight', parseInt(e.target.value, 10));
   oDistance.oninput = (e) => updateSelectedObjectFromUI('oLineDist', parseInt(e.target.value, 10));
   oColor.oninput = (e) => updateSelectedObjectFromUI('oLineColor', e.target.value);
   flcDistance.oninput = (e) => updateSelectedObjectFromUI('flcDist', parseInt(e.target.value, 10));
   flcColor.oninput = (e) => updateSelectedObjectFromUI('flcColor', e.target.value);
   flcMaxWeight.oninput = (e) => updateSelectedObjectFromUI('flcWeight', parseInt(e.target.value, 10));
   flcSpacing.oninput = (e) => updateSelectedObjectFromUI('flcSpacing', parseInt(e.target.value, 10));
   flcFillTop.onchange = () => updateSelectedObjectFromUI('flcDir', 'top');
   flcFillBottom.onchange = () => updateSelectedObjectFromUI('flcDir', 'bottom');
   addImageBtn.onclick = () => imageFileInput.click(); imageFileInput.onchange = (e) => { if (e.target.files && e.target.files[0]) { handleAddImage(e.target.files[0]); } e.target.value = null; }; deleteImageBtn.onclick = handleDeleteObject; iImageSize.oninput = (e) => updateSelectedObjectFromUI('scale', e.target.value); iImageRotation.oninput = (e) => updateSelectedObjectFromUI('rotation', e.target.value);
   document.getElementById('removeBgBtn').addEventListener('click', handleBgRemoveClick); // Add listener for new button

   // Add event listener for SVG color picker
   const iImageColor = document.getElementById('iImageColor');
   if (iImageColor) {
       iImageColor.oninput = (e) => updateSelectedObjectFromUI('svgColor', e.target.value);
   }

   // Image shadow controls event handlers
   if (iImageShadow) {
       iImageShadow.onchange = (e) => {
           updateSelectedObjectFromUI('shadowMode', e.target.value);
           // Show/hide shadow parameter controls
           const imageShadowParam = document.querySelector('.image-shadow-param');
           if (imageShadowParam) {
               imageShadowParam.style.display = e.target.value === 'standard' ? 'block' : 'none';
           }
       };
   }
   if (iImageShadowColor) {
       iImageShadowColor.oninput = (e) => updateSelectedObjectFromUI('shadowColor', e.target.value);
   }
   if (iImageShadowOffsetX) {
       iImageShadowOffsetX.oninput = (e) => updateSelectedObjectFromUI('shadowOffsetX', e.target.value);
   }
   if (iImageShadowOffsetY) {
       iImageShadowOffsetY.oninput = (e) => updateSelectedObjectFromUI('shadowOffsetY', e.target.value);
   }
   if (iImageShadowBlur) {
       iImageShadowBlur.oninput = (e) => updateSelectedObjectFromUI('shadowBlur', e.target.value);
   }
   if (iImageShadowOpacity) {
       iImageShadowOpacity.oninput = (e) => updateSelectedObjectFromUI('shadowOpacity', e.target.value);
   }
   if (iImageOpacity) {
       iImageOpacity.oninput = (e) => updateSelectedObjectFromUI('opacity', e.target.value);
   }

   // Image stroke controls event handlers
   if (iImageStroke) {
       iImageStroke.onchange = (e) => {
           updateSelectedObjectFromUI('strokeMode', e.target.value);
           // Show/hide stroke parameter controls
           const imageStrokeParam = document.querySelector('.image-stroke-param');
           if (imageStrokeParam) {
               imageStrokeParam.style.display = e.target.value === 'standard' ? 'block' : 'none';
           }
       };
   }
   if (iImageStrokeWidth) {
       iImageStrokeWidth.oninput = (e) => updateSelectedObjectFromUI('strokeWidth', e.target.value);
   }
   if (iImageStrokeColor) {
       iImageStrokeColor.oninput = (e) => updateSelectedObjectFromUI('strokeColor', e.target.value);
   }

   // Add stroke opacity control
   if (iImageStrokeOpacity) {
       iImageStrokeOpacity.oninput = (e) => updateSelectedObjectFromUI('strokeOpacity', e.target.value);
   }

   // Initialize gradient color pickers
   initializeGradientColorPickers();
   canvas.addEventListener('mousedown', handleMouseDown); canvas.addEventListener('wheel', handleWheel, { passive: false }); window.addEventListener('mousemove', handleMouseMove); window.addEventListener('mouseup', handleMouseUp); canvas.addEventListener('mouseleave', handleMouseLeave);
   zoomInBtn.addEventListener('click', () => zoom(1.2)); zoomOutBtn.addEventListener('click', () => zoom(1 / 1.2));
   // Ensure listeners are added only once, check if they exist before adding if necessary
   if (moveForwardBtn && !moveForwardBtn.onclick) { // Basic check
       moveForwardBtn.addEventListener('click', moveObjectForward);
   }
   if (moveBackwardBtn && !moveBackwardBtn.onclick) { // Basic check
       moveBackwardBtn.addEventListener('click', moveObjectBackward);
   }

   // Background Color Picker Listeners (Now using Pickr)
   // Pickr initialization moved to DOMContentLoaded listener below

   const sidebarTabs = document.querySelectorAll('.sidebar-tab'); const sidebarContents = document.querySelectorAll('.sidebar-content'); const propertyTabs = document.querySelectorAll('.property-tab'); const propertyPanels = document.querySelectorAll('.property-panel'); sidebarTabs.forEach(tab => { tab.addEventListener('click', () => { const targetContentId = tab.getAttribute('data-tab'); sidebarTabs.forEach(t => t.classList.remove('active')); sidebarContents.forEach(c => c.classList.remove('active')); tab.classList.add('active'); document.getElementById(targetContentId)?.classList.add('active'); }); }); propertyTabs.forEach(tab => { tab.addEventListener('click', () => { const targetPanelClass = tab.getAttribute('data-panel'); propertyTabs.forEach(t => t.classList.remove('active')); propertyPanels.forEach(p => p.classList.remove('active')); tab.classList.add('active'); document.querySelector(`#text-controls .${targetPanelClass}`)?.classList.add('active'); }); });

   // --- Background Removal Handler ---
   async function handleBgRemoveClick() {
       if (selectedObjectIndex === -1 || canvasObjects[selectedObjectIndex].type !== 'image') return;

       const imageObj = canvasObjects[selectedObjectIndex];
       if (!imageObj.isFromGeneration || !imageObj.generationId || imageObj.backgroundRemoved) {
           console.warn('Background removal not applicable or already done for this image.');
           return;
       }

       const removeBgBtn = document.getElementById('removeBgBtn');
       removeBgBtn.disabled = true;
       removeBgBtn.textContent = 'Removing...';
       if (window.showToast) window.showToast('Removing background...', 'info');

       try {
           const response = await fetch('/api/images/bgremove', {
               method: 'POST',
               headers: { 'Content-Type': 'application/json' },
               body: JSON.stringify({
                   imageUrl: imageObj.imageUrl, // Send the current (likely B2) URL
                   generationId: imageObj.generationId
               })
           });

           const data = await response.json();

           if (!response.ok) {
               throw new Error(data.error || data.details || 'Background removal failed');
           }

           const newImageUrl = data.imageUrl; // URL of the BG-removed image in B2
           console.log('Background removed successfully. New URL:', newImageUrl);

           // Load the new image
           const newImg = new Image();
           newImg.crossOrigin = 'anonymous';
           newImg.onload = () => {
               imageObj.image = newImg; // Replace the image element in the object
               imageObj.imageUrl = newImageUrl; // Update the stored URL
               imageObj.backgroundRemoved = true; // Mark as removed
               imageObj.originalWidth = newImg.naturalWidth; // Update dimensions if needed
               imageObj.originalHeight = newImg.naturalHeight;
               removeBgBtn.textContent = 'Background Removed'; // Keep disabled
               removeBgBtn.style.display = 'none'; // Hide after success
               update(); // Redraw canvas
               if (window.showToast) window.showToast('Background removed!', 'success');
           };
           newImg.onerror = () => {
               console.error('Failed to load the background-removed image:', newImageUrl);
               throw new Error('Failed to load updated image');
           };
           newImg.src = getProxiedImageUrlIfNeeded(newImageUrl); // Use proxy helper

       } catch (error) {
           console.error('Error removing background:', error);
           if (window.showToast) window.showToast(`Error: ${error.message}`, 'error');
           else alert(`Error removing background: ${error.message}`);
           removeBgBtn.disabled = false; // Re-enable on error
           removeBgBtn.textContent = 'Remove Background';
       }
   }

   // --- Initial State ---
   function initialize() { scale = Math.min(canvas.clientWidth / w, canvas.clientHeight / h) * 0.8; offsetX = (canvas.clientWidth - w * scale) / 2; offsetY = (canvas.clientHeight - h * scale) / 2; const initialObject = createTextObject({ text: "DESIGN", isSelected: false, x: w / 2 - 150, y: h / 2, fontSize: 200, color: '#3b82f6' }); canvasObjects.push(initialObject); selectedObjectIndex = -1; syncGlobalReferences(); applyFontStylesToOptions(); updateUIFromSelectedObject(); update(); }
   // initialize(); // Initialization is now handled by DOMContentLoaded logic
   // --- Helper to get proxied URL if needed ---
   function getProxiedImageUrlIfNeeded(imageUrl) {
       if (!imageUrl) return null;
       try {
           const url = new URL(imageUrl);
           if (url.hostname.endsWith('backblazeb2.com')) {
               const pathSegments = url.pathname.split('/');
               const bucketName = 'stickers-replicate-app'; // Adjust if dynamic
               const bucketNameIndex = pathSegments.indexOf(bucketName);
               if (bucketNameIndex !== -1 && bucketNameIndex + 1 < pathSegments.length) {
                   const fileName = pathSegments.slice(bucketNameIndex + 1).join('/');
                   const proxiedUrl = `/api/image-proxy?fileName=${encodeURIComponent(fileName)}`;
                   console.log(`[ProxyHelper] Using proxy for B2 URL: ${imageUrl} -> ${proxiedUrl}`);
                   return proxiedUrl;
               } else {
                   console.warn('[ProxyHelper] Could not extract filename from B2 URL for proxy:', imageUrl);
                   return imageUrl; // Return original if parsing fails
               }
           }
           return imageUrl; // Not a B2 URL, return original
       } catch (urlError) {
           console.warn('[ProxyHelper] Could not parse URL, assuming relative or already proxied:', imageUrl, urlError);
           // If it's not a valid URL, it might be a path already handled by a proxy or relative
           // Let's try the proxy logic anyway if it looks like a path
           if (!imageUrl.startsWith('http') && imageUrl.includes('/')) {
                const proxiedUrl = `/api/image-proxy?fileName=${encodeURIComponent(imageUrl)}`;
                console.log('[ProxyHelper] Assuming path needs proxy:', proxiedUrl);
                return proxiedUrl;
           }
           return imageUrl; // Return original if it's not a URL and doesn't look like a path
       }
   }

   // --- Load Image from URL Parameter ---
   function loadImageFromUrlParam() {
       const params = new URLSearchParams(window.location.search);
       const imageUrlParam = params.get('imageUrl') || params.get('image');
       const generationIdParam = params.get('generationId'); // Get generationId

       if (imageUrlParam) {
           try {
               const decodedImageUrl = decodeURIComponent(imageUrlParam);
               const finalImageUrl = getProxiedImageUrlIfNeeded(decodedImageUrl); // Use helper

               if (!finalImageUrl) {
                    console.error('Could not determine final image URL for loading.');
                    return;
               }

               const img = new Image();
               img.crossOrigin = 'anonymous';
               img.onload = () => {
                   const canvas = document.getElementById('demo');
                   if (!canvas) return;
                   const ctx = canvas.getContext('2d');
                   if (!ctx) return;

                   const scaleFactor = Math.min(canvas.width / img.width, canvas.height / img.height, 1) * 0.8;
                   const centerX = w / 2;
                   const centerY = h / 2;

                   const imageObj = createImageObject(img, {
                       x: centerX,
                       y: centerY,
                       scale: scaleFactor,
                       isSelected: true,
                       imageUrl: finalImageUrl, // Store the potentially proxied URL
                       generationId: generationIdParam || null, // Store generationId if present
                       isFromGeneration: !!generationIdParam // Set flag if generationId exists
                   });

                   // Replace existing image or add new
                   const existingImageIndex = canvasObjects.findIndex(obj => obj.type === 'image');
                   if (existingImageIndex !== -1) {
                       canvasObjects[existingImageIndex] = imageObj;
                       selectedObjectIndex = existingImageIndex;
                   } else {
                       canvasObjects.push(imageObj);
                       selectedObjectIndex = canvasObjects.length - 1;
                   }
                   // canvasObjects = canvasObjects.filter(obj => obj.type !== 'image'); // Keep other objects
                   canvasObjects.push(imageObj);
                   selectedObjectIndex = canvasObjects.length - 1;
                   updateUIFromSelectedObject();
                   update();
               };
               img.onerror = (err) => {
                   console.error('Failed to load image:', finalImageUrl, err);
                   alert(`Failed to load image from URL: ${decodedImageUrl}`);
               };
               img.src = finalImageUrl;

           } catch (e) {
               console.error('Error processing image URL parameter:', e);
           }
       }
   }

   // --- Load Admin Data from URL Parameter ---
   function loadAdminDataFromUrlParam() {
        const params = new URLSearchParams(window.location.search);
        const imageUrl = params.get('imageUrl'); // Use specific param name
        const model = params.get('model');
        const prompt = params.get('prompt');
        const palette = params.get('palette');
        const inspirationId = params.get('inspirationId');

        if (imageUrl || model || prompt || palette || inspirationId) {
            console.log('Admin data found in URL parameters.');
            document.getElementById('adminImageUrl').value = decodeURIComponent(imageUrl || '');
            document.getElementById('adminModel').value = decodeURIComponent(model || '');
            document.getElementById('adminPrompt').value = decodeURIComponent(prompt || '');
            document.getElementById('adminPalette').value = decodeURIComponent(palette || '');
            document.getElementById('adminInspirationId').value = decodeURIComponent(inspirationId || '');

            // Optionally switch to the Admin tab if data is present
            const adminTabButton = document.querySelector('.sidebar-tab[data-tab="admin-tab-content"]');
            const adminTabContent = document.getElementById('admin-tab-content');
            if (adminTabButton && adminTabContent) {
                sidebarTabs.forEach(t => t.classList.remove('active'));
                sidebarContents.forEach(c => c.classList.remove('active'));
                adminTabButton.classList.add('active');
                adminTabContent.classList.add('active');
            }
        }
   }

   // --- Update Editor UI from Restored State ---
   function updateEditorUIFromState() {
       console.log('[UpdateUI] Updating editor UI from restored state');

       // Update zoom level display
       const zoomLevelSpan = document.getElementById('zoomLevel');
       if (zoomLevelSpan) {
           zoomLevelSpan.textContent = Math.round(scale * 100) + '%';
       }

       // Update canvas background color picker if it exists
       const colorPickerElement = document.getElementById('canvasBgColorPicker');
       if (colorPickerElement && window.pickrInstance) {
           try {
               window.pickrInstance.setColor(canvasBackgroundColor);
           } catch (e) {
               console.warn('[UpdateUI] Could not update color picker:', e);
           }
       }

       // Update any other UI elements that reflect editor state
       console.log('[UpdateUI] Editor UI updated with:', {
           scale: scale,
           offsetX: offsetX,
           offsetY: offsetY,
           canvasBackgroundColor: canvasBackgroundColor,
           selectedObjectIndex: selectedObjectIndex,
           nextId: nextId
       });
   }

   // --- Update Editor UI from Restored State ---
   function updateEditorUIFromState() {
       console.log('[UpdateUI] Updating editor UI from restored state');

       // Update zoom level display
       const zoomLevelSpan = document.getElementById('zoomLevel');
       if (zoomLevelSpan) {
           zoomLevelSpan.textContent = Math.round(scale * 100) + '%';
       }

       // Update canvas background color picker if it exists
       const colorPickerElement = document.getElementById('canvasBgColorPicker');
       if (colorPickerElement && window.pickrInstance) {
           try {
               window.pickrInstance.setColor(canvasBackgroundColor);
           } catch (e) {
               console.warn('[UpdateUI] Could not update color picker:', e);
           }
       }

       console.log('[UpdateUI] Editor UI updated with restored state');
   }

   // --- Load Template Data ---
   async function loadTemplateData(templateId) {
       console.log(`[LoadTemplate] Fetching template data for ID: ${templateId}`);
       try {
           const response = await fetch(`/api/design-templates/${templateId}`, { credentials: 'include' });
           if (!response.ok) {
               throw new Error(`Failed to fetch template: ${response.statusText}`);
           }
           const template = await response.json();
           console.log('[LoadTemplate] Received template data:', template);

           canvasObjects = [];
           selectedObjectIndex = -1;

           // Restore editor state if available
           if (template.editorState) {
               console.log('[LoadTemplate] Restoring editor state:', template.editorState);

               // Restore canvas background color
               if (template.editorState.canvasBackgroundColor) {
                   canvasBackgroundColor = template.editorState.canvasBackgroundColor;
                   console.log('[LoadTemplate] Restored canvas background color:', canvasBackgroundColor);
               }

               // Restore zoom and positioning
               if (template.editorState.zoom) {
                   scale = template.editorState.zoom.scale || 1.0;
                   offsetX = template.editorState.zoom.offsetX || 0;
                   offsetY = template.editorState.zoom.offsetY || 0;
                   console.log('[LoadTemplate] Restored zoom/position:', { scale, offsetX, offsetY });
               }

               // Restore nextId to avoid ID conflicts
               if (template.editorState.nextId) {
                   nextId = template.editorState.nextId;
                   console.log('[LoadTemplate] Restored nextId:', nextId);
               }

               // Restore other editor settings
               if (template.editorState.editorSettings) {
                   // Apply any additional editor settings
                   console.log('[LoadTemplate] Additional editor settings:', template.editorState.editorSettings);
               }
           } else {
               console.log('[LoadTemplate] No editor state found, using defaults');
           }

           if (template.artboard) {
               artboard = {
                   x: Number(template.artboard.x),
                   y: Number(template.artboard.y),
                   width: Number(template.artboard.width),
                   height: Number(template.artboard.height),
                   isSelected: true
               };
               console.log('[LoadTemplate] Restored artboard:', artboard);
           } else {
               artboard = null;
               console.log('[LoadTemplate] No artboard data found in template.');
           }

           if (template.adminData) {
               document.getElementById('adminImageUrl').value = template.adminData.imageUrl || '';
               document.getElementById('adminModel').value = template.adminData.model || '';
               document.getElementById('adminPrompt').value = template.adminData.prompt || '';
               document.getElementById('adminPalette').value = template.adminData.palette || '';
               document.getElementById('adminInspirationId').value = template.inspirationId || '';
               console.log('[LoadTemplate] Restored admin data.');
           }

           if (template.canvasObjects && Array.isArray(template.canvasObjects)) {
               console.log('[LoadTemplate] Starting to restore canvas objects...');
               const objectPromises = template.canvasObjects.map(objData => {
                   return new Promise((resolve, reject) => {
                       if (objData.type === 'text') {
                           const textObj = createTextObject(objData);
                           textObj.id = objData.id ?? nextId++;
                           canvasObjects.push(textObj);
                           resolve();
                       } else if (objData.type === 'image' && objData.imageUrl) {
                           const img = new Image();
                           img.crossOrigin = 'anonymous';
                           const finalImageUrl = getProxiedImageUrlIfNeeded(objData.imageUrl); // Proxy if needed
                           img.onload = () => {
                               const imageObj = createImageObject(img, objData);
                               imageObj.id = objData.id ?? nextId++;
                               imageObj.imageUrl = finalImageUrl; // Store the potentially proxied URL
                               canvasObjects.push(imageObj);
                               resolve();
                           };
                           img.onerror = (err) => {
                               console.error(`[LoadTemplate] Failed to load image for object ${objData.id}: ${finalImageUrl}`, err);
                               resolve();
                           };
                           img.src = finalImageUrl;
                       } else {
                           console.warn('[LoadTemplate] Skipping unknown or invalid object data:', objData);
                           resolve();
                       }
                   });
               });

               await Promise.all(objectPromises);
               console.log('[LoadTemplate] Restored canvas objects:', canvasObjects);

               // Initialize mesh warp handlers for text objects with mesh effect
               canvasObjects.forEach(obj => {
                   if (obj.type === 'text' && obj.effectMode === 'mesh' && obj.meshWarp) {
                       console.log('[LoadTemplate] Initializing mesh warp handler for text object:', obj.text);
                       console.log('[LoadTemplate] Mesh warp data:', obj.meshWarp);
                       try {
                           // Restore control points from saved data BEFORE creating handler
                           // This prevents initMeshGrid from resetting hasCustomDistortion
                           let savedControlPoints = null;
                           let savedInitialControlPoints = null;
                           let savedRelativeControlPoints = null;
                           let savedHasCustomDistortion = false;
                           let savedShowGrid = true;
                           let savedGridRect = null;

                           if (obj.meshWarp.controlPoints && obj.meshWarp.controlPoints.length > 0) {
                               console.log('[LoadTemplate] Preparing to restore control points:', obj.meshWarp.controlPoints.length, 'points');
                               savedControlPoints = obj.meshWarp.controlPoints.map(p => ({ ...p }));
                               savedInitialControlPoints = obj.meshWarp.initialControlPoints.map(p => ({ ...p }));
                               savedRelativeControlPoints = obj.meshWarp.relativeControlPoints.map(p => ({ ...p }));
                               savedHasCustomDistortion = obj.meshWarp.hasCustomDistortion;
                               savedShowGrid = obj.meshWarp.showGrid;
                               savedGridRect = obj.meshWarp.gridRect ? { ...obj.meshWarp.gridRect } : null;
                               console.log('[LoadTemplate] Saved hasCustomDistortion:', savedHasCustomDistortion);
                           }

                           // Create a new mesh warp handler for this object
                           const meshHandler = new MeshWarpHandler(
                               document.getElementById('demo'),
                               obj
                           );

                           // Now restore the saved data to the handler
                           if (savedControlPoints) {
                               console.log('[LoadTemplate] Restoring control points to handler');
                               meshHandler.controlPoints = savedControlPoints;
                               meshHandler.initialControlPoints = savedInitialControlPoints;
                               meshHandler.relativeControlPoints = savedRelativeControlPoints;
                               meshHandler.hasCustomDistortion = savedHasCustomDistortion;
                               meshHandler.showGrid = savedShowGrid;
                               meshHandler.initialGridRect = savedGridRect;
                               console.log('[LoadTemplate] Restored mesh warp control points for:', obj.text, 'Points:', meshHandler.controlPoints.length);
                               console.log('[LoadTemplate] First control point:', meshHandler.controlPoints[0]);
                               console.log('[LoadTemplate] hasCustomDistortion:', meshHandler.hasCustomDistortion);
                           } else {
                               console.warn('[LoadTemplate] No control points found in mesh warp data for:', obj.text);
                           }

                           // Store the handler reference (we'll only keep one active at a time)
                           obj._meshWarpHandler = meshHandler;
                           console.log('[LoadTemplate] Stored mesh handler reference in obj._meshWarpHandler for:', obj.text);
                       } catch (error) {
                           console.error('[LoadTemplate] Error initializing mesh warp handler for:', obj.text, error);
                       }
                   } else {
                       console.log('[LoadTemplate] Skipping object:', obj.text || obj.type, 'effectMode:', obj.effectMode, 'hasMeshWarp:', !!obj.meshWarp);
                   }
               });
           }

           // Auto-activate mesh warp handlers for immediate visual feedback
           setTimeout(() => {
               let meshTextFound = false;
               canvasObjects.forEach((obj, index) => {
                   if (obj.type === 'text' && obj.effectMode === 'mesh' && obj._meshWarpHandler && !meshTextFound) {
                       console.log('[LoadTemplate] Auto-activating mesh warp for immediate display:', obj.text);

                       // Temporarily select the object to activate mesh warp
                       const previousSelection = selectedObjectIndex;
                       selectedObjectIndex = index;
                       obj.isSelected = true;

                       // Activate the mesh warp handler
                       activeMeshWarpHandler = obj._meshWarpHandler;
                       activeMeshWarpHandler.selectedTextObject = obj;

                       // Force a redraw to show the distortion
                       update();

                       // After a brief moment, restore the previous selection state
                       setTimeout(() => {
                           if (previousSelection === -1) {
                               // No previous selection, deselect everything
                               selectedObjectIndex = -1;
                               obj.isSelected = false;
                               console.log('[LoadTemplate] Auto-deselected mesh text for clean display');
                           } else {
                               // Restore previous selection
                               selectedObjectIndex = previousSelection;
                               obj.isSelected = false;
                               if (canvasObjects[previousSelection]) {
                                   canvasObjects[previousSelection].isSelected = true;
                               }
                               console.log('[LoadTemplate] Restored previous selection:', previousSelection);
                           }

                           // Update UI to reflect the final selection state
                           updateUIFromSelectedObject();
                           update();
                       }, 100); // Very brief selection - just enough to activate mesh warp

                       meshTextFound = true; // Only auto-activate the first mesh text found
                   }
               });

               if (!meshTextFound) {
                   console.log('[LoadTemplate] No mesh warp text objects found for auto-activation');
               }
           }, 200); // Small delay to ensure everything is fully loaded

           // Sync global references to ensure window.canvasObjects points to the current array
           syncGlobalReferences();
           console.log('[LoadTemplate] Synced global references. window.canvasObjects length:', window.canvasObjects.length);

           // Re-initialize Elements accordion to restore shape click handlers
           if (typeof window.reinitializeElementsAccordion === 'function') {
               window.reinitializeElementsAccordion();
           }

           // Update UI elements to reflect restored editor state
           updateEditorUIFromState();
           updateUIFromSelectedObject();
           update();
           alert('Template loaded successfully!');

       } catch (error) {
           console.error('[LoadTemplate] Error loading template:', error);
           alert(`Error loading template: ${error.message}`);
           canvasObjects = [];
           selectedObjectIndex = -1;
           artboard = null;
           updateUIFromSelectedObject();
           update();
       }
   }

   // --- New function to load and merge generated design data ---
   async function loadGeneratedDesign(templateId, newImageUrl, userTexts) {
       console.log(`[LoadGenerated] Loading template ${templateId} and merging with new image/text`);
       try {
           // 1. Fetch the original template data
           const response = await fetch(`/api/design-templates/${templateId}`, { credentials: 'include' });
           if (!response.ok) {
               throw new Error(`Failed to fetch original template: ${response.statusText}`);
           }
           const template = await response.json();
           console.log('[LoadGenerated] Received original template data:', template);

           canvasObjects = [];
           selectedObjectIndex = -1;

           // Restore editor state if available (same as loadTemplateData)
           if (template.editorState) {
               console.log('[LoadGenerated] Restoring editor state:', template.editorState);

               // Restore canvas background color
               if (template.editorState.canvasBackgroundColor) {
                   canvasBackgroundColor = template.editorState.canvasBackgroundColor;
                   console.log('[LoadGenerated] Restored canvas background color:', canvasBackgroundColor);
               }

               // Restore zoom and positioning
               if (template.editorState.zoom) {
                   scale = template.editorState.zoom.scale || 1.0;
                   offsetX = template.editorState.zoom.offsetX || 0;
                   offsetY = template.editorState.zoom.offsetY || 0;
                   console.log('[LoadGenerated] Restored zoom/position:', { scale, offsetX, offsetY });
               }

               // Restore nextId to avoid ID conflicts
               if (template.editorState.nextId) {
                   nextId = template.editorState.nextId;
                   console.log('[LoadGenerated] Restored nextId:', nextId);
               }
           } else {
               console.log('[LoadGenerated] No editor state found, using defaults');
           }

           if (template.artboard) {
               artboard = {
                   x: Number(template.artboard.x),
                   y: Number(template.artboard.y),
                   width: Number(template.artboard.width),
                   height: Number(template.artboard.height),
                   isSelected: true
               };
               console.log('[LoadGenerated] Restored artboard:', artboard);
           } else {
               artboard = null;
               console.warn('[LoadGenerated] Original template has no artboard data!');
           }

           if (template.adminData) {
               document.getElementById('adminImageUrl').value = template.adminData.imageUrl || '';
               document.getElementById('adminModel').value = template.adminData.model || '';
               document.getElementById('adminPrompt').value = template.adminData.prompt || '';
               document.getElementById('adminPalette').value = template.adminData.palette || '';
               document.getElementById('adminInspirationId').value = template.inspirationId || '';
               console.log('[LoadGenerated] Restored admin data.');
           }

           if (template.canvasObjects && Array.isArray(template.canvasObjects)) {
               console.log('[LoadGenerated] Starting to restore and modify canvas objects...');
               let textIndex = 0;
               const objectPromises = template.canvasObjects.map(objData => {
                   return new Promise((resolve, reject) => {
                       if (objData.type === 'text') {
                           const textObj = createTextObject(objData);
                           textObj.id = objData.id ?? nextId++;
                           if (userTexts && textIndex < userTexts.length && userTexts[textIndex]) {
                               textObj.text = userTexts[textIndex];
                               console.log(`[LoadGenerated] Updated text for object ${textObj.id} to: "${textObj.text}"`);
                           } else {
                                console.log(`[LoadGenerated] Keeping original text for object ${textObj.id}: "${textObj.text}" (No user input or index out of bounds)`);
                           }
                           textIndex++;
                           canvasObjects.push(textObj);
                           resolve();
                       } else if (objData.type === 'image' && objData.imageUrl) {
                           // Image objects: Load the NEW image URL but retain original layout properties
                           const newImgEl = new Image();
                           newImgEl.crossOrigin = 'anonymous';
                           const finalImageUrl = getProxiedImageUrlIfNeeded(newImageUrl); // Proxy the NEW URL
                           console.log(`[LoadGenerated] Attempting to load NEW image via URL: ${finalImageUrl} (Original newImageUrl was: ${newImageUrl})`); // DEBUG

                           newImgEl.onload = () => {
                               console.log(`[LoadGenerated] Successfully loaded NEW image: ${finalImageUrl}`); // DEBUG
                               // Create the canvas object using the NEW image element (newImgEl)
                               // but pass the original objData to retain layout (x, y, scale, rotation)
                               const imageObj = createImageObject(newImgEl, objData);
                               imageObj.id = objData.id ?? nextId++; // Ensure ID exists

                           // Explicitly set the imageUrl property to the new URL for serialization
                           imageObj.imageUrl = finalImageUrl;
                           // Get generationId from sessionStorage
                           const storedGenerationId = sessionStorage.getItem('generationId');
                           imageObj.generationId = storedGenerationId || null;
                           imageObj.isFromGeneration = !!storedGenerationId; // Mark as generated if we have the ID from storage

                           // The imageObj.image property already holds the loaded newImgEl,
                           // and createImageObject uses its natural dimensions with the scale from objData.

                           console.log(`[LoadGenerated] Created image object ${imageObj.id} with NEW image URL: ${finalImageUrl}, GenID: ${imageObj.generationId}, IsGen: ${imageObj.isFromGeneration}`);
                           canvasObjects.push(imageObj);
                           resolve();
                       };
                       newImgEl.onerror = (err) => {
                               console.error(`[LoadGenerated] ERROR loading NEW image for object ${objData.id}: ${finalImageUrl}. Falling back. Error:`, err); // DEBUG
                               // Fallback: Try loading the original image from the template (also proxied)
                               const originalImgEl = new Image();
                               originalImgEl.crossOrigin = 'anonymous';
                               const originalFinalUrl = getProxiedImageUrlIfNeeded(objData.imageUrl);
                               originalImgEl.onload = () => {
                                    const imageObj = createImageObject(originalImgEl, objData); // Create with original image
                                    imageObj.id = objData.id ?? nextId++;
                                    // Ensure imageUrl property reflects the actual source used
                                    imageObj.imageUrl = originalFinalUrl;
                                    console.warn(`[LoadGenerated] Falling back to original image for object ${imageObj.id}`);
                                    canvasObjects.push(imageObj);
                                    resolve();
                               };
                               originalImgEl.onerror = (origErr) => {
                                    console.error(`[LoadGenerated] Failed to load ORIGINAL image as fallback for object ${objData.id}: ${originalFinalUrl}`, origErr);
                                    resolve();
                               };
                               originalImgEl.src = originalFinalUrl; // Load original (proxied)
                           };
                           newImgEl.src = finalImageUrl; // Start loading the NEW (potentially proxied) image
                       } else {
                           console.warn('[LoadGenerated] Skipping unknown or invalid object data:', objData);
                           resolve();
                       }
                   });
               });

               await Promise.all(objectPromises);
               console.log('[LoadGenerated] Restored and modified canvas objects:', canvasObjects);

               // Initialize mesh warp handlers for text objects with mesh effect
               canvasObjects.forEach(obj => {
                   if (obj.type === 'text' && obj.effectMode === 'mesh' && obj.meshWarp) {
                       console.log('[LoadGenerated] Initializing mesh warp handler for text object:', obj.text);
                       try {
                           // Create a new mesh warp handler for this object
                           const meshHandler = new MeshWarpHandler(
                               document.getElementById('demo'),
                               obj
                           );

                           // Restore control points from saved data
                           if (obj.meshWarp.controlPoints && obj.meshWarp.controlPoints.length > 0) {
                               meshHandler.controlPoints = obj.meshWarp.controlPoints.map(p => ({ ...p }));
                               meshHandler.initialControlPoints = obj.meshWarp.initialControlPoints.map(p => ({ ...p }));
                               meshHandler.relativeControlPoints = obj.meshWarp.relativeControlPoints.map(p => ({ ...p }));
                               meshHandler.hasCustomDistortion = obj.meshWarp.hasCustomDistortion;
                               meshHandler.showGrid = obj.meshWarp.showGrid;
                               meshHandler.initialGridRect = obj.meshWarp.gridRect ? { ...obj.meshWarp.gridRect } : null;
                               console.log('[LoadGenerated] Restored mesh warp control points for:', obj.text, 'Points:', meshHandler.controlPoints.length);
                           }

                           // Store the handler reference (we'll only keep one active at a time)
                           obj._meshWarpHandler = meshHandler;
                       } catch (error) {
                           console.error('[LoadGenerated] Error initializing mesh warp handler for:', obj.text, error);
                       }
                   }
               });
           }

           // Auto-activate mesh warp handlers for immediate visual feedback
           setTimeout(() => {
               let meshTextFound = false;
               canvasObjects.forEach((obj, index) => {
                   if (obj.type === 'text' && obj.effectMode === 'mesh' && obj._meshWarpHandler && !meshTextFound) {
                       console.log('[LoadGenerated] Auto-activating mesh warp for immediate display:', obj.text);

                       // Temporarily select the object to activate mesh warp
                       const previousSelection = selectedObjectIndex;
                       selectedObjectIndex = index;
                       obj.isSelected = true;

                       // Activate the mesh warp handler
                       activeMeshWarpHandler = obj._meshWarpHandler;
                       activeMeshWarpHandler.selectedTextObject = obj;

                       // Force a redraw to show the distortion
                       update();

                       // After a brief moment, restore the previous selection state
                       setTimeout(() => {
                           if (previousSelection === -1) {
                               // No previous selection, deselect everything
                               selectedObjectIndex = -1;
                               obj.isSelected = false;
                               console.log('[LoadGenerated] Auto-deselected mesh text for clean display');
                           } else {
                               // Restore previous selection
                               selectedObjectIndex = previousSelection;
                               obj.isSelected = false;
                               if (canvasObjects[previousSelection]) {
                                   canvasObjects[previousSelection].isSelected = true;
                               }
                               console.log('[LoadGenerated] Restored previous selection:', previousSelection);
                           }

                           // Update UI to reflect the final selection state
                           updateUIFromSelectedObject();
                           update();
                       }, 100); // Very brief selection - just enough to activate mesh warp

                       meshTextFound = true; // Only auto-activate the first mesh text found
                   }
               });

               if (!meshTextFound) {
                   console.log('[LoadGenerated] No mesh warp text objects found for auto-activation');
               }
           }, 200); // Small delay to ensure everything is fully loaded

           // Sync global references to ensure window.canvasObjects points to the current array
           syncGlobalReferences();
           console.log('[LoadGenerated] Synced global references. window.canvasObjects length:', window.canvasObjects.length);

           // Re-initialize Elements accordion to restore shape click handlers
           if (typeof window.reinitializeElementsAccordion === 'function') {
               window.reinitializeElementsAccordion();
           }

           // Update UI elements to reflect restored editor state
           updateEditorUIFromState();
           updateUIFromSelectedObject();
           update();
           alert('Generated design loaded into editor!');

       } catch (error) {
           console.error('[LoadGenerated] Error loading generated design:', error);
           alert(`Error loading generated design: ${error.message}`);
           canvasObjects = [];
           selectedObjectIndex = -1;
           artboard = null;
           updateUIFromSelectedObject();
           update();
       }
   }


   // --- DOMContentLoaded Listener ---
   document.addEventListener('DOMContentLoaded', async () => { // Make async
       const params = new URLSearchParams(window.location.search);
       const templateId = params.get('templateId');
       const source = params.get('source');

       if (source === 'generation' && templateId) {
           console.log('[EditorLoad] Source is generation, attempting to load merged data.');
           const newImageUrl = sessionStorage.getItem('generatedImageUrl');
           const userTextsRaw = sessionStorage.getItem('userTexts');
           const originalTemplateId = sessionStorage.getItem('originalTemplateId'); // Should match templateId from URL

           if (newImageUrl && userTextsRaw && originalTemplateId === templateId) {
               try {
                   const userTexts = JSON.parse(userTextsRaw);
                   await loadGeneratedDesign(templateId, newImageUrl, userTexts);
                   // Clear session storage after successful load (including generationId)
                   sessionStorage.removeItem('generatedImageUrl');
                   sessionStorage.removeItem('userTexts');
                   sessionStorage.removeItem('originalTemplateId');
                   sessionStorage.removeItem('generationId'); // <-- Clear generationId
               } catch (e) {
                   console.error("Error parsing sessionStorage data or loading generated design:", e);
                   alert("Error loading generated design data. Initializing default editor.");
                   initialize(); // Fallback to default init
               }
           } else {
               console.warn("[EditorLoad] Missing data in sessionStorage for generated design. Loading original template instead.");
               // Fallback to loading the original template if session data is missing
               await loadTemplateData(templateId);
           }

       } else if (templateId) {
           console.log('[EditorLoad] Loading template directly:', templateId);
           await loadTemplateData(templateId);
       } else {
           console.log('[EditorLoad] No templateId or generation source found, initializing default state.');
           // Handle other loading methods (e.g., from admin inspiration) or initialize default
           loadImageFromUrlParam();
           loadAdminDataFromUrlParam();
           // If neither template nor generation source, initialize might be called here or within the other load functions if they don't find params
           if (!params.has('templateId') && !params.has('imageUrl') && !params.has('image')) {
                initialize(); // Ensure default state if no params load anything
           }
       }

       // Add listener for Save Template button
       const saveTemplateBtn = document.getElementById('saveTemplateBtn');
       if (saveTemplateBtn) {
           saveTemplateBtn.addEventListener('click', handleSaveTemplate);
       } else {
           console.error('Save Template button not found!');
       }

       // Initialize Pickr for background color on the dedicated div
       // Make sure DOM is fully loaded before initializing Pickr
       function initializePickr() {
           const colorPickerElement = document.getElementById('canvasBgColorPicker');
           if (!colorPickerElement) {
               console.error('Canvas background color picker element not found');
               return;
           }

           try {
               // Wait for Pickr library to be fully loaded
               if (typeof Pickr === 'undefined') {
                   console.log('Pickr not loaded yet, retrying in 200ms');
                   setTimeout(initializePickr, 200);
                   return;
               }

               const pickr = Pickr.create({
                   el: colorPickerElement,
                   theme: 'nano',
                   default: canvasBackgroundColor,
                   swatches: [
                       '#ffffff', '#f4f7fc', '#d1d5db', '#9ca3af', '#6b7280', '#4b5563', '#374151', '#1f2937', '#111827', '#000000',
                       '#ef4444', '#f97316', '#eab308', '#84cc16', '#22c55e', '#14b8a6', '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'
                   ],
                   components: {
                       preview: true,
                       opacity: true,
                       hue: true,
                       interaction: {
                           hex: true,
                           rgba: true,
                           input: true,
                           clear: false,
                           save: true
                       }
                   }
               });

               pickr.on('save', (color, instance) => {
                   const newColor = color.toHEXA().toString();
                   console.log('Pickr save event:', newColor);
                   canvasBackgroundColor = newColor; // Update the global variable
                   update(); // Redraw canvas
                   pickr.hide(); // Hide picker after saving
               });

               // Store pickr instance globally for access by updateEditorUIFromState
               window.pickrInstance = pickr;
               console.log('Pickr initialized successfully');
           } catch (error) {
               console.error('Error initializing Pickr:', error);
           }
       }

       // Start the initialization process with a delay to ensure DOM is ready
       setTimeout(initializePickr, 300);

       // Add event listeners for move buttons
       const moveForwardBtn = document.getElementById('moveForwardBtn');
       const moveBackwardBtn = document.getElementById('moveBackwardBtn');
       if (moveForwardBtn) {
           moveForwardBtn.addEventListener('click', moveObjectForward);
       }
       if (moveBackwardBtn) {
           moveBackwardBtn.addEventListener('click', moveObjectBackward);
       }

   });

   // --- Save Template Logic ---
   async function handleSaveTemplate() {
       console.log('[SaveTemplate] Clicked');
       if (!artboard) {
           alert('Cannot save template without an Artboard defined.');
           return;
       }

       // 1. Get Admin Data
       const adminData = {
           imageUrl: document.getElementById('adminImageUrl')?.value || '',
           model: document.getElementById('adminModel')?.value || '',
           prompt: document.getElementById('adminPrompt')?.value || '',
           palette: document.getElementById('adminPalette')?.value || ''
       };
       const inspirationId = document.getElementById('adminInspirationId')?.value || null;

       // 2. Generate Preview Image (similar to Add to Collection)
       const exportCanvas = document.createElement('canvas');
       // Use a smaller size for preview for efficiency, maintaining aspect ratio
       const previewWidth = 300;
       const previewHeight = artboard.height * (previewWidth / artboard.width);
       exportCanvas.width = previewWidth;
       exportCanvas.height = previewHeight;
       const exportCtx = exportCanvas.getContext('2d');

       exportCtx.save();
       // Scale the drawing to fit the preview canvas
       exportCtx.scale(previewWidth / artboard.width, previewHeight / artboard.height);
       // Translate context so drawing happens relative to artboard's top-left
       exportCtx.translate(-artboard.x, -artboard.y);

       // Draw objects onto exportCtx (passing the context)
       canvasObjects.forEach((obj) => {
           const bounds = calculateObjectBounds(obj); // Check bounds relative to original artboard
            if (bounds.x + bounds.width > artboard.x && bounds.x < artboard.x + artboard.width &&
               bounds.y + bounds.height > artboard.y && bounds.y < artboard.y + artboard.height) {
               if (obj.type === 'text') {
                   drawTextObject(obj, exportCtx);
               } else if (obj.type === 'image') {
                   drawImageObject(obj, exportCtx);
               }
           }
       });
       exportCtx.restore();

       const previewDataUrl = exportCanvas.toDataURL('image/png');

       // 3. Upload Preview Image
       let previewImageUrl = '';
       try {
           console.log('[SaveTemplate] Uploading preview image...');
           const blob = await (await fetch(previewDataUrl)).blob();
           const formData = new FormData();
           formData.append('image', blob, 'template_preview.png');

           const response = await fetch('/api/images/upload', { // Use the existing image upload endpoint
               method: 'POST',
               body: formData
           });

           if (!response.ok) {
               const errorText = await response.text();
               throw new Error(`Preview upload failed: ${response.statusText} - ${errorText}`);
           }
           const result = await response.json();
           previewImageUrl = result.imageUrl || result.url || result.fileUrl;
           if (!previewImageUrl || typeof previewImageUrl !== 'string') {
                throw new Error('Invalid or missing imageUrl in preview upload response.');
           }
           console.log('[SaveTemplate] Preview image uploaded:', previewImageUrl);

       } catch (e) {
           console.error('[SaveTemplate] Error uploading preview image:', e);
           alert(`Error uploading preview image: ${e.message}`);
           return; // Stop if preview upload fails
       }

       // 4. Prepare Canvas Objects Data (Remove non-serializable parts like the image element)
       const serializableObjects = canvasObjects.map(obj => {
           // Create a deep copy to preserve nested objects like meshWarp
           const copy = JSON.parse(JSON.stringify(obj));

           if (copy.type === 'image') {
               // Store image source URL instead of the Image object
               copy.imageUrl = obj.image ? obj.image.src : obj.imageUrl; // Use original URL if image object exists
               delete copy.image; // Remove the actual Image object
           }

           // Remove selection state from saved template
           delete copy.isSelected;

           // Sync mesh warp data from active handler before saving
           if (copy.type === 'text' && copy.effectMode === 'mesh' &&
               typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler &&
               activeMeshWarpHandler.selectedTextObject === obj) {
               console.log('[SaveTemplate] Syncing mesh warp data from active handler for:', copy.text);
               activeMeshWarpHandler.syncControlPointsToTextObject();
           }

           // Ensure mesh warp data is preserved for text objects
           if (copy.type === 'text' && obj.meshWarp) {
               copy.meshWarp = JSON.parse(JSON.stringify(obj.meshWarp));
               console.log('[SaveTemplate] Preserving mesh warp data for text object:', copy.text, copy.meshWarp);
           }

           return copy;
       });

       // 5. Prepare Complete Editor State
       const editorState = {
           canvasBackgroundColor: canvasBackgroundColor,
           zoom: {
               scale: scale,
               offsetX: offsetX,
               offsetY: offsetY
           },
           selectedObjectIndex: selectedObjectIndex,
           nextId: nextId,
           editorSettings: {
               // Add any additional editor settings that affect the design
               artboardEditMode: isArtboardEditMode,
               // Store any UI state that affects the template
               lastUpdateTimestamp: Date.now()
           }
       };

       // 6. Prepare Template Data Payload
       const templateData = {
           name: prompt('Enter a name for this template (optional):') || 'Untitled Template', // Simple prompt for name
           inspirationId: inspirationId,
           previewImageUrl: previewImageUrl,
           // Correctly assign the global artboard object
           artboard: artboard ? {
               x: artboard.x,
               y: artboard.y,
               width: artboard.width,
               height: artboard.height
           } : null, // Send null if artboard doesn't exist
           canvasObjects: serializableObjects,
           adminData: adminData,
           editorState: editorState // Include complete editor state
       };

       // Add console log to inspect the data being sent
       console.log('[SaveTemplate] Editor state being saved:', editorState);
       console.log('[SaveTemplate] Canvas objects being saved:', serializableObjects);
       console.log('[SaveTemplate] Full template data:', JSON.stringify(templateData, null, 2));

       // 7. Send Data to Backend
       try {
           // console.log('[SaveTemplate] Saving template data to /api/design-templates:', templateData); // Log above is more detailed
           const saveResponse = await fetch('/api/design-templates', { // Use the new endpoint
               method: 'POST',
               headers: {
                   'Content-Type': 'application/json'
               },
               body: JSON.stringify(templateData)
           });

           if (!saveResponse.ok) {
                const errorText = await saveResponse.text();
                throw new Error(`Save template failed: ${saveResponse.statusText} - ${errorText}`);
           }

           const savedTemplate = await saveResponse.json();
           console.log('[SaveTemplate] Template saved successfully:', savedTemplate);
           alert('Template saved successfully!');

       } catch (e) {
            console.error('[SaveTemplate] Error saving template data:', e);
            alert(`Error saving template: ${e.message}`);
       }
   }
   document.getElementById('addToCollectionBtn').addEventListener('click', async () => {
       console.log('[AddToCollection] Clicked');
       if (!artboard) {
           // Use showToast if available, otherwise alert
           if (window.showToast) window.showToast('No Artboard defined', 'error');
           else alert('No Artboard defined');
           return;
       }

       const canvasEl = document.getElementById('demo');
       const ctxMain = canvasEl.getContext('2d');

       // Create offscreen canvas for export
       const exportCanvas = document.createElement('canvas');
       exportCanvas.width = artboard.width;
       exportCanvas.height = artboard.height;
       const exportCtx = exportCanvas.getContext('2d');

       // --- Redraw objects onto export canvas (More robust than drawImage) ---
       // Clear export canvas (assuming transparent background is desired)
       exportCtx.clearRect(0, 0, exportCanvas.width, exportCanvas.height);
       // If white background needed:
       // exportCtx.fillStyle = '#FFFFFF';
       // exportCtx.fillRect(0, 0, exportCanvas.width, exportCanvas.height);

       exportCtx.save();
       // Clip to artboard dimensions on the export canvas
       exportCtx.beginPath();
       exportCtx.rect(0, 0, artboard.width, artboard.height);
       exportCtx.clip();
       // Translate context so drawing happens relative to artboard's top-left
       exportCtx.translate(-artboard.x, -artboard.y);

       // Draw objects directly onto exportCtx
       canvasObjects.forEach((obj) => {
           // Basic check if object might be within artboard bounds
           const bounds = calculateObjectBounds(obj);
           if (bounds.x + bounds.width > artboard.x && bounds.x < artboard.x + artboard.width &&
               bounds.y + bounds.height > artboard.y && bounds.y < artboard.y + artboard.height) {

               // Call drawing functions with exportCtx as the target
               if (obj.type === 'text') {
                   drawTextObject(obj, exportCtx); // Pass exportCtx
               } else if (obj.type === 'image') {
                   drawImageObject(obj, exportCtx); // Pass exportCtx
               }
           }
       });

       exportCtx.restore(); // Restore clipping and translation
       // --- End Redraw ---

       const dataUrl = exportCanvas.toDataURL('image/png');
       console.log('[AddToCollection] Exported data URL length:', dataUrl.length);

       // Find or create collection modal
       let modal = document.querySelector('collection-modal');
       if (!modal) {
           modal = document.createElement('collection-modal');
           document.body.appendChild(modal);
       }

       // Generate a prompt from text objects
       let generatedPrompt = canvasObjects
           .filter(obj => obj.type === 'text')
           .map(obj => obj.text)
           .join(' ') || "Design Editor Image"; // Default if no text

       // Show loading state
       if (window.showToast) window.showToast('Uploading design...', 'info');

       // Upload image
       console.log('[AddToCollection] Uploading image...');
       try {
           const blob = await (await fetch(dataUrl)).blob();
           const formData = new FormData();
           formData.append('image', blob, 'design.png'); // Changed filename

           const response = await fetch('/api/images/upload', {
               method: 'POST',
               // Assuming cookie/session auth middleware handles authentication
               body: formData
           });

           if (!response.ok) {
               const errorText = await response.text();
               console.error('[AddToCollection] Upload failed:', response.status, errorText);
               throw new Error(`Upload failed: ${response.statusText} - ${errorText}`);
           }

           const result = await response.json();
           console.log('[AddToCollection] Upload response:', result);

           // **Crucial Check:** Verify imageUrl exists and is a string
           const uploadedUrl = result.imageUrl || result.url || result.fileUrl;
           if (!uploadedUrl || typeof uploadedUrl !== 'string') {
                console.error('[AddToCollection] Invalid or missing imageUrl in upload response:', result);
                throw new Error('Failed to get valid image URL after upload.');
           }
           console.log('[AddToCollection] Uploaded image URL:', uploadedUrl);

           // Set modal data with the valid uploaded URL and generated prompt
           if (modal.setImageData) {
               console.log('[AddToCollection] Using setImageData() with uploaded URL:', uploadedUrl);
               modal.setImageData({
                   imageUrl: uploadedUrl,
                   prompt: generatedPrompt, // Use generated prompt
                   generationId: null // Explicitly null as it's not a generation
               });
           } else {
               // Fallback if method doesn't exist
               console.warn('[AddToCollection] setImageData method not found on modal. Setting attribute as fallback.');
               modal.setAttribute('image-url', uploadedUrl);
           }

           // Show modal only after successful upload and data setting
           if (modal.show) {
               console.log('[AddToCollection] Calling modal.show()');
               modal.show();
           } else {
               console.warn('[AddToCollection] show method not found on modal. Setting display:block as fallback.');
               modal.style.display = 'block'; // Fallback
           }

       } catch (e) {
           console.error('[AddToCollection] Error during upload or modal setup:', e);
           // Use showToast for error feedback if available
           if (window.showToast) window.showToast(`Error preparing collection add: ${e.message}`, 'error');
           else alert(`Error preparing collection add: ${e.message}`);
       }
   });

   // --- Copy/Paste Functionality ---
   let copiedElement = null;

   // Copy Element Function
   function copySelectedElement() {
       if (selectedObjectIndex === -1) {
           console.log('No element selected to copy');
           if (window.showToast) window.showToast('No element selected to copy', 'warning');
           return;
       }

       const selectedObject = canvasObjects[selectedObjectIndex];

       // Create a deep copy of the selected object
       copiedElement = JSON.parse(JSON.stringify(selectedObject));

       // Remove selection state from the copy
       copiedElement.isSelected = false;

       console.log('📋 Element copied:', copiedElement.type, copiedElement.text || copiedElement.imageUrl);

       // Enable paste button
       const pasteBtn = document.getElementById('pasteElementBtn');
       if (pasteBtn) {
           pasteBtn.disabled = false;
           pasteBtn.title = `Paste ${copiedElement.type === 'text' ? 'Text' : 'Image'}`;
       }

       if (window.showToast) {
           window.showToast(`${copiedElement.type === 'text' ? 'Text' : 'Image'} copied to clipboard`, 'success');
       }
   }

   // Paste Element Function
   function pasteElement() {
       if (!copiedElement) {
           console.log('No element in clipboard to paste');
           if (window.showToast) window.showToast('No element in clipboard to paste', 'warning');
           return;
       }

       // Create a new copy for pasting (so we can paste multiple times)
       const newElement = JSON.parse(JSON.stringify(copiedElement));

       // Generate a new unique ID
       newElement.id = Date.now() + Math.random();

       // Offset the position slightly so it doesn't overlap exactly
       newElement.x += 50;
       newElement.y += 50;

       // Ensure the element is not selected initially
       newElement.isSelected = false;

       // Handle special cases for different element types
       if (newElement.type === 'text') {
           console.log('📋 Pasting text element:', newElement.text);

           // Reset any mesh warp or grid distort states that might cause issues
           if (newElement.effectMode === 'mesh' && newElement.meshWarp) {
               // Reset mesh warp to avoid control point conflicts
               newElement.meshWarp = null;
               newElement.effectMode = 'normal';
           }

           if (newElement.effectMode === 'grid-distort' && newElement.gridDistort) {
               // Reset grid distort to avoid control point conflicts
               newElement.gridDistort = null;
               newElement.effectMode = 'normal';
           }

       } else if (newElement.type === 'image') {
           console.log('📋 Pasting image element:', newElement.imageUrl);

           // For images, we need to reload the image object
           if (newElement.imageUrl) {
               const img = new Image();
               img.onload = () => {
                   newElement.image = img;
                   newElement.originalWidth = img.width;
                   newElement.originalHeight = img.height;

                   // Add to canvas and update
                   canvasObjects.push(newElement);

                   // Select the newly pasted element
                   if (selectedObjectIndex !== -1) {
                       canvasObjects[selectedObjectIndex].isSelected = false;
                   }
                   selectedObjectIndex = canvasObjects.length - 1;
                   newElement.isSelected = true;

                   updateUIFromSelectedObject();
                   update();

                   console.log('📋 Image element pasted successfully');
               };

               img.onerror = () => {
                   console.error('Failed to load pasted image:', newElement.imageUrl);
                   if (window.showToast) window.showToast('Failed to load pasted image', 'error');
               };

               img.src = newElement.imageUrl;
               return; // Exit early for async image loading
           }
       }

       // Add the new element to the canvas
       canvasObjects.push(newElement);

       // Select the newly pasted element
       if (selectedObjectIndex !== -1) {
           canvasObjects[selectedObjectIndex].isSelected = false;
       }
       selectedObjectIndex = canvasObjects.length - 1;
       newElement.isSelected = true;

       updateUIFromSelectedObject();
       update();

       console.log('📋 Element pasted successfully');
       if (window.showToast) {
           window.showToast(`${newElement.type === 'text' ? 'Text' : 'Image'} pasted successfully`, 'success');
       }

       // Save state for undo/redo
       saveState(`Paste ${newElement.type === 'text' ? 'Text' : 'Image'}`);
   }

   // Copy/Paste Button Event Listeners
   const copyBtn = document.getElementById('copyElementBtn');
   const pasteBtn = document.getElementById('pasteElementBtn');

   if (copyBtn) {
       copyBtn.addEventListener('click', copySelectedElement);
   }

   if (pasteBtn) {
       pasteBtn.addEventListener('click', pasteElement);
       // Initially disabled until something is copied
       pasteBtn.disabled = true;
   }

   // Keyboard shortcuts for copy/paste
   document.addEventListener('keydown', (e) => {
       // Only handle shortcuts when not typing in input fields
       if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
           return;
       }

       // Ctrl+C or Cmd+C for copy
       if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
           e.preventDefault();
           copySelectedElement();
       }

       // Ctrl+V or Cmd+V for paste
       if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
           e.preventDefault();
           pasteElement();
       }
   });

   // Update copy button state when selection changes
   function updateCopyPasteButtons() {
       const copyBtn = document.getElementById('copyElementBtn');

       if (copyBtn) {
           copyBtn.disabled = selectedObjectIndex === -1;

           if (selectedObjectIndex !== -1) {
               const selectedObject = canvasObjects[selectedObjectIndex];
               copyBtn.title = `Copy ${selectedObject.type === 'text' ? 'Text' : 'Image'}`;
           } else {
               copyBtn.title = 'Copy Selected Element';
           }
       }
   }

   // Call this function whenever selection changes
   // Add this to the existing updateUIFromSelectedObject function or call it separately

   // --- Undo/Redo Functionality ---
   let historyStack = [];
   let historyIndex = -1;
   const MAX_HISTORY_SIZE = 50;

   // Save current state to history
   function saveStateToHistory(actionName = 'Action') {
       // Create a deep copy of the current canvas state, excluding circular references
       const cleanCanvasObjects = canvasObjects.map(obj => {
           const cleanObj = { ...obj };
           // Remove circular reference properties that can't be serialized
           delete cleanObj._meshWarpHandler;
           return cleanObj;
       });

       const currentState = {
           canvasObjects: JSON.parse(JSON.stringify(cleanCanvasObjects)),
           selectedObjectIndex: selectedObjectIndex,
           canvasBackgroundColor: canvasBackgroundColor,
           artboard: JSON.parse(JSON.stringify(artboard)),
           actionName: actionName,
           timestamp: Date.now()
       };

       // Remove any history after current index (when we're not at the end)
       if (historyIndex < historyStack.length - 1) {
           historyStack = historyStack.slice(0, historyIndex + 1);
       }

       // Add new state to history
       historyStack.push(currentState);

       // Limit history size
       if (historyStack.length > MAX_HISTORY_SIZE) {
           historyStack.shift();
       } else {
           historyIndex++;
       }

       console.log('📚 State saved to history:', actionName, 'Index:', historyIndex, 'Stack size:', historyStack.length);
       updateUndoRedoButtons();
   }

   // Restore state from history
   function restoreStateFromHistory(state) {
       console.log('📚 Restoring state:', state.actionName);

       // Clear current objects
       canvasObjects.length = 0;

       // Restore canvas objects
       if (state.canvasObjects && Array.isArray(state.canvasObjects)) {
           const objectPromises = state.canvasObjects.map(objData => {
               return new Promise((resolve) => {
                   if (objData.type === 'text') {
                       const textObj = createTextObject(objData);
                       // Copy all properties from saved state
                       Object.assign(textObj, objData);
                       canvasObjects.push(textObj);
                       resolve();
                   } else if (objData.type === 'image') {
                       const img = new Image();
                       img.onload = () => {
                           const imageObj = createImageObject(objData.imageUrl, objData.x, objData.y);
                           // Copy all properties from saved state
                           Object.assign(imageObj, objData);
                           imageObj.image = img;
                           canvasObjects.push(imageObj);
                           resolve();
                       };
                       img.onerror = () => {
                           console.error('Failed to restore image:', objData.imageUrl);
                           resolve();
                       };
                       img.src = objData.imageUrl;
                   } else {
                       resolve();
                   }
               });
           });

           Promise.all(objectPromises).then(() => {
               // Recreate mesh warp handlers for text objects with mesh effect
               canvasObjects.forEach(obj => {
                   if (obj.type === 'text' && obj.effectMode === 'mesh' && obj.meshWarp) {
                       console.log('[UndoRedo] Recreating mesh warp handler for text object:', obj.text);
                       try {
                           // Prepare saved data
                           let savedControlPoints = null;
                           let savedInitialControlPoints = null;
                           let savedRelativeControlPoints = null;
                           let savedHasCustomDistortion = false;
                           let savedShowGrid = true;
                           let savedGridRect = null;

                           if (obj.meshWarp.controlPoints && obj.meshWarp.controlPoints.length > 0) {
                               savedControlPoints = obj.meshWarp.controlPoints.map(p => ({ ...p }));
                               savedInitialControlPoints = obj.meshWarp.initialControlPoints.map(p => ({ ...p }));
                               savedRelativeControlPoints = obj.meshWarp.relativeControlPoints.map(p => ({ ...p }));
                               savedHasCustomDistortion = obj.meshWarp.hasCustomDistortion;
                               savedShowGrid = obj.meshWarp.showGrid;
                               savedGridRect = obj.meshWarp.gridRect ? { ...obj.meshWarp.gridRect } : null;
                           }

                           // Create mesh warp handler
                           const meshHandler = new MeshWarpHandler(
                               document.getElementById('demo'),
                               obj
                           );

                           // Restore saved data
                           if (savedControlPoints) {
                               meshHandler.controlPoints = savedControlPoints;
                               meshHandler.initialControlPoints = savedInitialControlPoints;
                               meshHandler.relativeControlPoints = savedRelativeControlPoints;
                               meshHandler.hasCustomDistortion = savedHasCustomDistortion;
                               meshHandler.showGrid = savedShowGrid;
                               meshHandler.initialGridRect = savedGridRect;
                           }

                           // Store handler reference
                           obj._meshWarpHandler = meshHandler;
                       } catch (error) {
                           console.error('[UndoRedo] Error recreating mesh warp handler for:', obj.text, error);
                       }
                   }
               });

               // Restore selection
               selectedObjectIndex = state.selectedObjectIndex;
               if (selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex]) {
                   canvasObjects[selectedObjectIndex].isSelected = true;

                   // Activate mesh warp handler if the selected object has mesh effect
                   const selectedObject = canvasObjects[selectedObjectIndex];
                   if (selectedObject.type === 'text' && selectedObject.effectMode === 'mesh' && selectedObject._meshWarpHandler) {
                       console.log('[UndoRedo] Activating mesh warp handler for selected object:', selectedObject.text);
                       activeMeshWarpHandler = selectedObject._meshWarpHandler;
                       activeMeshWarpHandler.selectedTextObject = selectedObject;
                   }
               }

               // Restore background color
               canvasBackgroundColor = state.canvasBackgroundColor || '#ffffff';

               // Restore artboard
               if (state.artboard) {
                   Object.assign(artboard, state.artboard);
               }

               // Update UI and redraw
               updateUIFromSelectedObject();
               update();
               console.log('📚 State restored successfully');
           });
       } else {
           // No objects to restore
           selectedObjectIndex = -1;
           canvasBackgroundColor = state.canvasBackgroundColor || '#ffffff';
           if (state.artboard) {
               Object.assign(artboard, state.artboard);
           }
           updateUIFromSelectedObject();
           update();
       }
   }

   // Undo function
   function performUndo() {
       if (historyIndex > 0) {
           historyIndex--;
           const previousState = historyStack[historyIndex];
           console.log('⏪ Undoing to:', previousState.actionName);
           restoreStateFromHistory(previousState);
           updateUndoRedoButtons();

           if (window.showToast) {
               window.showToast(`Undone: ${previousState.actionName}`, 'info');
           }
       } else {
           console.log('⏪ Nothing to undo');
           if (window.showToast) {
               window.showToast('Nothing to undo', 'warning');
           }
       }
   }

   // Redo function
   function performRedo() {
       if (historyIndex < historyStack.length - 1) {
           historyIndex++;
           const nextState = historyStack[historyIndex];
           console.log('⏩ Redoing to:', nextState.actionName);
           restoreStateFromHistory(nextState);
           updateUndoRedoButtons();

           if (window.showToast) {
               window.showToast(`Redone: ${nextState.actionName}`, 'info');
           }
       } else {
           console.log('⏩ Nothing to redo');
           if (window.showToast) {
               window.showToast('Nothing to redo', 'warning');
           }
       }
   }

   // Update undo/redo button states
   function updateUndoRedoButtons() {
       const undoBtn = document.getElementById('undoBtn');
       const redoBtn = document.getElementById('redoBtn');

       if (undoBtn) {
           undoBtn.disabled = historyIndex <= 0;
           if (historyIndex > 0) {
               undoBtn.title = `Undo: ${historyStack[historyIndex].actionName}`;
           } else {
               undoBtn.title = 'Undo';
           }
       }

       if (redoBtn) {
           redoBtn.disabled = historyIndex >= historyStack.length - 1;
           if (historyIndex < historyStack.length - 1) {
               redoBtn.title = `Redo: ${historyStack[historyIndex + 1].actionName}`;
           } else {
               redoBtn.title = 'Redo';
           }
       }
   }

   // Undo/Redo Button Event Listeners
   const undoBtn = document.getElementById('undoBtn');
   const redoBtn = document.getElementById('redoBtn');

   if (undoBtn) {
       undoBtn.addEventListener('click', performUndo);
   }

   if (redoBtn) {
       redoBtn.addEventListener('click', performRedo);
   }

   // Keyboard shortcuts for undo/redo
   document.addEventListener('keydown', (e) => {
       // Only handle shortcuts when not typing in input fields
       if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
           return;
       }

       // Ctrl+Z or Cmd+Z for undo
       if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
           e.preventDefault();
           performUndo();
       }

       // Ctrl+Y or Cmd+Y or Ctrl+Shift+Z for redo
       if (((e.ctrlKey || e.metaKey) && e.key === 'y') ||
           ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'z')) {
           e.preventDefault();
           performRedo();
       }
   });

   // Save initial state
   setTimeout(() => {
       saveStateToHistory('Initial State');
   }, 100);

   // Helper function to save state with action name
   function saveState(actionName) {
       saveStateToHistory(actionName);
   }