/**
 * Font Variant Detection System
 *
 * Simple font variant system using predefined font map
 */

class FontVariantDetector {
    constructor() {
        this.initialized = false;

        // FIXED: Use predefined font map instead of complex scanning
        this.fontMap = {
            'Arial': {
                regular: true,
                bold: true,
                italic: true,
                boldItalic: true
            },
            'Verdana': {
                regular: true,
                bold: true,
                italic: true,
                boldItalic: true
            },
            'Georgia': {
                regular: true,
                bold: true,
                italic: true,
                boldItalic: true
            },
            'Times New Roman': {
                regular: true,
                bold: true,
                italic: true,
                boldItalic: true
            },
            'Courier New': {
                regular: true,
                bold: true,
                italic: true,
                boldItalic: true
            },
            'Trebuchet MS': {
                regular: true,
                bold: true,
                italic: true,
                boldItalic: true
            },
            'Impact': {
                regular: true,
                bold: false,
                italic: false,
                boldItalic: false
            },
            'Comic Sans MS': {
                regular: true,
                bold: true,
                italic: false,
                boldItalic: false
            },
            'Poppins': {
                regular: true,
                bold: false,
                italic: false,
                boldItalic: false
            },
            'Tahoma': {
                regular: true,
                bold: true,
                italic: false,
                boldItalic: false
            }
        };
    }

    /**
     * Initialize the font variant detection system
     */
    async initialize() {
        if (this.initialized) return;

        console.log('🔤 FontVariantDetector: Initializing simple font variant detection...');

        try {
            // FIXED: Simple initialization without complex scanning or font loading
            this.setupUIControls();
            this.initialized = true;

            console.log('🔤 FontVariantDetector: Initialization complete');
            console.log('🔤 FontVariantDetector: Available fonts:', Object.keys(this.fontMap));

            // Test a few fonts
            console.log('🔤 FontVariantDetector: Arial Bold available:', this.isVariantAvailable('Arial', 'bold'));
            console.log('🔤 FontVariantDetector: Times New Roman Bold available:', this.isVariantAvailable('Times New Roman', 'bold'));
            console.log('🔤 FontVariantDetector: Impact Bold available:', this.isVariantAvailable('Impact', 'bold'));
            console.log('🔤 FontVariantDetector: Poppins Bold available:', this.isVariantAvailable('Poppins', 'bold'));
        } catch (error) {
            console.error('🔤 FontVariantDetector: Initialization failed:', error);
        }
    }

    /**
     * Check if a specific font variant is available
     */
    isVariantAvailable(fontFamily, variant) {
        const fontData = this.fontMap[fontFamily];
        return fontData ? fontData[variant] : false;
    }

    /**
     * Get all available variants for a font family
     */
    getAvailableVariants(fontFamily) {
        const fontData = this.fontMap[fontFamily];
        if (!fontData) return [];

        const variants = [];
        if (fontData.regular) variants.push('regular');
        if (fontData.bold) variants.push('bold');
        if (fontData.italic) variants.push('italic');
        if (fontData.boldItalic) variants.push('boldItalic');

        return variants;
    }



    /**
     * Setup UI controls based on available font variants
     */
    setupUIControls() {
        console.log('🔤 FontVariantDetector: Setting up UI controls');

        // Get font family selector
        const fontFamilySelect = document.getElementById('iFontFamily');
        if (!fontFamilySelect) {
            console.warn('🔤 FontVariantDetector: Font family selector not found');
            return;
        }

        // Listen for font family changes
        fontFamilySelect.addEventListener('change', (e) => {
            this.updateVariantControls(e.target.value);
        });

        // FIXED: Don't call updateVariantControls during initialization to avoid "No object selected" errors
        console.log('🔤 FontVariantDetector: UI controls setup complete (will update when font family changes)');
    }

    /**
     * Update bold/italic controls based on selected font family
     */
    updateVariantControls(fontFamily) {
        console.log('🔤 FontVariantDetector: Updating variant controls for:', fontFamily);

        const boldCheckbox = document.getElementById('iBold');
        const italicCheckbox = document.getElementById('iItalic');

        if (!boldCheckbox || !italicCheckbox) {
            console.warn('🔤 FontVariantDetector: Bold/Italic controls not found');
            return;
        }

        const availableVariants = this.getAvailableVariants(fontFamily);

        // Enable/disable bold checkbox
        const boldAvailable = availableVariants.includes('bold') || availableVariants.includes('boldItalic');

        // FIXED: Properly disable/enable controls without triggering change events during initialization
        if (boldAvailable) {
            boldCheckbox.disabled = false;
            boldCheckbox.style.opacity = '1';
            boldCheckbox.style.cursor = 'pointer';
        } else {
            boldCheckbox.disabled = true;

            // FIXED: Only clear checked state and trigger events if there's a selected object
            if (typeof selectedObjectIndex !== 'undefined' && selectedObjectIndex !== -1) {
                boldCheckbox.checked = false;
                // Trigger change event to update the text object
                boldCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
            }

            boldCheckbox.style.opacity = '0.5';
            boldCheckbox.style.cursor = 'not-allowed';
        }

        // Enable/disable italic checkbox
        const italicAvailable = availableVariants.includes('italic') || availableVariants.includes('boldItalic');

        if (italicAvailable) {
            italicCheckbox.disabled = false;
            italicCheckbox.style.opacity = '1';
            italicCheckbox.style.cursor = 'pointer';
        } else {
            italicCheckbox.disabled = true;

            // FIXED: Only clear checked state and trigger events if there's a selected object
            if (typeof selectedObjectIndex !== 'undefined' && selectedObjectIndex !== -1) {
                italicCheckbox.checked = false;
                // Trigger change event to update the text object
                italicCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
            }

            italicCheckbox.style.opacity = '0.5';
            italicCheckbox.style.cursor = 'not-allowed';
        }

        // Add visual feedback for disabled controls
        this.updateControlVisualFeedback(boldCheckbox, boldAvailable);
        this.updateControlVisualFeedback(italicCheckbox, italicAvailable);

        console.log('🔤 FontVariantDetector: Font:', fontFamily);
        console.log('🔤 FontVariantDetector: Available variants:', availableVariants);
        console.log('🔤 FontVariantDetector: Bold available:', boldAvailable, 'Italic available:', italicAvailable);
        console.log('🔤 FontVariantDetector: Bold disabled:', boldCheckbox.disabled, 'Italic disabled:', italicCheckbox.disabled);
    }

    /**
     * Update visual feedback for disabled controls
     */
    updateControlVisualFeedback(control, available) {
        const label = control.closest('label') || control.parentElement;
        if (label) {
            if (available) {
                label.style.opacity = '1';
                label.style.color = '';
                label.title = '';
            } else {
                label.style.opacity = '0.5';
                label.style.color = '#999';
                label.title = 'This variant is not available for the selected font';
            }
        }
    }


}

// Create global instance
window.fontVariantDetector = new FontVariantDetector();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FontVariantDetector;
}
