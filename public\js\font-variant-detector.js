/**
 * Font Variant Detection System
 *
 * Simple font variant system using predefined font map
 */

class FontVariantDetector {
    constructor() {
        this.initialized = false;

        // Font cache for loaded font faces
        this.fontCache = new Map();

        // FIXED: Simple font map - just check if bold/italic variants exist
        // Based on the actual font files we have in /public/fonts/
        this.fontMap = {
            'Arial': {
                regular: true,
                bold: true,      // We have Arial Bold.ttf
                italic: true,    // We have Arial Italic.ttf
                boldItalic: true // We have Arial Bold Italic.ttf
            },
            'Verdana': {
                regular: true,
                bold: true,      // We have Verdana Bold.ttf
                italic: true,    // We have Verdana Italic.ttf
                boldItalic: true // We have Verdana Bold Italic.ttf
            },
            'Georgia': {
                regular: true,
                bold: true,      // We have Georgia Bold.ttf
                italic: true,    // We have Georgia Italic.ttf
                boldItalic: true // We have Georgia Bold Italic.ttf
            },
            'Times New Roman': {
                regular: true,
                bold: true,      // We have Times New Roman Bold.ttf
                italic: true,    // We have Times New Roman Italic.ttf
                boldItalic: true // We have Times New Roman Bold Italic.ttf
            },
            'Courier New': {
                regular: true,
                bold: true,      // We have Courier New Bold.ttf
                italic: true,    // We have Courier New Italic.ttf
                boldItalic: true // We have Courier New Bold Italic.ttf
            },
            'Trebuchet MS': {
                regular: true,
                bold: true,      // We have Trebuchet MS Bold.ttf
                italic: true,    // We have Trebuchet MS Italic.ttf
                boldItalic: true // We have Trebuchet MS Bold Italic.ttf
            },
            'Impact': {
                regular: true,
                bold: false,     // No Impact Bold.ttf
                italic: false,   // No Impact Italic.ttf
                boldItalic: false
            },
            'Comic Sans MS': {
                regular: true,
                bold: true,      // We have Comic Sans MS Bold.ttf
                italic: false,   // No Comic Sans MS Italic.ttf
                boldItalic: false
            },
            'Poppins': {
                regular: true,
                bold: false,     // No Poppins Bold.ttf (only Poppins-Regular.ttf)
                italic: false,   // No Poppins Italic.ttf
                boldItalic: false
            },
            'Tahoma': {
                regular: true,
                bold: true,      // We have Tahoma Bold.ttf
                italic: false,   // No Tahoma Italic.ttf
                boldItalic: false
            }
        };
    }

    /**
     * Initialize the font variant detection system
     */
    async initialize() {
        if (this.initialized) return;

        console.log('🔤 FontVariantDetector: Initializing simple font variant detection...');

        try {
            // FIXED: Simple initialization with pre-loading of common variants
            this.setupUIControls();

            // Pre-load common font variants for immediate availability
            await this.preloadCommonVariants();

            this.initialized = true;

            console.log('🔤 FontVariantDetector: Initialization complete');
            console.log('🔤 FontVariantDetector: Available fonts:', Object.keys(this.fontMap));
            console.log('🔤 FontVariantDetector: Cached variants:', this.fontCache.size);

            // Test a few fonts
            console.log('🔤 FontVariantDetector: Arial Bold available:', this.isVariantAvailable('Arial', 'bold'));
            console.log('🔤 FontVariantDetector: Times New Roman Bold available:', this.isVariantAvailable('Times New Roman', 'bold'));
            console.log('🔤 FontVariantDetector: Impact Bold available:', this.isVariantAvailable('Impact', 'bold'));
            console.log('🔤 FontVariantDetector: Poppins Bold available:', this.isVariantAvailable('Poppins', 'bold'));
        } catch (error) {
            console.error('🔤 FontVariantDetector: Initialization failed:', error);
        }
    }

    /**
     * Pre-load common font variants for immediate availability
     */
    async preloadCommonVariants() {
        console.log('🔤 FontVariantDetector: Pre-loading common font variants...');

        // Common fonts to pre-load
        const commonFonts = ['Arial', 'Times New Roman', 'Verdana', 'Georgia'];
        const variants = [
            { bold: false, italic: false }, // regular
            { bold: true, italic: false },  // bold
            { bold: false, italic: true },  // italic
            { bold: true, italic: true }    // boldItalic
        ];

        const loadPromises = [];

        for (const fontFamily of commonFonts) {
            for (const variant of variants) {
                if (this.isVariantAvailable(fontFamily, this.getVariantName(variant.bold, variant.italic))) {
                    // Add to load queue (don't await here to load in parallel)
                    loadPromises.push(
                        this.loadFontVariant(fontFamily, variant.bold, variant.italic).catch(err => {
                            console.warn(`🔤 Failed to pre-load ${fontFamily} ${this.getVariantName(variant.bold, variant.italic)}:`, err.message);
                        })
                    );
                }
            }
        }

        // Wait for all fonts to load (or fail)
        const results = await Promise.allSettled(loadPromises);
        const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
        console.log(`🔤 FontVariantDetector: Pre-loading complete. ${successful}/${loadPromises.length} variants loaded successfully.`);
    }

    /**
     * Helper method to get variant name
     */
    getVariantName(bold, italic) {
        if (bold && italic) return 'boldItalic';
        if (bold) return 'bold';
        if (italic) return 'italic';
        return 'regular';
    }

    /**
     * Check if a specific font variant is available
     */
    isVariantAvailable(fontFamily, variant) {
        const fontData = this.fontMap[fontFamily];
        return fontData && fontData[variant] ? fontData[variant].available : false;
    }

    /**
     * Get all available variants for a font family
     */
    getAvailableVariants(fontFamily) {
        const fontData = this.fontMap[fontFamily];
        if (!fontData) return [];

        const variants = [];
        if (fontData.regular && fontData.regular.available) variants.push('regular');
        if (fontData.bold && fontData.bold.available) variants.push('bold');
        if (fontData.italic && fontData.italic.available) variants.push('italic');
        if (fontData.boldItalic && fontData.boldItalic.available) variants.push('boldItalic');

        return variants;
    }

    /**
     * Load a specific font variant
     */
    async loadFontVariant(fontFamily, bold = false, italic = false) {
        let variant = 'regular';
        if (bold && italic) variant = 'boldItalic';
        else if (bold) variant = 'bold';
        else if (italic) variant = 'italic';

        const fontData = this.fontMap[fontFamily];
        if (!fontData || !fontData[variant] || !fontData[variant].available) {
            console.warn(`🔤 FontVariantDetector: Font variant not available: ${fontFamily} ${variant}`);
            return null;
        }

        const fontPath = fontData[variant].path;
        const cacheKey = `${fontFamily}-${variant}`;

        // Check cache first
        if (this.fontCache.has(cacheKey)) {
            const cached = this.fontCache.get(cacheKey);
            console.log(`🔤 Using cached font: ${cached.uniqueFontFamily}`);
            return cached;
        }

        try {
            // Create unique font family name to avoid conflicts
            const uniqueFontFamily = variant === 'regular' ? fontFamily : `${fontFamily}-${variant}`;

            // Properly encode the font path for URLs with spaces
            const encodedFontPath = encodeURI(fontPath);

            console.log(`🔤 Loading font: ${uniqueFontFamily} from ${encodedFontPath}`);

            // Load the font using CSS Font Loading API
            const fontFace = new FontFace(uniqueFontFamily, `url("${encodedFontPath}")`);
            await fontFace.load();
            document.fonts.add(fontFace);

            const result = { fontFace, uniqueFontFamily, originalFamily: fontFamily, variant };
            this.fontCache.set(cacheKey, result);

            console.log(`🔤 Successfully loaded font: ${uniqueFontFamily}`);
            return result;
        } catch (error) {
            console.error(`🔤 Failed to load font variant: ${fontFamily} ${variant}`, error);
            return null;
        }
    }

    /**
     * Get the loaded font family name for a specific variant
     */
    getLoadedFontFamily(fontFamily, bold = false, italic = false) {
        let variant = 'regular';
        if (bold && italic) variant = 'boldItalic';
        else if (bold) variant = 'bold';
        else if (italic) variant = 'italic';

        const cacheKey = `${fontFamily}-${variant}`;
        const cached = this.fontCache.get(cacheKey);

        if (cached) {
            return cached.uniqueFontFamily;
        }

        // If not cached, return original family name
        return fontFamily;
    }



    /**
     * Setup UI controls based on available font variants
     */
    setupUIControls() {
        console.log('🔤 FontVariantDetector: Setting up UI controls');

        // Get font family selector
        const fontFamilySelect = document.getElementById('iFontFamily');
        if (!fontFamilySelect) {
            console.warn('🔤 FontVariantDetector: Font family selector not found');
            return;
        }

        // Listen for font family changes
        fontFamilySelect.addEventListener('change', (e) => {
            this.updateVariantControls(e.target.value);
        });

        // FIXED: Don't call updateVariantControls during initialization to avoid "No object selected" errors
        console.log('🔤 FontVariantDetector: UI controls setup complete (will update when font family changes)');
    }

    /**
     * Update bold/italic controls based on selected font family
     */
    updateVariantControls(fontFamily) {
        console.log('🔤 FontVariantDetector: Updating variant controls for:', fontFamily);

        const boldCheckbox = document.getElementById('iBold');
        const italicCheckbox = document.getElementById('iItalic');

        if (!boldCheckbox || !italicCheckbox) {
            console.warn('🔤 FontVariantDetector: Bold/Italic controls not found');
            return;
        }

        const availableVariants = this.getAvailableVariants(fontFamily);

        // Enable/disable bold checkbox
        const boldAvailable = availableVariants.includes('bold') || availableVariants.includes('boldItalic');

        // FIXED: Properly disable/enable controls without triggering change events during initialization
        if (boldAvailable) {
            boldCheckbox.disabled = false;
            boldCheckbox.style.opacity = '1';
            boldCheckbox.style.cursor = 'pointer';
        } else {
            boldCheckbox.disabled = true;

            // FIXED: Only clear checked state and trigger events if there's a selected object
            if (typeof selectedObjectIndex !== 'undefined' && selectedObjectIndex !== -1) {
                boldCheckbox.checked = false;
                // Trigger change event to update the text object
                boldCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
            }

            boldCheckbox.style.opacity = '0.5';
            boldCheckbox.style.cursor = 'not-allowed';
        }

        // Enable/disable italic checkbox
        const italicAvailable = availableVariants.includes('italic') || availableVariants.includes('boldItalic');

        if (italicAvailable) {
            italicCheckbox.disabled = false;
            italicCheckbox.style.opacity = '1';
            italicCheckbox.style.cursor = 'pointer';
        } else {
            italicCheckbox.disabled = true;

            // FIXED: Only clear checked state and trigger events if there's a selected object
            if (typeof selectedObjectIndex !== 'undefined' && selectedObjectIndex !== -1) {
                italicCheckbox.checked = false;
                // Trigger change event to update the text object
                italicCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
            }

            italicCheckbox.style.opacity = '0.5';
            italicCheckbox.style.cursor = 'not-allowed';
        }

        // Add visual feedback for disabled controls
        this.updateControlVisualFeedback(boldCheckbox, boldAvailable);
        this.updateControlVisualFeedback(italicCheckbox, italicAvailable);

        console.log('🔤 FontVariantDetector: Font:', fontFamily);
        console.log('🔤 FontVariantDetector: Available variants:', availableVariants);
        console.log('🔤 FontVariantDetector: Bold available:', boldAvailable, 'Italic available:', italicAvailable);
        console.log('🔤 FontVariantDetector: Bold disabled:', boldCheckbox.disabled, 'Italic disabled:', italicCheckbox.disabled);
    }

    /**
     * Update visual feedback for disabled controls
     */
    updateControlVisualFeedback(control, available) {
        const label = control.closest('label') || control.parentElement;
        if (label) {
            if (available) {
                label.style.opacity = '1';
                label.style.color = '';
                label.title = '';
            } else {
                label.style.opacity = '0.5';
                label.style.color = '#999';
                label.title = 'This variant is not available for the selected font';
            }
        }
    }


}

// Create global instance
window.fontVariantDetector = new FontVariantDetector();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FontVariantDetector;
}
