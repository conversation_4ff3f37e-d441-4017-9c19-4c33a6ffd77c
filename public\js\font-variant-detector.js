/**
 * Font Variant Detection System
 *
 * Detects available font variants (Bold, Italic, Bold Italic) in the public/fonts/ directory
 * and manages UI controls based on availability.
 */

class FontVariantDetector {
    constructor() {
        this.fontDirectory = '/public/fonts/';
        this.availableFonts = new Map(); // Map of font family -> available variants
        this.fontCache = new Map(); // Cache for loaded font files
        this.initialized = false;

        // Font variant patterns based on actual font files in the directory
        this.variantPatterns = {
            bold: [' Bold', '-Bold', '_Bold', 'Bold', '-Regular-Bold'],
            italic: [' Italic', '-Italic', '_Italic', 'Italic', '-Regular-Italic'],
            boldItalic: [
                ' Bold Italic', '-Bold-Italic', '_Bold_Italic', 'BoldItalic', ' BoldItalic',
                '-BoldItalic', '_BoldItalic', '-Bold Italic'
            ]
        };
    }

    /**
     * Initialize the font variant detection system
     */
    async initialize() {
        if (this.initialized) return;

        console.log('🔤 FontVariantDetector: Initializing font variant detection...');

        try {
            await this.scanFontDirectory();
            this.setupUIControls();
            this.initialized = true;
            console.log('🔤 FontVariantDetector: Initialization complete');
            console.log('🔤 FontVariantDetector: Available fonts:', this.availableFonts);

            // Test a few fonts
            console.log('🔤 FontVariantDetector: Arial Bold available:', this.isVariantAvailable('Arial', 'bold'));
            console.log('🔤 FontVariantDetector: Times New Roman Bold available:', this.isVariantAvailable('Times New Roman', 'bold'));
            console.log('🔤 FontVariantDetector: Verdana Italic available:', this.isVariantAvailable('Verdana', 'italic'));
        } catch (error) {
            console.error('🔤 FontVariantDetector: Initialization failed:', error);
        }
    }

    /**
     * Scan the fonts directory for available font files
     */
    async scanFontDirectory() {
        try {
            // Try static JSON file first (works with any hosting)
            let response = await fetch('/api/fonts/list.json');
            if (!response.ok) {
                // Try PHP endpoint
                response = await fetch('/api/fonts/list.php');
                if (!response.ok) {
                    // Try Node.js endpoint
                    response = await fetch('/api/fonts/list');
                    if (!response.ok) {
                        throw new Error(`Failed to fetch font list: ${response.status}`);
                    }
                }
            }

            const fontFiles = await response.json();
            console.log('🔤 FontVariantDetector: Found font files via API:', fontFiles);

            this.processFontFiles(fontFiles);
        } catch (error) {
            console.warn('🔤 FontVariantDetector: API not available, using fallback method:', error.message);
            await this.scanFontDirectoryFallback();
        }
    }

    /**
     * Fallback method to detect fonts by attempting to load common font files
     */
    async scanFontDirectoryFallback() {
        // Font families based on actual files in the fonts directory
        const commonFonts = [
            'Arial', 'Times New Roman', 'Verdana', 'Georgia', 'Trebuchet MS',
            'Comic Sans MS', 'Courier New', 'Tahoma', 'Impact',
            'Roboto', 'Raleway', 'Almendra', 'CaviarDreams', 'Caviar Dreams',
            'BebasNeue', 'FredokaOne', 'Poppins', 'LuckiestGuy'
        ];

        const fontFiles = [];

        for (const fontFamily of commonFonts) {
            // Check for regular font
            fontFiles.push(`${fontFamily}.ttf`);
            fontFiles.push(`${fontFamily}.otf`);
            fontFiles.push(`${fontFamily}.woff`);
            fontFiles.push(`${fontFamily}.woff2`);

            // Check for variants
            for (const [variantType, patterns] of Object.entries(this.variantPatterns)) {
                for (const pattern of patterns) {
                    fontFiles.push(`${fontFamily}${pattern}.ttf`);
                    fontFiles.push(`${fontFamily}${pattern}.otf`);
                    fontFiles.push(`${fontFamily}${pattern}.woff`);
                    fontFiles.push(`${fontFamily}${pattern}.woff2`);
                }
            }
        }

        // Test which files actually exist
        const existingFiles = await this.testFontFiles(fontFiles);
        this.processFontFiles(existingFiles);
    }

    /**
     * Test which font files actually exist
     */
    async testFontFiles(fontFiles) {
        const existingFiles = [];
        const batchSize = 10; // Test files in batches to avoid overwhelming the server

        for (let i = 0; i < fontFiles.length; i += batchSize) {
            const batch = fontFiles.slice(i, i + batchSize);
            const batchPromises = batch.map(async (file) => {
                try {
                    const response = await fetch(`${this.fontDirectory}${file}`, { method: 'HEAD' });
                    if (response.ok) {
                        return file;
                    }
                } catch (error) {
                    // File doesn't exist or can't be accessed
                }
                return null;
            });

            const batchResults = await Promise.all(batchPromises);
            existingFiles.push(...batchResults.filter(file => file !== null));

            // Small delay between batches to be nice to the server
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        return existingFiles;
    }

    /**
     * Process the list of font files and organize by family and variant
     */
    processFontFiles(fontFiles) {
        console.log('🔤 FontVariantDetector: Processing font files:', fontFiles);

        for (const file of fontFiles) {
            const fontInfo = this.parseFontFileName(file);
            if (fontInfo) {
                if (!this.availableFonts.has(fontInfo.family)) {
                    this.availableFonts.set(fontInfo.family, {
                        regular: false,
                        bold: false,
                        italic: false,
                        boldItalic: false,
                        files: {}
                    });
                }

                const fontData = this.availableFonts.get(fontInfo.family);
                fontData[fontInfo.variant] = true;
                fontData.files[fontInfo.variant] = file;
            }
        }

        console.log('🔤 FontVariantDetector: Processed fonts:', this.availableFonts);
    }

    /**
     * Parse a font file name to extract family and variant information
     */
    parseFontFileName(fileName) {
        // Remove file extension
        const nameWithoutExt = fileName.replace(/\.(ttf|otf|woff2?|eot)$/i, '');

        // Special handling for specific font naming patterns in our directory

        // Handle Roboto variants (Roboto-Bold.ttf, Roboto-BoldItalic.ttf, etc.)
        if (nameWithoutExt.startsWith('Roboto-')) {
            const variant = nameWithoutExt.replace('Roboto-', '');
            if (variant === 'BoldItalic') return { family: 'Roboto', variant: 'boldItalic', file: fileName };
            if (variant === 'Bold') return { family: 'Roboto', variant: 'bold', file: fileName };
            if (variant === 'Italic') return { family: 'Roboto', variant: 'italic', file: fileName };
            if (variant === 'Regular') return { family: 'Roboto', variant: 'regular', file: fileName };
        }

        // Handle Raleway variants (Raleway-Bold.ttf, Raleway-Bold-Italic.ttf, etc.)
        if (nameWithoutExt.startsWith('Raleway-')) {
            const variant = nameWithoutExt.replace('Raleway-', '');
            if (variant === 'Bold-Italic') return { family: 'Raleway', variant: 'boldItalic', file: fileName };
            if (variant === 'Bold') return { family: 'Raleway', variant: 'bold', file: fileName };
            if (variant === 'Regular-Italic') return { family: 'Raleway', variant: 'italic', file: fileName };
            if (variant === 'Regular') return { family: 'Raleway', variant: 'regular', file: fileName };
        }

        // Handle Almendra variants (Almendra-Bold.otf, Almendra-BoldItalic.otf, etc.)
        if (nameWithoutExt.startsWith('Almendra-')) {
            const variant = nameWithoutExt.replace('Almendra-', '');
            if (variant === 'BoldItalic') return { family: 'Almendra', variant: 'boldItalic', file: fileName };
            if (variant === 'Bold') return { family: 'Almendra', variant: 'bold', file: fileName };
            if (variant === 'Italic') return { family: 'Almendra', variant: 'italic', file: fileName };
            if (variant === 'Regular') return { family: 'Almendra', variant: 'regular', file: fileName };
        }

        // Check for bold italic first (most specific)
        for (const pattern of this.variantPatterns.boldItalic) {
            if (nameWithoutExt.includes(pattern)) {
                return {
                    family: nameWithoutExt.replace(pattern, '').trim(),
                    variant: 'boldItalic',
                    file: fileName
                };
            }
        }

        // Check for bold
        for (const pattern of this.variantPatterns.bold) {
            if (nameWithoutExt.includes(pattern)) {
                return {
                    family: nameWithoutExt.replace(pattern, '').trim(),
                    variant: 'bold',
                    file: fileName
                };
            }
        }

        // Check for italic
        for (const pattern of this.variantPatterns.italic) {
            if (nameWithoutExt.includes(pattern)) {
                return {
                    family: nameWithoutExt.replace(pattern, '').trim(),
                    variant: 'italic',
                    file: fileName
                };
            }
        }

        // Default to regular
        return {
            family: nameWithoutExt,
            variant: 'regular',
            file: fileName
        };
    }

    /**
     * Check if a specific font variant is available
     */
    isVariantAvailable(fontFamily, variant) {
        const fontData = this.availableFonts.get(fontFamily);
        return fontData ? fontData[variant] : false;
    }

    /**
     * Get the font file path for a specific family and variant
     */
    getFontFile(fontFamily, variant = 'regular') {
        const fontData = this.availableFonts.get(fontFamily);
        if (fontData && fontData.files[variant]) {
            return `${this.fontDirectory}${fontData.files[variant]}`;
        }
        return null;
    }

    /**
     * Get all available variants for a font family
     */
    getAvailableVariants(fontFamily) {
        const fontData = this.availableFonts.get(fontFamily);
        if (!fontData) return [];

        const variants = [];
        if (fontData.regular) variants.push('regular');
        if (fontData.bold) variants.push('bold');
        if (fontData.italic) variants.push('italic');
        if (fontData.boldItalic) variants.push('boldItalic');

        return variants;
    }

    /**
     * Setup UI controls based on available font variants
     */
    setupUIControls() {
        console.log('🔤 FontVariantDetector: Setting up UI controls');

        // Get font family selector
        const fontFamilySelect = document.getElementById('iFontFamily');
        if (!fontFamilySelect) {
            console.warn('🔤 FontVariantDetector: Font family selector not found');
            return;
        }

        // Listen for font family changes
        fontFamilySelect.addEventListener('change', (e) => {
            this.updateVariantControls(e.target.value);
        });

        // Initial setup for current font
        this.updateVariantControls(fontFamilySelect.value);
    }

    /**
     * Update bold/italic controls based on selected font family
     */
    updateVariantControls(fontFamily) {
        console.log('🔤 FontVariantDetector: Updating variant controls for:', fontFamily);

        const boldCheckbox = document.getElementById('iBold');
        const italicCheckbox = document.getElementById('iItalic');

        if (!boldCheckbox || !italicCheckbox) {
            console.warn('🔤 FontVariantDetector: Bold/Italic controls not found');
            return;
        }

        const availableVariants = this.getAvailableVariants(fontFamily);

        // Enable/disable bold checkbox
        const boldAvailable = availableVariants.includes('bold') || availableVariants.includes('boldItalic');

        // FIXED: Properly disable/enable controls and clear checked state
        if (boldAvailable) {
            boldCheckbox.disabled = false;
            boldCheckbox.style.opacity = '1';
            boldCheckbox.style.cursor = 'pointer';
        } else {
            boldCheckbox.disabled = true;
            boldCheckbox.checked = false; // Clear checked state when disabled
            boldCheckbox.style.opacity = '0.5';
            boldCheckbox.style.cursor = 'not-allowed';
            // Trigger change event to update the text object
            boldCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // Enable/disable italic checkbox
        const italicAvailable = availableVariants.includes('italic') || availableVariants.includes('boldItalic');

        if (italicAvailable) {
            italicCheckbox.disabled = false;
            italicCheckbox.style.opacity = '1';
            italicCheckbox.style.cursor = 'pointer';
        } else {
            italicCheckbox.disabled = true;
            italicCheckbox.checked = false; // Clear checked state when disabled
            italicCheckbox.style.opacity = '0.5';
            italicCheckbox.style.cursor = 'not-allowed';
            // Trigger change event to update the text object
            italicCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // Add visual feedback for disabled controls
        this.updateControlVisualFeedback(boldCheckbox, boldAvailable);
        this.updateControlVisualFeedback(italicCheckbox, italicAvailable);

        console.log('🔤 FontVariantDetector: Font:', fontFamily);
        console.log('🔤 FontVariantDetector: Available variants:', availableVariants);
        console.log('🔤 FontVariantDetector: Bold available:', boldAvailable, 'Italic available:', italicAvailable);
        console.log('🔤 FontVariantDetector: Bold disabled:', boldCheckbox.disabled, 'Italic disabled:', italicCheckbox.disabled);
    }

    /**
     * Update visual feedback for disabled controls
     */
    updateControlVisualFeedback(control, available) {
        const label = control.closest('label') || control.parentElement;
        if (label) {
            if (available) {
                label.style.opacity = '1';
                label.style.color = '';
                label.title = '';
            } else {
                label.style.opacity = '0.5';
                label.style.color = '#999';
                label.title = 'This variant is not available for the selected font';
            }
        }
    }

    /**
     * Load a specific font variant
     */
    async loadFontVariant(fontFamily, bold = false, italic = false) {
        let variant = 'regular';
        if (bold && italic) variant = 'boldItalic';
        else if (bold) variant = 'bold';
        else if (italic) variant = 'italic';

        const fontFile = this.getFontFile(fontFamily, variant);
        if (!fontFile) {
            console.warn(`🔤 FontVariantDetector: Font variant not available: ${fontFamily} ${variant}`);
            return null;
        }

        // Check cache first
        const cacheKey = `${fontFamily}-${variant}`;
        if (this.fontCache.has(cacheKey)) {
            return this.fontCache.get(cacheKey);
        }

        try {
            // FIXED: Properly encode font file URL to handle spaces and special characters
            const encodedFontFile = encodeURI(fontFile);
            console.log(`🔤 FontVariantDetector: Loading font: ${fontFamily} ${variant} from ${encodedFontFile}`);

            // Create unique font family name for the variant to avoid conflicts
            const uniqueFontFamily = variant === 'regular' ? fontFamily : `${fontFamily}-${variant}`;

            // Load the font using CSS Font Loading API with proper URL encoding
            const fontFace = new FontFace(uniqueFontFamily, `url("${encodedFontFile}")`);
            await fontFace.load();
            document.fonts.add(fontFace);

            this.fontCache.set(cacheKey, { fontFace, uniqueFontFamily });
            console.log(`🔤 FontVariantDetector: Successfully loaded font variant: ${uniqueFontFamily}`);
            return { fontFace, uniqueFontFamily };
        } catch (error) {
            console.error(`🔤 FontVariantDetector: Failed to load font variant: ${fontFamily} ${variant}`, error);
            return null;
        }
    }
}

// Create global instance
window.fontVariantDetector = new FontVariantDetector();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FontVariantDetector;
}
