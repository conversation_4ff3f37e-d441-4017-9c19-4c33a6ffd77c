=== UPDATE OBJECT PROPERTY DEBUG (decorationMode) ===
design-editor.js:1276 Updating property 'decorationMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'noDecoration', newValue: 'horizontalLines'}
design-editor.js:1339 Property 'decorationMode' updated: {oldValue: 'noDecoration', newValue: 'horizontalLines', effectiveValue: 'horizontalLines'}
design-editor.js:1579 Updated body class for decorationMode change
design-editor.js:1582 Forcing redraw...
design-editor.js:3495 🎨 GRADIENT MASK: Drawing circular text with gradient mask
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:4787 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Circular text (no gradient)
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3629 🔍 RENDER ORDER: Step 5 - Drawing shadow effects
design-editor.js:3633 🔍 RENDER ORDER: Step 6 - Drawing gradient text
design-editor.js:3637 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
design-editor.js:3660 🎨 DECORATION: Applied decoration pattern for circular text: horizontalLines
design-editor.js:4179 🎨 MASK: Creating circular text mask for decorations
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:4223 🎨 MASK: Created circular text mask
design-editor.js:3682 🎨 DECORATION: Applied decoration effects on top of circular gradient text
design-editor.js:3686 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top
design-editor.js:3709 🎨 GRADIENT MASK: Circular text with gradient mask complete
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1584 Property 'decorationMode' update complete
design-editor.js:1659 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10614 📚 State saved to history: Change decorationMode Index: 10 Stack size: 11