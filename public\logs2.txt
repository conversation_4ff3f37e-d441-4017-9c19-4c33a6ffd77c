=== SHADOW MODE CHANGE DEBUG ===
design-editor.js:7685 Shadow mode changed to: perspectiveShadow
design-editor.js:7694 Selected object before update: {id: 0, text: 'DESIG<PERSON>', shadowMode: 'noShadow', blockShadowPerspective: false, blockShadowPerspectiveIntensity: 50}
design-editor.js:1199 === UPDATE OBJECT PROPERTY DEBUG (shadowMode) ===
design-editor.js:1208 Updating property 'shadowMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'noShadow', newValue: 'perspectiveShadow'}
design-editor.js:1271 Property 'shadowMode' updated: {oldValue: 'noShadow', newValue: 'perspectiveShadow', effectiveValue: 'perspectiveShadow'}
design-editor.js:1466 Shadow mode set to: perspectiveShadow
design-editor.js:1469 🔍 FRONT OUTLINE DEBUG: Current values before initialization: {shadowMode: 'perspectiveShadow', d3dSecondaryWidth: 4, d3dSecondaryColor: '#00FF66', perspectiveShadowOutlineWidth: 3, perspectiveShadowOutlineColor: '#d1d5db'}
design-editor.js:1499 🔍 FRONT OUTLINE: Force initialized perspective shadow front outline properties: {perspectiveShadowOutlineWidth: 3, perspectiveShadowOutlineColor: '#d1d5db', perspectiveShadowOutlineOpacity: 100, perspectiveShadowOutlineOffsetX: 2, perspectiveShadowOutlineOffsetY: -3}
design-editor.js:1511 Updated body class for shadowMode change
design-editor.js:1514 Forcing redraw...
design-editor.js:4464 🔍 GRID DISTORT CALL [gpt1ei]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4563 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4606 🔍 GRID DISTORT CALL [gpt1ei]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:4323 🎨 GRADIENT MASK [hfvro1]: Drawing grid distorted text with gradient mask
design-editor.js:4331 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:5456 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:5478 Creating text path with letter spacing: 0
design-editor.js:5527 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:5558 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:5569 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:5957 === PERSPECTIVE SHADOW START (w2uq2h) ===
design-editor.js:5958 [w2uq2h] Text object: DESIGN
design-editor.js:6111 [w2uq2h] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6165 [w2uq2h] Number of path shadow steps: 50
design-editor.js:6183 [w2uq2h] Path shadow step 1/50: scale=0.810
design-editor.js:6183 [w2uq2h] Path shadow step 5/50: scale=0.825
design-editor.js:6183 [w2uq2h] Path shadow step 10/50: scale=0.845
design-editor.js:6183 [w2uq2h] Path shadow step 15/50: scale=0.864
design-editor.js:6183 [w2uq2h] Path shadow step 20/50: scale=0.884
design-editor.js:6183 [w2uq2h] Path shadow step 25/50: scale=0.903
design-editor.js:6183 [w2uq2h] Path shadow step 30/50: scale=0.922
design-editor.js:6183 [w2uq2h] Path shadow step 35/50: scale=0.942
design-editor.js:6183 [w2uq2h] Path shadow step 40/50: scale=0.961
design-editor.js:6183 [w2uq2h] Path shadow step 45/50: scale=0.981
design-editor.js:6183 [w2uq2h] Path shadow step 50/50: scale=1.000
design-editor.js:6213 [w2uq2h] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:5835 🔍 FRONT OUTLINE SOURCE #2: All front outlines removed from drawGridDistortedText - handled by gradient masking only
design-editor.js:5838 Grid Distort text rendered with letter spacing: 0
design-editor.js:5456 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:5478 Creating text path with letter spacing: 0
design-editor.js:5527 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:5558 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:5569 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:5835 🔍 FRONT OUTLINE SOURCE #2: All front outlines removed from drawGridDistortedText - handled by gradient masking only
design-editor.js:5838 Grid Distort text rendered with letter spacing: 0
design-editor.js:4426 🔍 RENDER ORDER: Step 7 - Drawing front outlines on top for Grid Distort
design-editor.js:4433 🔍 FRONT OUTLINE SOURCE #4 [hfvro1]: Drawing perspective shadow front outline from gradient masking (drawGridDistortedTextWithGradientMask)
design-editor.js:3744 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3745 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:3813 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:4456 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:4458 🎨 GRADIENT MASK [hfvro1]: Grid distorted text with gradient mask complete
design-editor.js:4616 Grid visibility: true Selected: true
design-editor.js:4618 Drawing grid...
design-editor.js:4982 Drawing grid with 2 rows and 3 columns
design-editor.js:4623 🔍 GRID DISTORT CALL [gpt1ei]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:1516 Property 'shadowMode' update complete
design-editor.js:1591 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:7704 Selected object after update: {id: 0, text: 'DESIGN', shadowMode: 'perspectiveShadow', blockShadowPerspective: false, blockShadowPerspectiveIntensity: 50}
design-editor.js:7715 Perspective toggle disabled: true
design-editor.js:7759 Showing perspective-shadow-param controls
design-editor.js:7782 Forcing redraw...
design-editor.js:4464 🔍 GRID DISTORT CALL [pfs8go]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4563 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4606 🔍 GRID DISTORT CALL [pfs8go]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:4323 🎨 GRADIENT MASK [nj8gtk]: Drawing grid distorted text with gradient mask
design-editor.js:4331 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:5456 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:5478 Creating text path with letter spacing: 0
design-editor.js:5527 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:5558 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:5569 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:5957 === PERSPECTIVE SHADOW START (r7h6al) ===
design-editor.js:5958 [r7h6al] Text object: DESIGN
design-editor.js:6111 [r7h6al] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6165 [r7h6al] Number of path shadow steps: 50
design-editor.js:6183 [r7h6al] Path shadow step 1/50: scale=0.810
design-editor.js:6183 [r7h6al] Path shadow step 5/50: scale=0.825
design-editor.js:6183 [r7h6al] Path shadow step 10/50: scale=0.845
design-editor.js:6183 [r7h6al] Path shadow step 15/50: scale=0.864
design-editor.js:6183 [r7h6al] Path shadow step 20/50: scale=0.884
design-editor.js:6183 [r7h6al] Path shadow step 25/50: scale=0.903
design-editor.js:6183 [r7h6al] Path shadow step 30/50: scale=0.922
design-editor.js:6183 [r7h6al] Path shadow step 35/50: scale=0.942
design-editor.js:6183 [r7h6al] Path shadow step 40/50: scale=0.961
design-editor.js:6183 [r7h6al] Path shadow step 45/50: scale=0.981
design-editor.js:6183 [r7h6al] Path shadow step 50/50: scale=1.000
design-editor.js:6213 [r7h6al] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:5835 🔍 FRONT OUTLINE SOURCE #2: All front outlines removed from drawGridDistortedText - handled by gradient masking only
design-editor.js:5838 Grid Distort text rendered with letter spacing: 0
design-editor.js:5456 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:5478 Creating text path with letter spacing: 0
design-editor.js:5527 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:5558 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:5569 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:5835 🔍 FRONT OUTLINE SOURCE #2: All front outlines removed from drawGridDistortedText - handled by gradient masking only
design-editor.js:5838 Grid Distort text rendered with letter spacing: 0
design-editor.js:4426 🔍 RENDER ORDER: Step 7 - Drawing front outlines on top for Grid Distort
design-editor.js:4433 🔍 FRONT OUTLINE SOURCE #4 [nj8gtk]: Drawing perspective shadow front outline from gradient masking (drawGridDistortedTextWithGradientMask)
design-editor.js:3744 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3745 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:3813 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:4456 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:4458 🎨 GRADIENT MASK [nj8gtk]: Grid distorted text with gradient mask complete
design-editor.js:4616 Grid visibility: true Selected: true
design-editor.js:4618 Drawing grid...
design-editor.js:4982 Drawing grid with 2 rows and 3 columns
design-editor.js:4623 🔍 GRID DISTORT CALL [pfs8go]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:7784 === END SHADOW MODE CHANGE DEBUG ===
design-editor.js:9504 📚 State saved to history: Change shadowMode Index: 10 Stack size: 11