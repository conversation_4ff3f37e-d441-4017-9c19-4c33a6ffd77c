=== SHADOW MODE CHANGE DEBUG ===
design-editor.js:7872 Shadow mode changed to: perspectiveShadow
design-editor.js:7881 Selected object before update: {id: 0, text: 'DESIG<PERSON>', shadowMode: 'noShadow', blockShadowPerspective: false, blockShadowPerspectiveIntensity: 50}
design-editor.js:1199 === UPDATE OBJECT PROPERTY DEBUG (shadowMode) ===
design-editor.js:1208 Updating property 'shadowMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'noShadow', newValue: 'perspectiveShadow'}
design-editor.js:1271 Property 'shadowMode' updated: {oldValue: 'noShadow', newValue: 'perspectiveShadow', effectiveValue: 'perspectiveShadow'}
design-editor.js:1466 Shadow mode set to: perspectiveShadow
design-editor.js:1469 🔍 FRONT OUTLINE DEBUG: Current values before initialization: {shadowMode: 'perspectiveShadow', d3dSecondaryWidth: 4, d3dSecondaryColor: '#00FF66', perspectiveShadowOutlineWidth: 3, perspectiveShadowOutlineColor: '#d1d5db'}
design-editor.js:1499 🔍 FRONT OUTLINE: Force initialized perspective shadow front outline properties: {perspectiveShadowOutlineWidth: 3, perspectiveShadowOutlineColor: '#d1d5db', perspectiveShadowOutlineOpacity: 100, perspectiveShadowOutlineOffsetX: 2, perspectiveShadowOutlineOffsetY: -3}
design-editor.js:1511 Updated body class for shadowMode change
design-editor.js:1514 Forcing redraw...
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (iag2r9) ===
design-editor.js:6145 [iag2r9] Text object: D
design-editor.js:6176 [iag2r9] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [iag2r9] Number of shadow steps: 50
design-editor.js:6229 [iag2r9] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [iag2r9] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [iag2r9] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [iag2r9] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [iag2r9] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [iag2r9] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [iag2r9] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [iag2r9] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [iag2r9] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [iag2r9] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [iag2r9] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [iag2r9] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [iag2r9] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [iag2r9] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [iag2r9] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [iag2r9] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [iag2r9] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [iag2r9] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [iag2r9] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [iag2r9] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [iag2r9] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [iag2r9] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [iag2r9] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (ofr2i4) ===
design-editor.js:6145 [ofr2i4] Text object: E
design-editor.js:6176 [ofr2i4] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [ofr2i4] Number of shadow steps: 50
design-editor.js:6229 [ofr2i4] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [ofr2i4] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [ofr2i4] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [ofr2i4] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [ofr2i4] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [ofr2i4] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [ofr2i4] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [ofr2i4] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [ofr2i4] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [ofr2i4] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [ofr2i4] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [ofr2i4] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [ofr2i4] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [ofr2i4] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [ofr2i4] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [ofr2i4] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [ofr2i4] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [ofr2i4] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [ofr2i4] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [ofr2i4] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [ofr2i4] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [ofr2i4] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [ofr2i4] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (cqzv7w) ===
design-editor.js:6145 [cqzv7w] Text object: S
design-editor.js:6176 [cqzv7w] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [cqzv7w] Number of shadow steps: 50
design-editor.js:6229 [cqzv7w] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [cqzv7w] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [cqzv7w] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [cqzv7w] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [cqzv7w] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [cqzv7w] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [cqzv7w] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [cqzv7w] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [cqzv7w] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [cqzv7w] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [cqzv7w] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [cqzv7w] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [cqzv7w] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [cqzv7w] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [cqzv7w] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [cqzv7w] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [cqzv7w] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [cqzv7w] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [cqzv7w] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [cqzv7w] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [cqzv7w] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [cqzv7w] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [cqzv7w] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (uneo49) ===
design-editor.js:6145 [uneo49] Text object: I
design-editor.js:6176 [uneo49] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [uneo49] Number of shadow steps: 50
design-editor.js:6229 [uneo49] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [uneo49] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [uneo49] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [uneo49] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [uneo49] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [uneo49] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [uneo49] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [uneo49] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [uneo49] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [uneo49] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [uneo49] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [uneo49] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [uneo49] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [uneo49] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [uneo49] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [uneo49] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [uneo49] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [uneo49] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [uneo49] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [uneo49] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [uneo49] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [uneo49] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [uneo49] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (n8p2up) ===
design-editor.js:6145 [n8p2up] Text object: G
design-editor.js:6176 [n8p2up] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [n8p2up] Number of shadow steps: 50
design-editor.js:6229 [n8p2up] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [n8p2up] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [n8p2up] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [n8p2up] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [n8p2up] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [n8p2up] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [n8p2up] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [n8p2up] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [n8p2up] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [n8p2up] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [n8p2up] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [n8p2up] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [n8p2up] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [n8p2up] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [n8p2up] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [n8p2up] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [n8p2up] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [n8p2up] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [n8p2up] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [n8p2up] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [n8p2up] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [n8p2up] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [n8p2up] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (6dldv9) ===
design-editor.js:6145 [6dldv9] Text object: N
design-editor.js:6176 [6dldv9] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [6dldv9] Number of shadow steps: 50
design-editor.js:6229 [6dldv9] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [6dldv9] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [6dldv9] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [6dldv9] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [6dldv9] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [6dldv9] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [6dldv9] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [6dldv9] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [6dldv9] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [6dldv9] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [6dldv9] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [6dldv9] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [6dldv9] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [6dldv9] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [6dldv9] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [6dldv9] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [6dldv9] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [6dldv9] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [6dldv9] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [6dldv9] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [6dldv9] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [6dldv9] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [6dldv9] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:1516 Property 'shadowMode' update complete
design-editor.js:1591 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:7891 Selected object after update: {id: 0, text: 'DESIGN', shadowMode: 'perspectiveShadow', blockShadowPerspective: false, blockShadowPerspectiveIntensity: 50}
design-editor.js:7902 Perspective toggle disabled: true
design-editor.js:7946 Showing perspective-shadow-param controls
design-editor.js:7969 Forcing redraw...
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (641nz4) ===
design-editor.js:6145 [641nz4] Text object: D
design-editor.js:6176 [641nz4] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [641nz4] Number of shadow steps: 50
design-editor.js:6229 [641nz4] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [641nz4] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [641nz4] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [641nz4] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [641nz4] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [641nz4] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [641nz4] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [641nz4] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [641nz4] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [641nz4] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [641nz4] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [641nz4] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [641nz4] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [641nz4] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [641nz4] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [641nz4] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [641nz4] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [641nz4] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [641nz4] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [641nz4] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [641nz4] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [641nz4] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [641nz4] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (lcg8zb) ===
design-editor.js:6145 [lcg8zb] Text object: E
design-editor.js:6176 [lcg8zb] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [lcg8zb] Number of shadow steps: 50
design-editor.js:6229 [lcg8zb] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [lcg8zb] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [lcg8zb] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [lcg8zb] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [lcg8zb] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [lcg8zb] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [lcg8zb] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [lcg8zb] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [lcg8zb] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [lcg8zb] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [lcg8zb] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [lcg8zb] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [lcg8zb] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [lcg8zb] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [lcg8zb] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [lcg8zb] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [lcg8zb] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [lcg8zb] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [lcg8zb] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [lcg8zb] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [lcg8zb] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [lcg8zb] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [lcg8zb] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (uldoeb) ===
design-editor.js:6145 [uldoeb] Text object: S
design-editor.js:6176 [uldoeb] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [uldoeb] Number of shadow steps: 50
design-editor.js:6229 [uldoeb] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [uldoeb] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [uldoeb] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [uldoeb] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [uldoeb] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [uldoeb] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [uldoeb] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [uldoeb] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [uldoeb] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [uldoeb] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [uldoeb] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [uldoeb] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [uldoeb] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [uldoeb] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [uldoeb] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [uldoeb] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [uldoeb] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [uldoeb] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [uldoeb] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [uldoeb] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [uldoeb] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [uldoeb] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [uldoeb] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (j6blm9) ===
design-editor.js:6145 [j6blm9] Text object: I
design-editor.js:6176 [j6blm9] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [j6blm9] Number of shadow steps: 50
design-editor.js:6229 [j6blm9] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [j6blm9] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [j6blm9] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [j6blm9] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [j6blm9] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [j6blm9] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [j6blm9] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [j6blm9] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [j6blm9] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [j6blm9] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [j6blm9] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [j6blm9] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [j6blm9] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [j6blm9] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [j6blm9] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [j6blm9] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [j6blm9] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [j6blm9] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [j6blm9] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [j6blm9] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [j6blm9] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [j6blm9] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [j6blm9] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (p2acod) ===
design-editor.js:6145 [p2acod] Text object: G
design-editor.js:6176 [p2acod] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [p2acod] Number of shadow steps: 50
design-editor.js:6229 [p2acod] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [p2acod] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [p2acod] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [p2acod] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [p2acod] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [p2acod] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [p2acod] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [p2acod] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [p2acod] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [p2acod] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [p2acod] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [p2acod] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [p2acod] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [p2acod] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [p2acod] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [p2acod] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [p2acod] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [p2acod] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [p2acod] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [p2acod] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [p2acod] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [p2acod] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [p2acod] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6144 === PERSPECTIVE SHADOW START (2q1bj9) ===
design-editor.js:6145 [2q1bj9] Text object: N
design-editor.js:6176 [2q1bj9] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6209 [2q1bj9] Number of shadow steps: 50
design-editor.js:6229 [2q1bj9] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6243 [2q1bj9] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6229 [2q1bj9] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6243 [2q1bj9] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6229 [2q1bj9] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6243 [2q1bj9] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6229 [2q1bj9] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6243 [2q1bj9] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6229 [2q1bj9] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6243 [2q1bj9] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6229 [2q1bj9] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6243 [2q1bj9] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6229 [2q1bj9] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6243 [2q1bj9] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6229 [2q1bj9] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6243 [2q1bj9] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6229 [2q1bj9] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6243 [2q1bj9] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6229 [2q1bj9] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6243 [2q1bj9] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6229 [2q1bj9] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6243 [2q1bj9] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6276 [2q1bj9] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:7971 === END SHADOW MODE CHANGE DEBUG ===
design-editor.js:9691 📚 State saved to history: Change shadowMode Index: 28 Stack size: 29