=== SHADOW MODE CHANGE DEBUG ===
design-editor.js:8071 Shadow mode changed to: perspectiveShadow
design-editor.js:8080 Selected object before update: {id: 0, text: 'DESIGN', shadowMode: 'noShadow', blockShadowPerspective: false, blockShadowPerspectiveIntensity: 50}
design-editor.js:1199 === UPDATE OBJECT PROPERTY DEBUG (shadowMode) ===
design-editor.js:1208 Updating property 'shadowMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'noShadow', newValue: 'perspectiveShadow'}
design-editor.js:1271 Property 'shadowMode' updated: {oldValue: 'noShadow', newValue: 'perspectiveShadow', effectiveValue: 'perspectiveShadow'}
design-editor.js:1466 Shadow mode set to: perspectiveShadow
design-editor.js:1469 🔍 FRONT OUTLINE DEBUG: Current values before initialization: {shadowMode: 'perspectiveShadow', d3dSecondaryWidth: 4, d3dSecondaryColor: '#00FF00', perspectiveShadowOutlineWidth: 3, perspectiveShadowOutlineColor: '#d1d5db'}
design-editor.js:1499 🔍 FRONT OUTLINE: Force initialized perspective shadow front outline properties: {perspectiveShadowOutlineWidth: 3, perspectiveShadowOutlineColor: '#d1d5db', perspectiveShadowOutlineOpacity: 100, perspectiveShadowOutlineOffsetX: 2, perspectiveShadowOutlineOffsetY: -3}
design-editor.js:1511 Updated body class for shadowMode change
design-editor.js:1514 Forcing redraw...
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:2865 Applying perspective shadow in renderStyledObjectToOffscreen
design-editor.js:6343 === PERSPECTIVE SHADOW START (ye6u1o) ===
design-editor.js:6344 [ye6u1o] Text object: DESIGN
design-editor.js:6375 [ye6u1o] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:6408 [ye6u1o] Number of shadow steps: 50
design-editor.js:6428 [ye6u1o] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6442 [ye6u1o] Shadow position: (2038.9, 2082.1), scale: 0.810
design-editor.js:6428 [ye6u1o] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6442 [ye6u1o] Shadow position: (2039.6, 2079.3), scale: 0.825
design-editor.js:6428 [ye6u1o] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6442 [ye6u1o] Shadow position: (2040.5, 2075.8), scale: 0.845
design-editor.js:6428 [ye6u1o] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6442 [ye6u1o] Shadow position: (2041.5, 2072.3), scale: 0.864
design-editor.js:6428 [ye6u1o] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6442 [ye6u1o] Shadow position: (2042.4, 2068.9), scale: 0.884
design-editor.js:6428 [ye6u1o] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6442 [ye6u1o] Shadow position: (2043.3, 2065.4), scale: 0.903
design-editor.js:6428 [ye6u1o] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6442 [ye6u1o] Shadow position: (2044.3, 2061.9), scale: 0.922
design-editor.js:6428 [ye6u1o] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6442 [ye6u1o] Shadow position: (2045.2, 2058.4), scale: 0.942
design-editor.js:6428 [ye6u1o] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6442 [ye6u1o] Shadow position: (2046.1, 2055.0), scale: 0.961
design-editor.js:6428 [ye6u1o] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6442 [ye6u1o] Shadow position: (2047.1, 2051.5), scale: 0.981
design-editor.js:6428 [ye6u1o] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6442 [ye6u1o] Shadow position: (2048.0, 2048.0), scale: 1.000
design-editor.js:6475 [ye6u1o] === PERSPECTIVE SHADOW END ===
design-editor.js:3040 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3047 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:3367 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)
design-editor.js:4158 🎨 NORMAL FRONT OUTLINE: Drawing normal text front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4180 🎨 NORMAL FRONT OUTLINE: Drew normal text front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:1516 Property 'shadowMode' update complete
design-editor.js:1591 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:8090 Selected object after update: {id: 0, text: 'DESIGN', shadowMode: 'perspectiveShadow', blockShadowPerspective: false, blockShadowPerspectiveIntensity: 50}
design-editor.js:8101 Perspective toggle disabled: true
design-editor.js:8145 Showing perspective-shadow-param controls
design-editor.js:8168 Forcing redraw...
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:2865 Applying perspective shadow in renderStyledObjectToOffscreen
design-editor.js:6343 === PERSPECTIVE SHADOW START (3miyhr) ===
design-editor.js:6344 [3miyhr] Text object: DESIGN
design-editor.js:6375 [3miyhr] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:6408 [3miyhr] Number of shadow steps: 50
design-editor.js:6428 [3miyhr] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6442 [3miyhr] Shadow position: (2038.9, 2082.1), scale: 0.810
design-editor.js:6428 [3miyhr] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6442 [3miyhr] Shadow position: (2039.6, 2079.3), scale: 0.825
design-editor.js:6428 [3miyhr] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6442 [3miyhr] Shadow position: (2040.5, 2075.8), scale: 0.845
design-editor.js:6428 [3miyhr] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6442 [3miyhr] Shadow position: (2041.5, 2072.3), scale: 0.864
design-editor.js:6428 [3miyhr] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6442 [3miyhr] Shadow position: (2042.4, 2068.9), scale: 0.884
design-editor.js:6428 [3miyhr] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6442 [3miyhr] Shadow position: (2043.3, 2065.4), scale: 0.903
design-editor.js:6428 [3miyhr] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6442 [3miyhr] Shadow position: (2044.3, 2061.9), scale: 0.922
design-editor.js:6428 [3miyhr] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6442 [3miyhr] Shadow position: (2045.2, 2058.4), scale: 0.942
design-editor.js:6428 [3miyhr] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6442 [3miyhr] Shadow position: (2046.1, 2055.0), scale: 0.961
design-editor.js:6428 [3miyhr] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6442 [3miyhr] Shadow position: (2047.1, 2051.5), scale: 0.981
design-editor.js:6428 [3miyhr] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6442 [3miyhr] Shadow position: (2048.0, 2048.0), scale: 1.000
design-editor.js:6475 [3miyhr] === PERSPECTIVE SHADOW END ===
design-editor.js:3040 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3047 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:3367 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)
design-editor.js:4158 🎨 NORMAL FRONT OUTLINE: Drawing normal text front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4180 🎨 NORMAL FRONT OUTLINE: Drew normal text front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:8170 === END SHADOW MODE CHANGE DEBUG ===
design-editor.js:8068 [Violation] 'change' handler took 153ms
design-editor.js:9890 📚 State saved to history: Change shadowMode Index: 5 Stack size: 6