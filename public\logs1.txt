=== UPDATE OBJECT PROPERTY DEBUG (hBottomLimit) ===
design-editor.js:1276 Updating property 'hBottomLimit' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 147, newValue: 144}
design-editor.js:1339 Property 'hBottomLimit' updated: {oldValue: 147, newValue: 144, effectiveValue: 144}
design-editor.js:1582 Forcing redraw...
design-editor.js:5324 🔍 GRID DISTORT CALL [27dz1v]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5423 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:5466 🔍 GRID DISTORT CALL [27dz1v]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:5134 🎨 GRADIENT MASK [d8d48i]: Drawing grid distorted text with gradient mask
design-editor.js:5142 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:5237 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Grid Distort
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
design-editor.js:5260 🎨 DECORATION: Applied decoration pattern for grid distort text: horizontalLines
design-editor.js:4173 🎨 MASK: Creating grid distort text mask for decorations
design-editor.js:4287 🎨 MASK: Created grid distort text mask
design-editor.js:5282 🎨 DECORATION: Applied decoration effects on top of grid distort gradient text
design-editor.js:5286 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort
design-editor.js:5316 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:5318 🎨 GRADIENT MASK [d8d48i]: Grid distorted text with gradient mask complete
design-editor.js:5476 Grid visibility: true Selected: true
design-editor.js:5478 Drawing grid...
design-editor.js:5835 Drawing grid with 2 rows and 3 columns
design-editor.js:5483 🔍 GRID DISTORT CALL [27dz1v]: Main font successful - skipping fallback
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1584 Property 'hBottomLimit' update complete
design-editor.js:1659 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:5324 🔍 GRID DISTORT CALL [rk616z]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5423 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:5466 🔍 GRID DISTORT CALL [rk616z]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:5134 🎨 GRADIENT MASK [nm8w9x]: Drawing grid distorted text with gradient mask
design-editor.js:5142 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:5237 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Grid Distort
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
design-editor.js:5260 🎨 DECORATION: Applied decoration pattern for grid distort text: horizontalLines
design-editor.js:4173 🎨 MASK: Creating grid distort text mask for decorations
design-editor.js:4287 🎨 MASK: Created grid distort text mask
design-editor.js:5282 🎨 DECORATION: Applied decoration effects on top of grid distort gradient text
design-editor.js:5286 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort
design-editor.js:5316 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:5318 🎨 GRADIENT MASK [nm8w9x]: Grid distorted text with gradient mask complete
design-editor.js:5476 Grid visibility: true Selected: true
design-editor.js:5478 Drawing grid...
design-editor.js:5835 Drawing grid with 2 rows and 3 columns
design-editor.js:5483 🔍 GRID DISTORT CALL [rk616z]: Main font successful - skipping fallback
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1267 === UPDATE OBJECT PROPERTY DEBUG (hBottomLimit) ===
design-editor.js:1276 Updating property 'hBottomLimit' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 144, newValue: 140}
design-editor.js:1339 Property 'hBottomLimit' updated: {oldValue: 144, newValue: 140, effectiveValue: 140}
design-editor.js:1582 Forcing redraw...
design-editor.js:5324 🔍 GRID DISTORT CALL [6lia8a]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5423 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:5466 🔍 GRID DISTORT CALL [6lia8a]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:5134 🎨 GRADIENT MASK [o9u5cb]: Drawing grid distorted text with gradient mask
design-editor.js:5142 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:5237 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Grid Distort
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
design-editor.js:5260 🎨 DECORATION: Applied decoration pattern for grid distort text: horizontalLines
design-editor.js:4173 🎨 MASK: Creating grid distort text mask for decorations
design-editor.js:4287 🎨 MASK: Created grid distort text mask
design-editor.js:5282 🎨 DECORATION: Applied decoration effects on top of grid distort gradient text
design-editor.js:5286 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort
design-editor.js:5316 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:5318 🎨 GRADIENT MASK [o9u5cb]: Grid distorted text with gradient mask complete
design-editor.js:5476 Grid visibility: true Selected: true
design-editor.js:5478 Drawing grid...
design-editor.js:5835 Drawing grid with 2 rows and 3 columns
design-editor.js:5483 🔍 GRID DISTORT CALL [6lia8a]: Main font successful - skipping fallback
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1584 Property 'hBottomLimit' update complete
design-editor.js:1659 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:5324 🔍 GRID DISTORT CALL [czeusx]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5423 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:5466 🔍 GRID DISTORT CALL [czeusx]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:5134 🎨 GRADIENT MASK [ntdewy]: Drawing grid distorted text with gradient mask
design-editor.js:5142 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:5237 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Grid Distort
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
design-editor.js:5260 🎨 DECORATION: Applied decoration pattern for grid distort text: horizontalLines
design-editor.js:4173 🎨 MASK: Creating grid distort text mask for decorations
design-editor.js:4287 🎨 MASK: Created grid distort text mask
design-editor.js:5282 🎨 DECORATION: Applied decoration effects on top of grid distort gradient text
design-editor.js:5286 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort
design-editor.js:5316 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:5318 🎨 GRADIENT MASK [ntdewy]: Grid distorted text with gradient mask complete
design-editor.js:5476 Grid visibility: true Selected: true
design-editor.js:5478 Drawing grid...
design-editor.js:5835 Drawing grid with 2 rows and 3 columns
design-editor.js:5483 🔍 GRID DISTORT CALL [czeusx]: Main font successful - skipping fallback
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:10510 📚 State saved to history: Change hBottomLimit Index: 38 Stack size: 39
design-editor.js:1267 === UPDATE OBJECT PROPERTY DEBUG (hTopLimit) ===
design-editor.js:1276 Updating property 'hTopLimit' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 67, newValue: 56}
design-editor.js:1339 Property 'hTopLimit' updated: {oldValue: 67, newValue: 56, effectiveValue: 56}
design-editor.js:1582 Forcing redraw...
design-editor.js:5324 🔍 GRID DISTORT CALL [bys0t0]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5423 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:5466 🔍 GRID DISTORT CALL [bys0t0]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:5134 🎨 GRADIENT MASK [25xw4a]: Drawing grid distorted text with gradient mask
design-editor.js:5142 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:5237 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Grid Distort
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
design-editor.js:5260 🎨 DECORATION: Applied decoration pattern for grid distort text: horizontalLines
design-editor.js:4173 🎨 MASK: Creating grid distort text mask for decorations
design-editor.js:4287 🎨 MASK: Created grid distort text mask
design-editor.js:5282 🎨 DECORATION: Applied decoration effects on top of grid distort gradient text
design-editor.js:5286 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort
design-editor.js:5316 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:5318 🎨 GRADIENT MASK [25xw4a]: Grid distorted text with gradient mask complete
design-editor.js:5476 Grid visibility: true Selected: true
design-editor.js:5478 Drawing grid...
design-editor.js:5835 Drawing grid with 2 rows and 3 columns
design-editor.js:5483 🔍 GRID DISTORT CALL [bys0t0]: Main font successful - skipping fallback
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1584 Property 'hTopLimit' update complete
design-editor.js:1659 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:5324 🔍 GRID DISTORT CALL [6rbwke]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5423 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:5466 🔍 GRID DISTORT CALL [6rbwke]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:5134 🎨 GRADIENT MASK [sgmox7]: Drawing grid distorted text with gradient mask
design-editor.js:5142 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:5237 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Grid Distort
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
design-editor.js:5260 🎨 DECORATION: Applied decoration pattern for grid distort text: horizontalLines
design-editor.js:4173 🎨 MASK: Creating grid distort text mask for decorations
design-editor.js:4287 🎨 MASK: Created grid distort text mask
design-editor.js:5282 🎨 DECORATION: Applied decoration effects on top of grid distort gradient text
design-editor.js:5286 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort
design-editor.js:5316 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:5318 🎨 GRADIENT MASK [sgmox7]: Grid distorted text with gradient mask complete
design-editor.js:5476 Grid visibility: true Selected: true
design-editor.js:5478 Drawing grid...
design-editor.js:5835 Drawing grid with 2 rows and 3 columns
design-editor.js:5483 🔍 GRID DISTORT CALL [6rbwke]: Main font successful - skipping fallback
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1267 === UPDATE OBJECT PROPERTY DEBUG (hTopLimit) ===
design-editor.js:1276 Updating property 'hTopLimit' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 56, newValue: 60}
design-editor.js:1339 Property 'hTopLimit' updated: {oldValue: 56, newValue: 60, effectiveValue: 60}
design-editor.js:1582 Forcing redraw...
design-editor.js:5324 🔍 GRID DISTORT CALL [cj1rfs]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5423 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:5466 🔍 GRID DISTORT CALL [cj1rfs]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:5134 🎨 GRADIENT MASK [0yuyh0]: Drawing grid distorted text with gradient mask
design-editor.js:5142 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:5237 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Grid Distort
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
design-editor.js:5260 🎨 DECORATION: Applied decoration pattern for grid distort text: horizontalLines
design-editor.js:4173 🎨 MASK: Creating grid distort text mask for decorations
design-editor.js:4287 🎨 MASK: Created grid distort text mask
design-editor.js:5282 🎨 DECORATION: Applied decoration effects on top of grid distort gradient text
design-editor.js:5286 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort
design-editor.js:5316 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:5318 🎨 GRADIENT MASK [0yuyh0]: Grid distorted text with gradient mask complete
design-editor.js:5476 Grid visibility: true Selected: true
design-editor.js:5478 Drawing grid...
design-editor.js:5835 Drawing grid with 2 rows and 3 columns
design-editor.js:5483 🔍 GRID DISTORT CALL [cj1rfs]: Main font successful - skipping fallback
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1584 Property 'hTopLimit' update complete
design-editor.js:1659 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:5324 🔍 GRID DISTORT CALL [ndcrfd]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5423 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:5466 🔍 GRID DISTORT CALL [ndcrfd]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:5134 🎨 GRADIENT MASK [mi9gs9]: Drawing grid distorted text with gradient mask
design-editor.js:5142 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:5237 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Grid Distort
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
design-editor.js:5260 🎨 DECORATION: Applied decoration pattern for grid distort text: horizontalLines
design-editor.js:4173 🎨 MASK: Creating grid distort text mask for decorations
design-editor.js:4287 🎨 MASK: Created grid distort text mask
design-editor.js:5282 🎨 DECORATION: Applied decoration effects on top of grid distort gradient text
design-editor.js:5286 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort
design-editor.js:5316 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:5318 🎨 GRADIENT MASK [mi9gs9]: Grid distorted text with gradient mask complete
design-editor.js:5476 Grid visibility: true Selected: true
design-editor.js:5478 Drawing grid...
design-editor.js:5835 Drawing grid with 2 rows and 3 columns
design-editor.js:5483 🔍 GRID DISTORT CALL [ndcrfd]: Main font successful - skipping fallback
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1267 === UPDATE OBJECT PROPERTY DEBUG (hTopLimit) ===
design-editor.js:1276 Updating property 'hTopLimit' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 60, newValue: 63}
design-editor.js:1339 Property 'hTopLimit' updated: {oldValue: 60, newValue: 63, effectiveValue: 63}
design-editor.js:1582 Forcing redraw...
design-editor.js:5324 🔍 GRID DISTORT CALL [bbgz2v]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5423 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:5466 🔍 GRID DISTORT CALL [bbgz2v]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:5134 🎨 GRADIENT MASK [coa1ot]: Drawing grid distorted text with gradient mask
design-editor.js:5142 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:5237 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Grid Distort
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
design-editor.js:5260 🎨 DECORATION: Applied decoration pattern for grid distort text: horizontalLines
design-editor.js:4173 🎨 MASK: Creating grid distort text mask for decorations
design-editor.js:4287 🎨 MASK: Created grid distort text mask
design-editor.js:5282 🎨 DECORATION: Applied decoration effects on top of grid distort gradient text
design-editor.js:5286 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort
design-editor.js:5316 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:5318 🎨 GRADIENT MASK [coa1ot]: Grid distorted text with gradient mask complete
design-editor.js:5476 Grid visibility: true Selected: true
design-editor.js:5478 Drawing grid...
design-editor.js:5835 Drawing grid with 2 rows and 3 columns
design-editor.js:5483 🔍 GRID DISTORT CALL [bbgz2v]: Main font successful - skipping fallback
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1584 Property 'hTopLimit' update complete
design-editor.js:1659 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:5324 🔍 GRID DISTORT CALL [jf0lvf]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5423 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:5466 🔍 GRID DISTORT CALL [jf0lvf]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:5134 🎨 GRADIENT MASK [034s54]: Drawing grid distorted text with gradient mask
design-editor.js:5142 🔍 GRADIENT MASK: Stored original gradient state: true
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:6309 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6331 Creating text path with letter spacing: 0
design-editor.js:6380 Text dimensions: {measuredWidth: 727.7999877929688, measuredHeight: 140, pathWidth: 675.8000000000001, pathHeight: 142.60000000000002, finalWidth: 727.7999877929688, …}
design-editor.js:6411 Grid bounds calculation: {gridLeft: -487.5389938354492, gridTop: -193.63899993896484, gridWidth: 975.0779876708984, gridHeight: 387.2779998779297, textWidth: 727.7999877929688, …}
design-editor.js:6422 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6687 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6708 Grid Distort text rendered with letter spacing: 0
design-editor.js:5237 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Grid Distort
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
design-editor.js:5260 🎨 DECORATION: Applied decoration pattern for grid distort text: horizontalLines
design-editor.js:4173 🎨 MASK: Creating grid distort text mask for decorations
design-editor.js:4287 🎨 MASK: Created grid distort text mask
design-editor.js:5282 🎨 DECORATION: Applied decoration effects on top of grid distort gradient text
design-editor.js:5286 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort
design-editor.js:5316 🔍 GRADIENT MASK: Cleaned up original gradient state flag
design-editor.js:5318 🎨 GRADIENT MASK [034s54]: Grid distorted text with gradient mask complete
design-editor.js:5476 Grid visibility: true Selected: true
design-editor.js:5478 Drawing grid...
design-editor.js:5835 Drawing grid with 2 rows and 3 columns
design-editor.js:5483 🔍 GRID DISTORT CALL [jf0lvf]: Main font successful - skipping fallback
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:10510 📚 State saved to history: Change hTopLimit Index: 39 Stack size: 40