=== UPDATE OBJECT PROPERTY DEBUG (effectMode) ===
design-editor.js:1208 Updating property 'effectMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'normal', newValue: 'grid-distort'}
design-editor.js:1271 Property 'effectMode' updated: {oldValue: 'normal', newValue: 'grid-distort', effectiveValue: 'grid-distort'}
design-editor.js:1511 Updated body class for effectMode change
design-editor.js:1514 Forcing redraw...
design-editor.js:4771 🔍 GRID DISTORT CALL [86grnb]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [86grnb]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (55398v) ===
design-editor.js:6282 [55398v] Text object: DESIGN
design-editor.js:6435 [55398v] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [55398v] Number of path shadow steps: 50
design-editor.js:6507 [55398v] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [55398v] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [55398v] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [55398v] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [55398v] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [55398v] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [55398v] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [55398v] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [55398v] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [55398v] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [55398v] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [55398v] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [86grnb]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:1516 Property 'effectMode' update complete
design-editor.js:1591 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:7944 Switching to grid-distort mode, initializing grid
design-editor.js:9828 📚 State saved to history: Change effectMode Index: 7 Stack size: 8
design-editor.js:7479 Checking for grid point hit
design-editor.js:7488 Grid point hit: 0 1
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -7.083320617675781 -190
design-editor.js:7637 Updated point position: (0.00, -190.00) -> (-7.08, -190.00)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [9jufhb]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [9jufhb]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (frpx9q) ===
design-editor.js:6282 [frpx9q] Text object: DESIGN
design-editor.js:6435 [frpx9q] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [frpx9q] Number of path shadow steps: 50
design-editor.js:6507 [frpx9q] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [frpx9q] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [frpx9q] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [frpx9q] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [frpx9q] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [frpx9q] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [frpx9q] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [frpx9q] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [frpx9q] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [frpx9q] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [frpx9q] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [frpx9q] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [9jufhb]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -4.583320617675781 -193.75
design-editor.js:7637 Updated point position: (-7.08, -190.00) -> (-4.58, -193.75)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [nmc4h6]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [nmc4h6]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (d5op3w) ===
design-editor.js:6282 [d5op3w] Text object: DESIGN
design-editor.js:6435 [d5op3w] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [d5op3w] Number of path shadow steps: 50
design-editor.js:6507 [d5op3w] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [d5op3w] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [d5op3w] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [d5op3w] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [d5op3w] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [d5op3w] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [d5op3w] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [d5op3w] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [d5op3w] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [d5op3w] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [d5op3w] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [d5op3w] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [nmc4h6]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -4.583320617675781 -195
design-editor.js:7637 Updated point position: (-4.58, -193.75) -> (-4.58, -195.00)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [ca45dy]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [ca45dy]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (2wspeq) ===
design-editor.js:6282 [2wspeq] Text object: DESIGN
design-editor.js:6435 [2wspeq] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [2wspeq] Number of path shadow steps: 50
design-editor.js:6507 [2wspeq] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [2wspeq] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [2wspeq] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [2wspeq] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [2wspeq] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [2wspeq] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [2wspeq] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [2wspeq] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [2wspeq] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [2wspeq] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [2wspeq] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [2wspeq] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [ca45dy]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -4.583320617675781 -196.25
design-editor.js:7637 Updated point position: (-4.58, -195.00) -> (-4.58, -196.25)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [ivhckm]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [ivhckm]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (z6kp9q) ===
design-editor.js:6282 [z6kp9q] Text object: DESIGN
design-editor.js:6435 [z6kp9q] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [z6kp9q] Number of path shadow steps: 50
design-editor.js:6507 [z6kp9q] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [z6kp9q] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [z6kp9q] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [z6kp9q] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [z6kp9q] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [z6kp9q] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [z6kp9q] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [z6kp9q] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [z6kp9q] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [z6kp9q] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [z6kp9q] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [z6kp9q] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [ivhckm]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -3.3333206176757812 -223.75
design-editor.js:7637 Updated point position: (-4.58, -196.25) -> (-3.33, -223.75)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [3macxa]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [3macxa]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (litdv7) ===
design-editor.js:6282 [litdv7] Text object: DESIGN
design-editor.js:6435 [litdv7] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [litdv7] Number of path shadow steps: 50
design-editor.js:6507 [litdv7] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [litdv7] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [litdv7] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [litdv7] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [litdv7] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [litdv7] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [litdv7] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [litdv7] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [litdv7] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [litdv7] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [litdv7] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [litdv7] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [3macxa]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -3.3333206176757812 -225
design-editor.js:7637 Updated point position: (-3.33, -223.75) -> (-3.33, -225.00)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [nm226l]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [nm226l]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (iyw5qx) ===
design-editor.js:6282 [iyw5qx] Text object: DESIGN
design-editor.js:6435 [iyw5qx] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [iyw5qx] Number of path shadow steps: 50
design-editor.js:6507 [iyw5qx] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [iyw5qx] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [iyw5qx] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [iyw5qx] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [iyw5qx] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [iyw5qx] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [iyw5qx] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [iyw5qx] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [iyw5qx] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [iyw5qx] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [iyw5qx] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [iyw5qx] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [nm226l]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -3.3333206176757812 -226.25
design-editor.js:7637 Updated point position: (-3.33, -225.00) -> (-3.33, -226.25)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [6570c2]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [6570c2]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (rqevw2) ===
design-editor.js:6282 [rqevw2] Text object: DESIGN
design-editor.js:6435 [rqevw2] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [rqevw2] Number of path shadow steps: 50
design-editor.js:6507 [rqevw2] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [rqevw2] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [rqevw2] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [rqevw2] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [rqevw2] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [rqevw2] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [rqevw2] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [rqevw2] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [rqevw2] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [rqevw2] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [rqevw2] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [rqevw2] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [6570c2]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -3.3333206176757812 -228.75
design-editor.js:7637 Updated point position: (-3.33, -226.25) -> (-3.33, -228.75)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [9eerec]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [9eerec]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (c07kxt) ===
design-editor.js:6282 [c07kxt] Text object: DESIGN
design-editor.js:6435 [c07kxt] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [c07kxt] Number of path shadow steps: 50
design-editor.js:6507 [c07kxt] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [c07kxt] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [c07kxt] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [c07kxt] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [c07kxt] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [c07kxt] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [c07kxt] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [c07kxt] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [c07kxt] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [c07kxt] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [c07kxt] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [c07kxt] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [9eerec]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -3.3333206176757812 -230
design-editor.js:7637 Updated point position: (-3.33, -228.75) -> (-3.33, -230.00)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [t0zbg5]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [t0zbg5]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (xi2w1w) ===
design-editor.js:6282 [xi2w1w] Text object: DESIGN
design-editor.js:6435 [xi2w1w] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [xi2w1w] Number of path shadow steps: 50
design-editor.js:6507 [xi2w1w] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [xi2w1w] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [xi2w1w] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [xi2w1w] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [xi2w1w] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [xi2w1w] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [xi2w1w] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [xi2w1w] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [xi2w1w] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [xi2w1w] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [xi2w1w] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [xi2w1w] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [t0zbg5]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -3.3333206176757812 -251.25
design-editor.js:7637 Updated point position: (-3.33, -230.00) -> (-3.33, -251.25)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [ffz4y2]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [ffz4y2]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (1b17gi) ===
design-editor.js:6282 [1b17gi] Text object: DESIGN
design-editor.js:6435 [1b17gi] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [1b17gi] Number of path shadow steps: 50
design-editor.js:6507 [1b17gi] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [1b17gi] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [1b17gi] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [1b17gi] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [1b17gi] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [1b17gi] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [1b17gi] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [1b17gi] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [1b17gi] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [1b17gi] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [1b17gi] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [1b17gi] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [ffz4y2]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -4.583320617675781 -251.25
design-editor.js:7637 Updated point position: (-3.33, -251.25) -> (-4.58, -251.25)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [38sv0t]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [38sv0t]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (h0q6pz) ===
design-editor.js:6282 [h0q6pz] Text object: DESIGN
design-editor.js:6435 [h0q6pz] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [h0q6pz] Number of path shadow steps: 50
design-editor.js:6507 [h0q6pz] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [h0q6pz] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [h0q6pz] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [h0q6pz] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [h0q6pz] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [h0q6pz] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [h0q6pz] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [h0q6pz] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [h0q6pz] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [h0q6pz] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [h0q6pz] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [h0q6pz] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [38sv0t]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -4.583320617675781 -253.75
design-editor.js:7637 Updated point position: (-4.58, -251.25) -> (-4.58, -253.75)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [ci3gym]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [ci3gym]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (wvtbdd) ===
design-editor.js:6282 [wvtbdd] Text object: DESIGN
design-editor.js:6435 [wvtbdd] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [wvtbdd] Number of path shadow steps: 50
design-editor.js:6507 [wvtbdd] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [wvtbdd] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [wvtbdd] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [wvtbdd] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [wvtbdd] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [wvtbdd] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [wvtbdd] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [wvtbdd] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [wvtbdd] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [wvtbdd] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [wvtbdd] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [wvtbdd] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [ci3gym]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -4.583320617675781 -253.75
design-editor.js:7637 Updated point position: (-4.58, -253.75) -> (-4.58, -253.75)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [acniwu]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [acniwu]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (71qsti) ===
design-editor.js:6282 [71qsti] Text object: DESIGN
design-editor.js:6435 [71qsti] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [71qsti] Number of path shadow steps: 50
design-editor.js:6507 [71qsti] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [71qsti] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [71qsti] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [71qsti] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [71qsti] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [71qsti] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [71qsti] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [71qsti] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [71qsti] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [71qsti] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [71qsti] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [71qsti] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [acniwu]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -4.583320617675781 -255
design-editor.js:7637 Updated point position: (-4.58, -253.75) -> (-4.58, -255.00)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [7qvr3y]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [7qvr3y]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (u0f2fv) ===
design-editor.js:6282 [u0f2fv] Text object: DESIGN
design-editor.js:6435 [u0f2fv] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [u0f2fv] Number of path shadow steps: 50
design-editor.js:6507 [u0f2fv] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [u0f2fv] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [u0f2fv] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [u0f2fv] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [u0f2fv] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [u0f2fv] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [u0f2fv] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [u0f2fv] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [u0f2fv] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [u0f2fv] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [u0f2fv] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [u0f2fv] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [7qvr3y]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -4.583320617675781 -256.25
design-editor.js:7637 Updated point position: (-4.58, -255.00) -> (-4.58, -256.25)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [up113f]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [up113f]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (yqnawf) ===
design-editor.js:6282 [yqnawf] Text object: DESIGN
design-editor.js:6435 [yqnawf] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [yqnawf] Number of path shadow steps: 50
design-editor.js:6507 [yqnawf] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [yqnawf] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [yqnawf] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [yqnawf] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [yqnawf] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [yqnawf] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [yqnawf] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [yqnawf] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [yqnawf] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [yqnawf] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [yqnawf] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [yqnawf] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [up113f]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:7607 Dragging grid point: 0 1
design-editor.js:7621 Moving point to local coordinates: -4.583320617675781 -258.75
design-editor.js:7637 Updated point position: (-4.58, -256.25) -> (-4.58, -258.75)
design-editor.js:5492 Stored relative control points for preserving distortion {gridDimensions: {…}, pointCount: 6, relativePointsCount: 6}
design-editor.js:4771 🔍 GRID DISTORT CALL [iy8ndt]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [iy8ndt]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (p667bw) ===
design-editor.js:6282 [p667bw] Text object: DESIGN
design-editor.js:6435 [p667bw] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [p667bw] Number of path shadow steps: 50
design-editor.js:6507 [p667bw] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [p667bw] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [p667bw] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [p667bw] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [p667bw] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [p667bw] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [p667bw] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [p667bw] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [p667bw] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [p667bw] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [p667bw] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [p667bw] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [iy8ndt]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4771 🔍 GRID DISTORT CALL [bzfkpo]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:4870 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:4913 🔍 GRID DISTORT CALL [bzfkpo]: Using main font: Times New Roman_bold_normal - SINGLE RENDER
design-editor.js:5763 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Times New Roman', bold: true, italic: false, …}
design-editor.js:5785 Creating text path with letter spacing: 0
design-editor.js:5834 Text dimensions: {measuredWidth: 733.30078125, measuredHeight: 140, pathWidth: 759.5703125, pathHeight: 138.671875, finalWidth: 733.30078125, …}
design-editor.js:5865 Grid bounds calculation: {gridLeft: -490.31689453125, gridTop: -193.66650390625, gridWidth: 980.6337890625, gridHeight: 387.3330078125, textWidth: 733.30078125, …}
design-editor.js:5876 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6281 === PERSPECTIVE SHADOW START (anlk1u) ===
design-editor.js:6282 [anlk1u] Text object: DESIGN
design-editor.js:6435 [anlk1u] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:6489 [anlk1u] Number of path shadow steps: 50
design-editor.js:6507 [anlk1u] Path shadow step 1/50: scale=0.810
design-editor.js:6507 [anlk1u] Path shadow step 5/50: scale=0.825
design-editor.js:6507 [anlk1u] Path shadow step 10/50: scale=0.845
design-editor.js:6507 [anlk1u] Path shadow step 15/50: scale=0.864
design-editor.js:6507 [anlk1u] Path shadow step 20/50: scale=0.884
design-editor.js:6507 [anlk1u] Path shadow step 25/50: scale=0.903
design-editor.js:6507 [anlk1u] Path shadow step 30/50: scale=0.922
design-editor.js:6507 [anlk1u] Path shadow step 35/50: scale=0.942
design-editor.js:6507 [anlk1u] Path shadow step 40/50: scale=0.961
design-editor.js:6507 [anlk1u] Path shadow step 45/50: scale=0.981
design-editor.js:6507 [anlk1u] Path shadow step 50/50: scale=1.000
design-editor.js:6537 [anlk1u] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:6141 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6143 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:3904 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:3905 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: 2, offsetY: -3, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:4051 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:6162 Grid Distort text rendered with letter spacing: 0
design-editor.js:4923 Grid visibility: true Selected: true
design-editor.js:4925 Drawing grid...
design-editor.js:5289 Drawing grid with 2 rows and 3 columns
design-editor.js:4930 🔍 GRID DISTORT CALL [bzfkpo]: Main font successful - skipping fallback
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0