=== UPDATE OBJECT PROPERTY DEBUG (effectMode) ===
design-editor.js:1208 Updating property 'effectMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'normal', newValue: 'mesh'}
design-editor.js:1271 Property 'effectMode' updated: {oldValue: 'normal', newValue: 'mesh', effectiveValue: 'mesh'}
design-editor.js:1511 Updated body class for effectMode change
design-editor.js:1514 Forcing redraw...
design-editor.js:6901 Mesh effect selected, but no handler found for object: 0
drawTextObject @ design-editor.js:6901
(anonymous) @ design-editor.js:7166
update @ design-editor.js:7165
updateSelectedObjectFromUI @ design-editor.js:1515
effectModeSelect.onchange @ design-editor.js:7860
handleMouseUp_ @ unknownUnderstand this warning
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:2865 Applying perspective shadow in renderStyledObjectToOffscreen
design-editor.js:6203 === PERSPECTIVE SHADOW START (gaauai) ===
design-editor.js:6204 [gaauai] Text object: DESIGN
design-editor.js:6235 [gaauai] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [gaauai] Number of shadow steps: 50
design-editor.js:6288 [gaauai] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [gaauai] Shadow position: (2038.9, 2082.1), scale: 0.810
design-editor.js:6288 [gaauai] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [gaauai] Shadow position: (2039.6, 2079.3), scale: 0.825
design-editor.js:6288 [gaauai] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [gaauai] Shadow position: (2040.5, 2075.8), scale: 0.845
design-editor.js:6288 [gaauai] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [gaauai] Shadow position: (2041.5, 2072.3), scale: 0.864
design-editor.js:6288 [gaauai] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [gaauai] Shadow position: (2042.4, 2068.9), scale: 0.884
design-editor.js:6288 [gaauai] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [gaauai] Shadow position: (2043.3, 2065.4), scale: 0.903
design-editor.js:6288 [gaauai] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [gaauai] Shadow position: (2044.3, 2061.9), scale: 0.922
design-editor.js:6288 [gaauai] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [gaauai] Shadow position: (2045.2, 2058.4), scale: 0.942
design-editor.js:6288 [gaauai] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [gaauai] Shadow position: (2046.1, 2055.0), scale: 0.961
design-editor.js:6288 [gaauai] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [gaauai] Shadow position: (2047.1, 2051.5), scale: 0.981
design-editor.js:6288 [gaauai] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [gaauai] Shadow position: (2048.0, 2048.0), scale: 1.000
design-editor.js:6335 [gaauai] === PERSPECTIVE SHADOW END ===
design-editor.js:3040 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3047 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:3365 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)
design-editor.js:4039 🎨 NORMAL FRONT OUTLINE: Drawing normal text front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:4061 🎨 NORMAL FRONT OUTLINE: Drew normal text front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:7234 Mesh effect selected, but no active handler found for object: 0
update @ design-editor.js:7234
updateSelectedObjectFromUI @ design-editor.js:1515
effectModeSelect.onchange @ design-editor.js:7860
handleMouseUp_ @ unknownUnderstand this warning
design-editor.js:1516 Property 'effectMode' update complete
design-editor.js:1591 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:6901 Mesh effect selected, but no handler found for object: 0
drawTextObject @ design-editor.js:6901
(anonymous) @ design-editor.js:7166
update @ design-editor.js:7165
effectModeSelect.onchange @ design-editor.js:7923
handleMouseUp_ @ unknownUnderstand this warning
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:2865 Applying perspective shadow in renderStyledObjectToOffscreen
design-editor.js:6203 === PERSPECTIVE SHADOW START (u3z71z) ===
design-editor.js:6204 [u3z71z] Text object: DESIGN
design-editor.js:6235 [u3z71z] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [u3z71z] Number of shadow steps: 50
design-editor.js:6288 [u3z71z] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [u3z71z] Shadow position: (2038.9, 2082.1), scale: 0.810
design-editor.js:6288 [u3z71z] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [u3z71z] Shadow position: (2039.6, 2079.3), scale: 0.825
design-editor.js:6288 [u3z71z] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [u3z71z] Shadow position: (2040.5, 2075.8), scale: 0.845
design-editor.js:6288 [u3z71z] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [u3z71z] Shadow position: (2041.5, 2072.3), scale: 0.864
design-editor.js:6288 [u3z71z] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [u3z71z] Shadow position: (2042.4, 2068.9), scale: 0.884
design-editor.js:6288 [u3z71z] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [u3z71z] Shadow position: (2043.3, 2065.4), scale: 0.903
design-editor.js:6288 [u3z71z] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [u3z71z] Shadow position: (2044.3, 2061.9), scale: 0.922
design-editor.js:6288 [u3z71z] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [u3z71z] Shadow position: (2045.2, 2058.4), scale: 0.942
design-editor.js:6288 [u3z71z] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [u3z71z] Shadow position: (2046.1, 2055.0), scale: 0.961
design-editor.js:6288 [u3z71z] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [u3z71z] Shadow position: (2047.1, 2051.5), scale: 0.981
design-editor.js:6288 [u3z71z] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [u3z71z] Shadow position: (2048.0, 2048.0), scale: 1.000
design-editor.js:6335 [u3z71z] === PERSPECTIVE SHADOW END ===
design-editor.js:3040 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3047 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:3365 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)
design-editor.js:4039 🎨 NORMAL FRONT OUTLINE: Drawing normal text front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:4061 🎨 NORMAL FRONT OUTLINE: Drew normal text front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:7234 Mesh effect selected, but no active handler found for object: 0
update @ design-editor.js:7234
effectModeSelect.onchange @ design-editor.js:7923
handleMouseUp_ @ unknownUnderstand this warning
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:196 Synced mesh warp control points to text object for template saving
design-editor.js:6901 Mesh effect selected, but no handler found for object: 0
drawTextObject @ design-editor.js:6901
(anonymous) @ design-editor.js:7166
update @ design-editor.js:7165
initMeshGrid @ mesh-warp-implementation.js:255
MeshWarpHandler @ mesh-warp-implementation.js:31
(anonymous) @ mesh-warp-implementation.js:1097
handleMouseUp_ @ unknownUnderstand this warning
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:2865 Applying perspective shadow in renderStyledObjectToOffscreen
design-editor.js:6203 === PERSPECTIVE SHADOW START (t6bm64) ===
design-editor.js:6204 [t6bm64] Text object: DESIGN
design-editor.js:6235 [t6bm64] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [t6bm64] Number of shadow steps: 50
design-editor.js:6288 [t6bm64] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [t6bm64] Shadow position: (2038.9, 2082.1), scale: 0.810
design-editor.js:6288 [t6bm64] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [t6bm64] Shadow position: (2039.6, 2079.3), scale: 0.825
design-editor.js:6288 [t6bm64] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [t6bm64] Shadow position: (2040.5, 2075.8), scale: 0.845
design-editor.js:6288 [t6bm64] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [t6bm64] Shadow position: (2041.5, 2072.3), scale: 0.864
design-editor.js:6288 [t6bm64] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [t6bm64] Shadow position: (2042.4, 2068.9), scale: 0.884
design-editor.js:6288 [t6bm64] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [t6bm64] Shadow position: (2043.3, 2065.4), scale: 0.903
design-editor.js:6288 [t6bm64] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [t6bm64] Shadow position: (2044.3, 2061.9), scale: 0.922
design-editor.js:6288 [t6bm64] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [t6bm64] Shadow position: (2045.2, 2058.4), scale: 0.942
design-editor.js:6288 [t6bm64] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [t6bm64] Shadow position: (2046.1, 2055.0), scale: 0.961
design-editor.js:6288 [t6bm64] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [t6bm64] Shadow position: (2047.1, 2051.5), scale: 0.981
design-editor.js:6288 [t6bm64] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [t6bm64] Shadow position: (2048.0, 2048.0), scale: 1.000
design-editor.js:6335 [t6bm64] === PERSPECTIVE SHADOW END ===
design-editor.js:3040 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3047 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:3365 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)
design-editor.js:4039 🎨 NORMAL FRONT OUTLINE: Drawing normal text front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:4061 🎨 NORMAL FRONT OUTLINE: Drew normal text front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:7234 Mesh effect selected, but no active handler found for object: 0
update @ design-editor.js:7234
initMeshGrid @ mesh-warp-implementation.js:255
MeshWarpHandler @ mesh-warp-implementation.js:31
(anonymous) @ mesh-warp-implementation.js:1097
handleMouseUp_ @ unknownUnderstand this warning
design-editor.js:6897 [MeshRender] Using active mesh handler for: DESIGN
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (k662s1) ===
design-editor.js:6204 [k662s1] Text object: D
design-editor.js:6235 [k662s1] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [k662s1] Number of shadow steps: 50
design-editor.js:6288 [k662s1] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [k662s1] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [k662s1] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [k662s1] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [k662s1] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [k662s1] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [k662s1] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [k662s1] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [k662s1] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [k662s1] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [k662s1] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [k662s1] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [k662s1] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [k662s1] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [k662s1] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [k662s1] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [k662s1] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [k662s1] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [k662s1] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [k662s1] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [k662s1] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [k662s1] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [k662s1] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (y7ukyw) ===
design-editor.js:6204 [y7ukyw] Text object: E
design-editor.js:6235 [y7ukyw] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [y7ukyw] Number of shadow steps: 50
design-editor.js:6288 [y7ukyw] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [y7ukyw] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [y7ukyw] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [y7ukyw] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [y7ukyw] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [y7ukyw] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [y7ukyw] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [y7ukyw] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [y7ukyw] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [y7ukyw] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [y7ukyw] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [y7ukyw] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [y7ukyw] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [y7ukyw] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [y7ukyw] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [y7ukyw] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [y7ukyw] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [y7ukyw] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [y7ukyw] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [y7ukyw] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [y7ukyw] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [y7ukyw] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [y7ukyw] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (904dmh) ===
design-editor.js:6204 [904dmh] Text object: S
design-editor.js:6235 [904dmh] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [904dmh] Number of shadow steps: 50
design-editor.js:6288 [904dmh] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [904dmh] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [904dmh] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [904dmh] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [904dmh] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [904dmh] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [904dmh] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [904dmh] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [904dmh] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [904dmh] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [904dmh] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [904dmh] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [904dmh] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [904dmh] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [904dmh] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [904dmh] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [904dmh] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [904dmh] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [904dmh] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [904dmh] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [904dmh] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [904dmh] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [904dmh] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (6g1ewh) ===
design-editor.js:6204 [6g1ewh] Text object: I
design-editor.js:6235 [6g1ewh] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [6g1ewh] Number of shadow steps: 50
design-editor.js:6288 [6g1ewh] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [6g1ewh] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [6g1ewh] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [6g1ewh] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [6g1ewh] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [6g1ewh] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [6g1ewh] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [6g1ewh] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [6g1ewh] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [6g1ewh] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [6g1ewh] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [6g1ewh] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [6g1ewh] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [6g1ewh] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [6g1ewh] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [6g1ewh] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [6g1ewh] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [6g1ewh] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [6g1ewh] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [6g1ewh] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [6g1ewh] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [6g1ewh] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [6g1ewh] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (2b2o80) ===
design-editor.js:6204 [2b2o80] Text object: G
design-editor.js:6235 [2b2o80] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [2b2o80] Number of shadow steps: 50
design-editor.js:6288 [2b2o80] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [2b2o80] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [2b2o80] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [2b2o80] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [2b2o80] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [2b2o80] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [2b2o80] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [2b2o80] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [2b2o80] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [2b2o80] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [2b2o80] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [2b2o80] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [2b2o80] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [2b2o80] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [2b2o80] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [2b2o80] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [2b2o80] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [2b2o80] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [2b2o80] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [2b2o80] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [2b2o80] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [2b2o80] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [2b2o80] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (sqgzzc) ===
design-editor.js:6204 [sqgzzc] Text object: N
design-editor.js:6235 [sqgzzc] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [sqgzzc] Number of shadow steps: 50
design-editor.js:6288 [sqgzzc] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [sqgzzc] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [sqgzzc] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [sqgzzc] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [sqgzzc] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [sqgzzc] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [sqgzzc] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [sqgzzc] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [sqgzzc] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [sqgzzc] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [sqgzzc] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [sqgzzc] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [sqgzzc] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [sqgzzc] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [sqgzzc] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [sqgzzc] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [sqgzzc] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [sqgzzc] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [sqgzzc] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [sqgzzc] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [sqgzzc] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [sqgzzc] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [sqgzzc] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:696 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:979 🎨 GRADIENT MASK: Drawing Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:1080 🎨 GRADIENT MASK: Drew Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:9750 📚 State saved to history: Change effectMode Index: 26 Stack size: 27
design-editor.js:7435 Main mouse down: Mesh point hit, preventing object drag.
mesh-warp-implementation.js:352 Mesh point drag started: 8
mesh-warp-implementation.js:196 Synced mesh warp control points to text object for template saving
mesh-warp-implementation.js:163 Stored relative mesh control points for preserving distortion {gridDimensions: {…}, pointCount: 15, relativePointsCount: 15}
design-editor.js:6897 [MeshRender] Using active mesh handler for: DESIGN
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (8fltrr) ===
design-editor.js:6204 [8fltrr] Text object: D
design-editor.js:6235 [8fltrr] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [8fltrr] Number of shadow steps: 50
design-editor.js:6288 [8fltrr] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [8fltrr] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [8fltrr] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [8fltrr] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [8fltrr] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [8fltrr] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [8fltrr] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [8fltrr] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [8fltrr] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [8fltrr] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [8fltrr] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [8fltrr] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [8fltrr] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [8fltrr] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [8fltrr] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [8fltrr] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [8fltrr] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [8fltrr] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [8fltrr] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [8fltrr] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [8fltrr] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [8fltrr] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [8fltrr] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (m0ry3v) ===
design-editor.js:6204 [m0ry3v] Text object: E
design-editor.js:6235 [m0ry3v] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [m0ry3v] Number of shadow steps: 50
design-editor.js:6288 [m0ry3v] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [m0ry3v] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [m0ry3v] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [m0ry3v] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [m0ry3v] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [m0ry3v] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [m0ry3v] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [m0ry3v] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [m0ry3v] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [m0ry3v] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [m0ry3v] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [m0ry3v] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [m0ry3v] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [m0ry3v] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [m0ry3v] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [m0ry3v] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [m0ry3v] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [m0ry3v] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [m0ry3v] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [m0ry3v] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [m0ry3v] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [m0ry3v] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [m0ry3v] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (sz6fq5) ===
design-editor.js:6204 [sz6fq5] Text object: S
design-editor.js:6235 [sz6fq5] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [sz6fq5] Number of shadow steps: 50
design-editor.js:6288 [sz6fq5] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [sz6fq5] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [sz6fq5] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [sz6fq5] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [sz6fq5] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [sz6fq5] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [sz6fq5] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [sz6fq5] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [sz6fq5] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [sz6fq5] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [sz6fq5] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [sz6fq5] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [sz6fq5] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [sz6fq5] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [sz6fq5] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [sz6fq5] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [sz6fq5] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [sz6fq5] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [sz6fq5] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [sz6fq5] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [sz6fq5] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [sz6fq5] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [sz6fq5] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (qi3u8b) ===
design-editor.js:6204 [qi3u8b] Text object: I
design-editor.js:6235 [qi3u8b] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [qi3u8b] Number of shadow steps: 50
design-editor.js:6288 [qi3u8b] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [qi3u8b] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [qi3u8b] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [qi3u8b] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [qi3u8b] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [qi3u8b] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [qi3u8b] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [qi3u8b] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [qi3u8b] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [qi3u8b] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [qi3u8b] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [qi3u8b] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [qi3u8b] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [qi3u8b] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [qi3u8b] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [qi3u8b] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [qi3u8b] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [qi3u8b] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [qi3u8b] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [qi3u8b] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [qi3u8b] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [qi3u8b] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [qi3u8b] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (8dcvk6) ===
design-editor.js:6204 [8dcvk6] Text object: G
design-editor.js:6235 [8dcvk6] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [8dcvk6] Number of shadow steps: 50
design-editor.js:6288 [8dcvk6] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [8dcvk6] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [8dcvk6] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [8dcvk6] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [8dcvk6] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [8dcvk6] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [8dcvk6] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [8dcvk6] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [8dcvk6] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [8dcvk6] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [8dcvk6] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [8dcvk6] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [8dcvk6] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [8dcvk6] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [8dcvk6] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [8dcvk6] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [8dcvk6] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [8dcvk6] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [8dcvk6] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [8dcvk6] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [8dcvk6] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [8dcvk6] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [8dcvk6] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (q00ugl) ===
design-editor.js:6204 [q00ugl] Text object: N
design-editor.js:6235 [q00ugl] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [q00ugl] Number of shadow steps: 50
design-editor.js:6288 [q00ugl] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [q00ugl] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [q00ugl] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [q00ugl] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [q00ugl] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [q00ugl] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [q00ugl] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [q00ugl] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [q00ugl] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [q00ugl] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [q00ugl] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [q00ugl] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [q00ugl] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [q00ugl] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [q00ugl] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [q00ugl] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [q00ugl] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [q00ugl] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [q00ugl] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [q00ugl] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [q00ugl] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [q00ugl] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [q00ugl] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:696 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:979 🎨 GRADIENT MASK: Drawing Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:1080 🎨 GRADIENT MASK: Drew Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:196 Synced mesh warp control points to text object for template saving
mesh-warp-implementation.js:163 Stored relative mesh control points for preserving distortion {gridDimensions: {…}, pointCount: 15, relativePointsCount: 15}
design-editor.js:6897 [MeshRender] Using active mesh handler for: DESIGN
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (qca6di) ===
design-editor.js:6204 [qca6di] Text object: D
design-editor.js:6235 [qca6di] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [qca6di] Number of shadow steps: 50
design-editor.js:6288 [qca6di] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [qca6di] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [qca6di] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [qca6di] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [qca6di] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [qca6di] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [qca6di] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [qca6di] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [qca6di] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [qca6di] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [qca6di] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [qca6di] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [qca6di] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [qca6di] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [qca6di] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [qca6di] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [qca6di] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [qca6di] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [qca6di] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [qca6di] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [qca6di] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [qca6di] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [qca6di] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (wumro4) ===
design-editor.js:6204 [wumro4] Text object: E
design-editor.js:6235 [wumro4] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [wumro4] Number of shadow steps: 50
design-editor.js:6288 [wumro4] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [wumro4] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [wumro4] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [wumro4] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [wumro4] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [wumro4] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [wumro4] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [wumro4] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [wumro4] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [wumro4] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [wumro4] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [wumro4] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [wumro4] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [wumro4] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [wumro4] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [wumro4] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [wumro4] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [wumro4] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [wumro4] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [wumro4] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [wumro4] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [wumro4] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [wumro4] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (2uffs1) ===
design-editor.js:6204 [2uffs1] Text object: S
design-editor.js:6235 [2uffs1] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [2uffs1] Number of shadow steps: 50
design-editor.js:6288 [2uffs1] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [2uffs1] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [2uffs1] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [2uffs1] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [2uffs1] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [2uffs1] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [2uffs1] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [2uffs1] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [2uffs1] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [2uffs1] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [2uffs1] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [2uffs1] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [2uffs1] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [2uffs1] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [2uffs1] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [2uffs1] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [2uffs1] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [2uffs1] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [2uffs1] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [2uffs1] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [2uffs1] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [2uffs1] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [2uffs1] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (9nf9zg) ===
design-editor.js:6204 [9nf9zg] Text object: I
design-editor.js:6235 [9nf9zg] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [9nf9zg] Number of shadow steps: 50
design-editor.js:6288 [9nf9zg] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [9nf9zg] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [9nf9zg] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [9nf9zg] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [9nf9zg] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [9nf9zg] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [9nf9zg] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [9nf9zg] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [9nf9zg] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [9nf9zg] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [9nf9zg] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [9nf9zg] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [9nf9zg] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [9nf9zg] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [9nf9zg] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [9nf9zg] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [9nf9zg] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [9nf9zg] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [9nf9zg] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [9nf9zg] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [9nf9zg] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [9nf9zg] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [9nf9zg] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (yx0f53) ===
design-editor.js:6204 [yx0f53] Text object: G
design-editor.js:6235 [yx0f53] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [yx0f53] Number of shadow steps: 50
design-editor.js:6288 [yx0f53] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [yx0f53] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [yx0f53] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [yx0f53] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [yx0f53] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [yx0f53] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [yx0f53] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [yx0f53] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [yx0f53] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [yx0f53] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [yx0f53] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [yx0f53] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [yx0f53] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [yx0f53] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [yx0f53] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [yx0f53] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [yx0f53] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [yx0f53] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [yx0f53] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [yx0f53] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [yx0f53] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [yx0f53] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [yx0f53] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (whs4rv) ===
design-editor.js:6204 [whs4rv] Text object: N
design-editor.js:6235 [whs4rv] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [whs4rv] Number of shadow steps: 50
design-editor.js:6288 [whs4rv] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [whs4rv] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [whs4rv] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [whs4rv] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [whs4rv] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [whs4rv] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [whs4rv] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [whs4rv] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [whs4rv] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [whs4rv] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [whs4rv] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [whs4rv] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [whs4rv] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [whs4rv] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [whs4rv] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [whs4rv] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [whs4rv] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [whs4rv] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [whs4rv] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [whs4rv] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [whs4rv] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [whs4rv] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [whs4rv] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:696 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:979 🎨 GRADIENT MASK: Drawing Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:1080 🎨 GRADIENT MASK: Drew Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:196 Synced mesh warp control points to text object for template saving
mesh-warp-implementation.js:163 Stored relative mesh control points for preserving distortion {gridDimensions: {…}, pointCount: 15, relativePointsCount: 15}
design-editor.js:6897 [MeshRender] Using active mesh handler for: DESIGN
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (cf6yxi) ===
design-editor.js:6204 [cf6yxi] Text object: D
design-editor.js:6235 [cf6yxi] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [cf6yxi] Number of shadow steps: 50
design-editor.js:6288 [cf6yxi] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [cf6yxi] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [cf6yxi] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [cf6yxi] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [cf6yxi] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [cf6yxi] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [cf6yxi] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [cf6yxi] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [cf6yxi] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [cf6yxi] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [cf6yxi] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [cf6yxi] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [cf6yxi] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [cf6yxi] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [cf6yxi] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [cf6yxi] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [cf6yxi] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [cf6yxi] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [cf6yxi] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [cf6yxi] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [cf6yxi] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [cf6yxi] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [cf6yxi] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (l8cchs) ===
design-editor.js:6204 [l8cchs] Text object: E
design-editor.js:6235 [l8cchs] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [l8cchs] Number of shadow steps: 50
design-editor.js:6288 [l8cchs] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [l8cchs] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [l8cchs] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [l8cchs] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [l8cchs] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [l8cchs] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [l8cchs] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [l8cchs] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [l8cchs] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [l8cchs] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [l8cchs] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [l8cchs] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [l8cchs] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [l8cchs] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [l8cchs] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [l8cchs] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [l8cchs] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [l8cchs] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [l8cchs] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [l8cchs] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [l8cchs] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [l8cchs] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [l8cchs] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (z0sqsd) ===
design-editor.js:6204 [z0sqsd] Text object: S
design-editor.js:6235 [z0sqsd] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [z0sqsd] Number of shadow steps: 50
design-editor.js:6288 [z0sqsd] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [z0sqsd] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [z0sqsd] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [z0sqsd] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [z0sqsd] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [z0sqsd] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [z0sqsd] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [z0sqsd] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [z0sqsd] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [z0sqsd] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [z0sqsd] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [z0sqsd] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [z0sqsd] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [z0sqsd] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [z0sqsd] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [z0sqsd] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [z0sqsd] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [z0sqsd] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [z0sqsd] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [z0sqsd] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [z0sqsd] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [z0sqsd] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [z0sqsd] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (8fqoup) ===
design-editor.js:6204 [8fqoup] Text object: I
design-editor.js:6235 [8fqoup] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [8fqoup] Number of shadow steps: 50
design-editor.js:6288 [8fqoup] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [8fqoup] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [8fqoup] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [8fqoup] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [8fqoup] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [8fqoup] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [8fqoup] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [8fqoup] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [8fqoup] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [8fqoup] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [8fqoup] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [8fqoup] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [8fqoup] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [8fqoup] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [8fqoup] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [8fqoup] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [8fqoup] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [8fqoup] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [8fqoup] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [8fqoup] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [8fqoup] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [8fqoup] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [8fqoup] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (68547y) ===
design-editor.js:6204 [68547y] Text object: G
design-editor.js:6235 [68547y] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [68547y] Number of shadow steps: 50
design-editor.js:6288 [68547y] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [68547y] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [68547y] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [68547y] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [68547y] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [68547y] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [68547y] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [68547y] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [68547y] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [68547y] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [68547y] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [68547y] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [68547y] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [68547y] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [68547y] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [68547y] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [68547y] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [68547y] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [68547y] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [68547y] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [68547y] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [68547y] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [68547y] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (uclu1l) ===
design-editor.js:6204 [uclu1l] Text object: N
design-editor.js:6235 [uclu1l] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [uclu1l] Number of shadow steps: 50
design-editor.js:6288 [uclu1l] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [uclu1l] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [uclu1l] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [uclu1l] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [uclu1l] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [uclu1l] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [uclu1l] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [uclu1l] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [uclu1l] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [uclu1l] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [uclu1l] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [uclu1l] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [uclu1l] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [uclu1l] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [uclu1l] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [uclu1l] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [uclu1l] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [uclu1l] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [uclu1l] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [uclu1l] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [uclu1l] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [uclu1l] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [uclu1l] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:696 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:979 🎨 GRADIENT MASK: Drawing Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:1080 🎨 GRADIENT MASK: Drew Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:196 Synced mesh warp control points to text object for template saving
mesh-warp-implementation.js:163 Stored relative mesh control points for preserving distortion {gridDimensions: {…}, pointCount: 15, relativePointsCount: 15}
design-editor.js:6897 [MeshRender] Using active mesh handler for: DESIGN
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (tbutlw) ===
design-editor.js:6204 [tbutlw] Text object: D
design-editor.js:6235 [tbutlw] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [tbutlw] Number of shadow steps: 50
design-editor.js:6288 [tbutlw] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [tbutlw] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [tbutlw] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [tbutlw] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [tbutlw] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [tbutlw] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [tbutlw] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [tbutlw] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [tbutlw] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [tbutlw] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [tbutlw] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [tbutlw] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [tbutlw] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [tbutlw] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [tbutlw] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [tbutlw] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [tbutlw] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [tbutlw] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [tbutlw] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [tbutlw] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [tbutlw] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [tbutlw] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [tbutlw] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (uzocl4) ===
design-editor.js:6204 [uzocl4] Text object: E
design-editor.js:6235 [uzocl4] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [uzocl4] Number of shadow steps: 50
design-editor.js:6288 [uzocl4] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [uzocl4] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [uzocl4] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [uzocl4] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [uzocl4] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [uzocl4] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [uzocl4] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [uzocl4] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [uzocl4] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [uzocl4] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [uzocl4] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [uzocl4] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [uzocl4] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [uzocl4] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [uzocl4] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [uzocl4] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [uzocl4] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [uzocl4] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [uzocl4] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [uzocl4] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [uzocl4] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [uzocl4] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [uzocl4] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (q7d4a9) ===
design-editor.js:6204 [q7d4a9] Text object: S
design-editor.js:6235 [q7d4a9] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [q7d4a9] Number of shadow steps: 50
design-editor.js:6288 [q7d4a9] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [q7d4a9] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [q7d4a9] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [q7d4a9] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [q7d4a9] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [q7d4a9] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [q7d4a9] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [q7d4a9] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [q7d4a9] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [q7d4a9] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [q7d4a9] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [q7d4a9] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [q7d4a9] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [q7d4a9] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [q7d4a9] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [q7d4a9] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [q7d4a9] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [q7d4a9] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [q7d4a9] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [q7d4a9] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [q7d4a9] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [q7d4a9] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [q7d4a9] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (iqc4h3) ===
design-editor.js:6204 [iqc4h3] Text object: I
design-editor.js:6235 [iqc4h3] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [iqc4h3] Number of shadow steps: 50
design-editor.js:6288 [iqc4h3] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [iqc4h3] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [iqc4h3] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [iqc4h3] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [iqc4h3] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [iqc4h3] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [iqc4h3] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [iqc4h3] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [iqc4h3] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [iqc4h3] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [iqc4h3] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [iqc4h3] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [iqc4h3] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [iqc4h3] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [iqc4h3] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [iqc4h3] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [iqc4h3] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [iqc4h3] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [iqc4h3] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [iqc4h3] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [iqc4h3] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [iqc4h3] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [iqc4h3] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (et2jve) ===
design-editor.js:6204 [et2jve] Text object: G
design-editor.js:6235 [et2jve] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [et2jve] Number of shadow steps: 50
design-editor.js:6288 [et2jve] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [et2jve] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [et2jve] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [et2jve] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [et2jve] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [et2jve] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [et2jve] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [et2jve] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [et2jve] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [et2jve] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [et2jve] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [et2jve] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [et2jve] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [et2jve] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [et2jve] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [et2jve] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [et2jve] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [et2jve] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [et2jve] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [et2jve] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [et2jve] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [et2jve] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [et2jve] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (07yaer) ===
design-editor.js:6204 [07yaer] Text object: N
design-editor.js:6235 [07yaer] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [07yaer] Number of shadow steps: 50
design-editor.js:6288 [07yaer] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [07yaer] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [07yaer] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [07yaer] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [07yaer] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [07yaer] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [07yaer] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [07yaer] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [07yaer] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [07yaer] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [07yaer] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [07yaer] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [07yaer] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [07yaer] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [07yaer] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [07yaer] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [07yaer] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [07yaer] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [07yaer] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [07yaer] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [07yaer] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [07yaer] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [07yaer] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:696 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:979 🎨 GRADIENT MASK: Drawing Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:1080 🎨 GRADIENT MASK: Drew Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:196 Synced mesh warp control points to text object for template saving
mesh-warp-implementation.js:163 Stored relative mesh control points for preserving distortion {gridDimensions: {…}, pointCount: 15, relativePointsCount: 15}
design-editor.js:6897 [MeshRender] Using active mesh handler for: DESIGN
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (99fvty) ===
design-editor.js:6204 [99fvty] Text object: D
design-editor.js:6235 [99fvty] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [99fvty] Number of shadow steps: 50
design-editor.js:6288 [99fvty] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [99fvty] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [99fvty] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [99fvty] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [99fvty] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [99fvty] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [99fvty] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [99fvty] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [99fvty] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [99fvty] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [99fvty] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [99fvty] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [99fvty] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [99fvty] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [99fvty] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [99fvty] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [99fvty] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [99fvty] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [99fvty] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [99fvty] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [99fvty] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [99fvty] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [99fvty] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (gp5ode) ===
design-editor.js:6204 [gp5ode] Text object: E
design-editor.js:6235 [gp5ode] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [gp5ode] Number of shadow steps: 50
design-editor.js:6288 [gp5ode] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [gp5ode] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [gp5ode] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [gp5ode] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [gp5ode] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [gp5ode] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [gp5ode] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [gp5ode] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [gp5ode] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [gp5ode] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [gp5ode] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [gp5ode] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [gp5ode] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [gp5ode] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [gp5ode] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [gp5ode] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [gp5ode] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [gp5ode] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [gp5ode] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [gp5ode] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [gp5ode] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [gp5ode] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [gp5ode] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (d9e93s) ===
design-editor.js:6204 [d9e93s] Text object: S
design-editor.js:6235 [d9e93s] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [d9e93s] Number of shadow steps: 50
design-editor.js:6288 [d9e93s] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [d9e93s] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [d9e93s] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [d9e93s] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [d9e93s] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [d9e93s] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [d9e93s] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [d9e93s] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [d9e93s] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [d9e93s] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [d9e93s] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [d9e93s] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [d9e93s] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [d9e93s] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [d9e93s] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [d9e93s] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [d9e93s] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [d9e93s] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [d9e93s] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [d9e93s] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [d9e93s] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [d9e93s] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [d9e93s] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (q9g0wc) ===
design-editor.js:6204 [q9g0wc] Text object: I
design-editor.js:6235 [q9g0wc] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [q9g0wc] Number of shadow steps: 50
design-editor.js:6288 [q9g0wc] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [q9g0wc] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [q9g0wc] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [q9g0wc] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [q9g0wc] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [q9g0wc] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [q9g0wc] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [q9g0wc] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [q9g0wc] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [q9g0wc] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [q9g0wc] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [q9g0wc] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [q9g0wc] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [q9g0wc] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [q9g0wc] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [q9g0wc] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [q9g0wc] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [q9g0wc] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [q9g0wc] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [q9g0wc] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [q9g0wc] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [q9g0wc] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [q9g0wc] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (evltmc) ===
design-editor.js:6204 [evltmc] Text object: G
design-editor.js:6235 [evltmc] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [evltmc] Number of shadow steps: 50
design-editor.js:6288 [evltmc] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [evltmc] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [evltmc] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [evltmc] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [evltmc] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [evltmc] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [evltmc] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [evltmc] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [evltmc] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [evltmc] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [evltmc] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [evltmc] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [evltmc] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [evltmc] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [evltmc] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [evltmc] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [evltmc] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [evltmc] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [evltmc] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [evltmc] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [evltmc] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [evltmc] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [evltmc] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:618 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6203 === PERSPECTIVE SHADOW START (ag4n49) ===
design-editor.js:6204 [ag4n49] Text object: N
design-editor.js:6235 [ag4n49] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:6268 [ag4n49] Number of shadow steps: 50
design-editor.js:6288 [ag4n49] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:6302 [ag4n49] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:6288 [ag4n49] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:6302 [ag4n49] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:6288 [ag4n49] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:6302 [ag4n49] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:6288 [ag4n49] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:6302 [ag4n49] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:6288 [ag4n49] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:6302 [ag4n49] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:6288 [ag4n49] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:6302 [ag4n49] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:6288 [ag4n49] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:6302 [ag4n49] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:6288 [ag4n49] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:6302 [ag4n49] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:6288 [ag4n49] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:6302 [ag4n49] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:6288 [ag4n49] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:6302 [ag4n49] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:6288 [ag4n49] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6302 [ag4n49] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:6335 [ag4n49] === PERSPECTIVE SHADOW END ===
design-editor.js:3200 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:696 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:979 🎨 GRADIENT MASK: Drawing Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:1080 🎨 GRADIENT MASK: Drew Mesh Warp front outline
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:609 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:610 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:622 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:637 Setting text context with font: bold 200px Poppins letter spacing: 0
mesh-warp-implementation.js:393 Mesh point drag ended
mesh-warp-implementation.js:196 Synced mesh warp control points to text object for template saving
mesh-warp-implementation.js:163 Stored relative mesh control points for preserving distortion {gridDimensions: {…}, pointCount: 15, relativePointsCount: 15}