=== UPDATE OBJECT PROPERTY DEBUG (decorationMode) ===
design-editor.js:1272 Updating property 'decorationMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'noDecoration', newValue: 'horizontalLines'}
design-editor.js:1335 Property 'decorationMode' updated: {oldValue: 'noDecoration', newValue: 'horizontalLines', effectiveValue: 'horizontalLines'}
design-editor.js:1575 Updated body class for decorationMode change
design-editor.js:1578 Forcing redraw...
design-editor.js:7517 [MeshRender] Using active mesh handler for: DESIGN
mesh-warp-implementation.js:718 🎨 GRADIENT MASK: Drawing mesh warp text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:825 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Mesh Warp
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
mesh-warp-implementation.js:848 🎨 DECORATION: Applied decoration pattern for mesh warp text: horizontalLines
design-editor.js:4287 🎨 MASK: Creating mesh warp text mask for decorations
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:4316 🎨 MASK: Created mesh warp text mask
mesh-warp-implementation.js:877 🎨 DECORATION: Applied decoration effects on top of mesh warp gradient text
mesh-warp-implementation.js:881 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp
mesh-warp-implementation.js:909 🎨 GRADIENT MASK: Mesh warp text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1580 Property 'decorationMode' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10425 📚 State saved to history: Change decorationMode Index: 10 Stack size: 11