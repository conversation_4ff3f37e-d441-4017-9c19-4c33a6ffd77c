🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:366 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:350 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:366 🎨 Generated 2 color stops in radialColorStops
gradient-color-picker.js:350 🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:366 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:350 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:366 🎨 Generated 2 color stops in radialColorStops
design-editor.js:2092 🎨 Text color changed: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:2100 🎨 Applying gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:2101 🎨 Selected object index: 0
design-editor.js:2102 🎨 Selected object: {id: 0, type: 'text', text: 'DESIGN', x: 1200.25, y: 641.5, …}
design-editor.js:1267 === UPDATE OBJECT PROPERTY DEBUG (gradient) ===
design-editor.js:1276 Updating property 'gradient' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: null, newValue: {…}}
design-editor.js:1339 Property 'gradient' updated: {oldValue: null, newValue: {…}, effectiveValue: {…}}
design-editor.js:1582 Forcing redraw...
design-editor.js:7630 [MeshRender] Using active mesh handler for: DESIGN
mesh-warp-implementation.js:719 🎨 GRADIENT MASK: Drawing mesh warp text with gradient mask
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:695 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:695 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:826 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Mesh Warp
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
mesh-warp-implementation.js:849 🎨 DECORATION: Applied decoration pattern for mesh warp text: horizontalLines
design-editor.js:4396 🎨 MASK: Creating mesh warp text mask for decorations
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:695 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:4425 🎨 MASK: Created mesh warp text mask
mesh-warp-implementation.js:878 🎨 DECORATION: Applied decoration effects on top of mesh warp gradient text
mesh-warp-implementation.js:882 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp
mesh-warp-implementation.js:910 🎨 GRADIENT MASK: Mesh warp text with gradient mask complete
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1584 Property 'gradient' update complete
design-editor.js:1659 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1267 === UPDATE OBJECT PROPERTY DEBUG (color) ===
design-editor.js:1276 Updating property 'color' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: '#3b82f6', newValue: '#3b82f6'}
design-editor.js:1339 Property 'color' updated: {oldValue: '#3b82f6', newValue: '#3b82f6', effectiveValue: '#3b82f6'}
design-editor.js:1582 Forcing redraw...
design-editor.js:7630 [MeshRender] Using active mesh handler for: DESIGN
mesh-warp-implementation.js:719 🎨 GRADIENT MASK: Drawing mesh warp text with gradient mask
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:695 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:695 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:826 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Mesh Warp
decoration-module.js:230 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 240.0px
mesh-warp-implementation.js:849 🎨 DECORATION: Applied decoration pattern for mesh warp text: horizontalLines
design-editor.js:4396 🎨 MASK: Creating mesh warp text mask for decorations
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:695 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:4425 🎨 MASK: Created mesh warp text mask
mesh-warp-implementation.js:878 🎨 DECORATION: Applied decoration effects on top of mesh warp gradient text
mesh-warp-implementation.js:882 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp
mesh-warp-implementation.js:910 🎨 GRADIENT MASK: Mesh warp text with gradient mask complete
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1584 Property 'color' update complete
design-editor.js:1659 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10619 📚 State saved to history: Change gradient Index: 6 Stack 