🎨 setTextContextOn - textObj.gradient: null
design-editor.js:659 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:671 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:686 Setting text context with font: 200px Arial letter spacing: 0
design-editor.js:658 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:659 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:671 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:686 Setting text context with font: 200px Arial letter spacing: 0
design-editor.js:658 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:659 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:671 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:686 Setting text context with font: 200px Arial letter spacing: 0
design-editor.js:3095 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3102 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:658 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:659 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:671 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:686 Setting text context with font: 200px Arial letter spacing: 0
design-editor.js:658 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:659 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:671 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:686 Setting text context with font: 200px Arial letter spacing: 0
design-editor.js:658 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:659 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:671 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:686 Setting text context with font: 200px Arial letter spacing: 0
design-editor.js:3095 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3102 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:658 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:659 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:671 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:686 Setting text context with font: 200px Arial letter spacing: 0
design-editor.js:658 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:659 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:671 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:686 Setting text context with font: 200px Arial letter spacing: 0
design-editor.js:658 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:659 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:671 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:686 Setting text context with font: 200px Arial letter spacing: 0
design-editor.js:3095 🎨 About to draw text w