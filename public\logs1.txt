=== UPDATE OBJECT PROPERTY DEBUG (decorationMode) ===
design-editor.js:1268 Updating property 'decorationMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'noDecoration', newValue: 'horizontalLines'}
design-editor.js:1331 Property 'decorationMode' updated: {oldValue: 'noDecoration', newValue: 'horizontalLines', effectiveValue: 'horizontalLines'}
design-editor.js:1571 Updated body class for decorationMode change
design-editor.js:1574 Forcing redraw...
design-editor.js:3683 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:663 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:664 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:691 Setting text context with font: 200px Georgia-bold letter spacing: 0
design-editor.js:3100 🎨 About to draw text with fillStyle: object CanvasPattern {}
design-editor.js:3107 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:663 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:664 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:691 Setting text context with font: 200px Georgia-bold letter spacing: 0
design-editor.js:3807 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3811 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3815 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3841 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:3864 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:663 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:664 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:691 Setting text context with font: 200px Georgia-bold letter spacing: 0
design-editor.js:663 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:664 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:691 Setting text context with font: 200px Georgia-bold letter spacing: 0
design-editor.js:1576 Property 'decorationMode' update complete
design-editor.js:1651 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10027 📚 State saved to history: Change decorationMode Index: 14 Stack size: 15