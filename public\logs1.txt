=== UPDATE OBJECT PROPERTY DEBUG (decorationMode) ===
design-editor.js:1276 Updating property 'decorationMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'noDecoration', newValue: 'horizontalLines'}
design-editor.js:1339 Property 'decorationMode' updated: {oldValue: 'noDecoration', newValue: 'horizontalLines', effectiveValue: 'horizontalLines'}
design-editor.js:1579 Updated body class for decorationMode change
design-editor.js:1582 Forcing redraw...
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3268 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:4787 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Circular text (no gradient)
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:671 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:672 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:684 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:699 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:1584 Property 'decorationMode' update complete
design-editor.js:1659 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10614 📚 State saved to history: Change decorationMode Index: 7 Stack size: 8